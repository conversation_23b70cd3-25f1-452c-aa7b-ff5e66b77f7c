"username" = "ユーザー名"
"password" = "パスワード"
"login" = "ログイン"
"confirm" = "確認"
"cancel" = "キャンセル"
"close" = "閉じる"
"create" = "作成"
"update" = "更新"
"copy" = "コピー"
"copied" = "コピー済み"
"download" = "ダウンロード"
"remark" = "備考"
"enable" = "有効化"
"protocol" = "プロトコル"
"search" = "検索"
"filter" = "フィルター"
"loading" = "読み込み中..."
"second" = "秒"
"minute" = "分"
"hour" = "時間"
"day" = "日"
"check" = "確認"
"indefinite" = "無期限"
"unlimited" = "無制限"
"none" = "なし"
"qrCode" = "QRコード"
"info" = "詳細情報"
"edit" = "編集"
"delete" = "削除"
"reset" = "リセット"
"noData" = "データなし。"
"copySuccess" = "コピー成功"
"sure" = "確定"
"encryption" = "暗号化"
"useIPv4ForHost" = "ホストにIPv4を使用"
"transmission" = "伝送"
"host" = "ホスト"
"path" = "パス"
"camouflage" = "偽装"
"status" = "ステータス"
"enabled" = "有効"
"disabled" = "無効"
"depleted" = "消耗済み"
"depletingSoon" = "間もなく消耗"
"offline" = "オフライン"
"online" = "オンライン"
"domainName" = "ドメイン名"
"monitor" = "監視"
"certificate" = "証明書"
"fail" = "失敗"
"comment" = "コメント"
"success" = "成功"
"getVersion" = "バージョン取得"
"install" = "インストール"
"clients" = "クライアント"
"usage" = "利用状況"
"twoFactorCode" = "コード"
"remained" = "残り"
"security" = "セキュリティ"
"secAlertTitle" = "セキュリティアラート"
"secAlertSsl" = "この接続は安全ではありません。TLSを有効にしてデータ保護を行うまで、機密情報を入力しないでください。"
"secAlertConf" = "一部の設定は脆弱です。潜在的な脆弱性を防ぐために、セキュリティプロトコルを強化することをお勧めします。"
"secAlertSSL" = "セキュアな接続がありません。データ保護のためにTLS証明書をインストールしてください。"
"secAlertPanelPort" = "デフォルトのポートにはセキュリティリスクがあります。ランダムなポートまたは特定のポートを設定してください。"
"secAlertPanelURI" = "デフォルトのURIパスは安全ではありません。複雑なURIパスを設定してください。"
"secAlertSubURI" = "サブスクリプションのデフォルトURIパスは安全ではありません。複雑なURIパスを設定してください。"
"secAlertSubJsonURI" = "JSONサブスクリプションのデフォルトURIパスは安全ではありません。複雑なURIパスを設定してください。"
"emptyDnsDesc" = "追加されたDNSサーバーはありません。"
"emptyFakeDnsDesc" = "追加されたFake DNSサーバーはありません。"
"emptyBalancersDesc" = "追加されたバランサーはありません。"
"emptyReverseDesc" = "追加されたリバースプロキシはありません。"
"somethingWentWrong" = "エラーが発生しました"

[menu]
"theme" = "テーマ"
"dark" = "ダーク"
"ultraDark" = "ウルトラダーク"
"dashboard" = "ダッシュボード"
"inbounds" = "インバウンド一覧"
"settings" = "パネル設定"
"xray" = "Xray設定"
"logout" = "ログアウト"
"link" = "リンク管理"

[pages.login]
"hello" = "こんにちは"
"title" = "ようこそ"
"loginAgain" = "ログインセッションが切れました。再度ログインしてください。"

[pages.login.toasts]
"invalidFormData" = "データ形式エラー"
"emptyUsername" = "ユーザー名を入力してください"
"emptyPassword" = "パスワードを入力してください"
"wrongUsernameOrPassword" = "ユーザー名、パスワード、または二段階認証コードが無効です。"  
"successLogin" = "アカウントに正常にログインしました。"

[pages.index]
"title" = "システムステータス"
"cpu" = "CPU"
"logicalProcessors" = "論理プロセッサ"
"frequency" = "周波数"
"swap" = "スワップ"
"storage" = "ストレージ"
"memory" = "RAM"
"threads" = "スレッド"
"xrayStatus" = "Xray"
"stopXray" = "停止"
"restartXray" = "再起動"
"xraySwitch" = "バージョン"
"xraySwitchClick" = "切り替えるバージョンを選択してください"
"xraySwitchClickDesk" = "慎重に選択してください。古いバージョンは現在の設定と互換性がない可能性があります。"
"xrayStatusUnknown" = "不明"
"xrayStatusRunning" = "実行中"
"xrayStatusStop" = "停止"
"xrayStatusError" = "エラー"
"xrayErrorPopoverTitle" = "Xrayの実行中にエラーが発生しました"
"operationHours" = "システム稼働時間"
"systemLoad" = "システム負荷"
"systemLoadDesc" = "過去1、5、15分間のシステム平均負荷"
"connectionTcpCountDesc" = "システム内のすべてのTCP接続数"
"connectionUdpCountDesc" = "システム内のすべてのUDP接続数"
"connectionCount" = "接続数"
"ipAddresses" = "IPアドレス"
"toggleIpVisibility" = "IPの表示を切り替える"
"overallSpeed" = "全体の速度"
"upload" = "アップロード"
"download" = "ダウンロード"
"totalData" = "総データ量"
"sent" = "送信"
"received" = "受信"
"documentation" = "ドキュメント"
"xraySwitchVersionDialog" = "Xrayのバージョンを本当に変更しますか？"
"xraySwitchVersionDialogDesc" = "Xrayのバージョンが#version#に変更されます。"
"xraySwitchVersionPopover" = "Xrayの更新が成功しました"
"geofileUpdateDialog" = "ジオファイルを本当に更新しますか？"
"geofileUpdateDialogDesc" = "これにより#filename#ファイルが更新されます。"
"geofileUpdatePopover" = "ジオファイルの更新が成功しました"
"dontRefresh" = "インストール中、このページをリロードしないでください"
"logs" = "ログ"
"config" = "設定"
"backup" = "バックアップ"
"backupTitle" = "データベースのバックアップと復元"
"exportDatabase" = "バックアップ"
"exportDatabaseDesc" = "クリックして、現在のデータベースのバックアップを含む .db ファイルをデバイスにダウンロードします。"
"importDatabase" = "復元"
"importDatabaseDesc" = "クリックして、デバイスから .db ファイルを選択し、アップロードしてバックアップからデータベースを復元します。"
"importDatabaseSuccess" = "データベースのインポートに成功しました"
"importDatabaseError" = "データベースのインポート中にエラーが発生しました"
"readDatabaseError" = "データベースの読み取り中にエラーが発生しました"
"getDatabaseError" = "データベースの取得中にエラーが発生しました"
"getConfigError" = "設定ファイルの取得中にエラーが発生しました"

[pages.inbounds]
"title" = "インバウンド一覧"
"totalDownUp" = "総アップロード / ダウンロード"
"totalUsage" = "総使用量"
"inboundCount" = "インバウンド数"
"operate" = "メニュー"
"enable" = "有効化"
"remark" = "備考"
"protocol" = "プロトコル"
"port" = "ポート"
"traffic" = "トラフィック"
"details" = "詳細情報"
"transportConfig" = "トランスポート設定"
"expireDate" = "有効期限"
"resetTraffic" = "トラフィックリセット"
"addInbound" = "インバウンド追加"
"generalActions" = "一般操作"
"autoRefresh" = "自動更新"
"autoRefreshInterval" = "間隔"
"modifyInbound" = "インバウンド修正"
"deleteInbound" = "インバウンド削除"
"deleteInboundContent" = "インバウンドを削除してもよろしいですか？"
"deleteClient" = "クライアント削除"
"deleteClientContent" = "クライアントを削除してもよろしいですか？"
"resetTrafficContent" = "トラフィックをリセットしてもよろしいですか？"
"inboundUpdateSuccess" = "インバウンドが正常に更新されました。"
"inboundCreateSuccess" = "インバウンドが正常に作成されました。"
"copyLink" = "リンクをコピー"
"address" = "アドレス"
"network" = "ネットワーク"
"destinationPort" = "宛先ポート"
"targetAddress" = "宛先アドレス"
"monitorDesc" = "空白にするとすべてのIPを監視"
"meansNoLimit" = "= 無制限（単位：GB）"
"totalFlow" = "総トラフィック"
"leaveBlankToNeverExpire" = "空白にすると期限なし"
"noRecommendKeepDefault" = "デフォルト値を保持することをお勧めします"
"certificatePath" = "ファイルパス"
"certificateContent" = "ファイル内容"
"publicKey" = "公開鍵"
"privatekey" = "秘密鍵"
"clickOnQRcode" = "QRコードをクリックしてコピー"
"client" = "クライアント"
"export" = "リンクエクスポート"
"clone" = "複製"
"cloneInbound" = "複製"
"cloneInboundContent" = "このインバウンドルールは、ポート（Port）、リスニングIP（Listening IP）、クライアント（Clients）を除くすべての設定がクローンされます"
"cloneInboundOk" = "クローン作成"
"resetAllTraffic" = "すべてのインバウンドトラフィックをリセット"
"resetAllTrafficTitle" = "すべてのインバウンドトラフィックをリセット"
"resetAllTrafficContent" = "すべてのインバウンドトラフィックをリセットしてもよろしいですか？"
"resetInboundClientTraffics" = "クライアントトラフィックをリセット"
"resetInboundClientTrafficTitle" = "すべてのクライアントトラフィックをリセット"
"resetInboundClientTrafficContent" = "このインバウンドクライアントのすべてのトラフィックをリセットしてもよろしいですか？"
"resetAllClientTraffics" = "すべてのクライアントトラフィックをリセット"
"resetAllClientTrafficTitle" = "すべてのクライアントトラフィックをリセット"
"resetAllClientTrafficContent" = "すべてのクライアントのトラフィックをリセットしてもよろしいですか？"
"delDepletedClients" = "トラフィックが尽きたクライアントを削除"
"delDepletedClientsTitle" = "トラフィックが尽きたクライアントを削除"
"delDepletedClientsContent" = "トラフィックが尽きたすべてのクライアントを削除してもよろしいですか？"
"email" = "メールアドレス"
"emailDesc" = "メールアドレスは一意でなければなりません"
"IPLimit" = "IP制限"
"IPLimitDesc" = "設定値を超えるとインバウンドトラフィックが無効になります。（0 = 無効）"
"IPLimitlog" = "IPログ"
"IPLimitlogDesc" = "IP履歴ログ（無効なインバウンドトラフィックを有効にするには、ログをクリアしてください）"
"IPLimitlogclear" = "ログをクリア"
"setDefaultCert" = "パネル設定から証明書を設定"
"telegramDesc" = "TelegramチャットIDを提供してください。（ボットで'/id'コマンドを使用）または（@userinfobot）"
"subscriptionDesc" = "サブスクリプションURLを見つけるには、“詳細情報”に移動してください。また、複数のクライアントに同じ名前を使用することができます。"
"info" = "情報"
"same" = "同じ"
"inboundData" = "インバウンドデータ"
"exportInbound" = "インバウンドルールをエクスポート"
"import" = "インポート"
"importInbound" = "インバウンドルールをインポート"

[pages.client]
"add" = "クライアント追加"
"edit" = "クライアント編集"
"submitAdd" = "クライアント追加"
"submitEdit" = "変更を保存"
"clientCount" = "クライアント数"
"bulk" = "一括作成"
"method" = "方法"
"first" = "最初"
"last" = "最後"
"prefix" = "プレフィックス"
"postfix" = "サフィックス"
"delayedStart" = "初回使用後に開始"
"expireDays" = "期間"
"days" = "日"
"renew" = "自動更新"
"renewDesc" = "期限が切れた後に自動更新。（0 = 無効）（単位：日）"

[pages.inbounds.toasts]
"obtain" = "取得"
"updateSuccess" = "更新が成功しました"
"logCleanSuccess" = "ログがクリアされました"
"inboundsUpdateSuccess" = "インバウンドが正常に更新されました"
"inboundUpdateSuccess" = "インバウンドが正常に更新されました"
"inboundCreateSuccess" = "インバウンドが正常に作成されました"
"inboundDeleteSuccess" = "インバウンドが正常に削除されました"
"inboundClientAddSuccess" = "インバウンドクライアントが追加されました"
"inboundClientDeleteSuccess" = "インバウンドクライアントが削除されました"
"inboundClientUpdateSuccess" = "インバウンドクライアントが更新されました"
"delDepletedClientsSuccess" = "すべての枯渇したクライアントが削除されました"
"resetAllClientTrafficSuccess" = "クライアントのすべてのトラフィックがリセットされました"
"resetAllTrafficSuccess" = "すべてのトラフィックがリセットされました"
"resetInboundClientTrafficSuccess" = "トラフィックがリセットされました"
"trafficGetError" = "トラフィックの取得中にエラーが発生しました"
"getNewX25519CertError" = "X25519証明書の取得中にエラーが発生しました。"

[pages.inbounds.stream.general]
"request" = "リクエスト"
"response" = "レスポンス"
"name" = "名前"
"value" = "値"

[pages.inbounds.stream.tcp]
"version" = "バージョン"
"method" = "方法"
"path" = "パス"
"status" = "ステータス"
"statusDescription" = "ステータス説明"
"requestHeader" = "リクエストヘッダー"
"responseHeader" = "レスポンスヘッダー"

[pages.settings]
"title" = "パネル設定"
"save" = "保存"
"infoDesc" = "ここでのすべての変更は、保存してパネルを再起動する必要があります"
"restartPanel" = "パネル再起動"
"restartPanelDesc" = "パネルを再起動してもよろしいですか？再起動後にパネルにアクセスできない場合は、サーバーでパネルログを確認してください"
"restartPanelSuccess" = "パネルの再起動に成功しました"
"actions" = "操作"
"resetDefaultConfig" = "デフォルト設定にリセット"
"panelSettings" = "一般"
"securitySettings" = "セキュリティ設定"
"TGBotSettings" = "Telegramボット設定"
"panelListeningIP" = "パネル監視IP"
"panelListeningIPDesc" = "デフォルトではすべてのIPを監視する"
"panelListeningDomain" = "パネル監視ドメイン"
"panelListeningDomainDesc" = "デフォルトで空白の場合、すべてのドメインとIPアドレスを監視する"
"panelPort" = "パネル監視ポート"
"panelPortDesc" = "再起動で有効"
"publicKeyPath" = "パネル証明書公開鍵ファイルパス"
"publicKeyPathDesc" = "'/'で始まる絶対パスを入力"
"privateKeyPath" = "パネル証明書秘密鍵ファイルパス"
"privateKeyPathDesc" = "'/'で始まる絶対パスを入力"
"panelUrlPath" = "パネルURLルートパス"
"panelUrlPathDesc" = "'/'で始まり、'/'で終わる必要があります"
"pageSize" = "ページサイズ"
"pageSizeDesc" = "インバウンドテーブルのページサイズを定義します。0を設定すると無効化されます"
"remarkModel" = "備考モデルと区切り記号"
"datepicker" = "日付ピッカー"
"datepickerPlaceholder" = "日付を選択"
"datepickerDescription" = "日付選択カレンダーで有効期限を指定する"
"sampleRemark" = "備考の例"
"oldUsername" = "旧ユーザー名"
"currentPassword" = "旧パスワード"
"newUsername" = "新しいユーザー名"
"newPassword" = "新しいパスワード"
"telegramBotEnable" = "Telegramボットを有効にする"
"telegramBotEnableDesc" = "Telegramボット機能を有効にする"
"telegramToken" = "Telegramボットトークン"
"telegramTokenDesc" = "'@BotFather'から取得したTelegramボットトークン"
"telegramProxy" = "SOCKS5プロキシ"
"telegramProxyDesc" = "SOCKS5プロキシを有効にしてTelegramに接続する（ガイドに従って設定を調整）"
"telegramAPIServer" = "Telegram APIサーバー"
"telegramAPIServerDesc" = "使用するTelegram APIサーバー。空白の場合はデフォルトサーバーを使用する"
"telegramChatId" = "管理者チャットID"
"telegramChatIdDesc" = "Telegram管理者チャットID（複数の場合はカンマで区切る）@userinfobotで取得するか、ボットで'/id'コマンドを使用して取得する"
"telegramNotifyTime" = "通知時間"
"telegramNotifyTimeDesc" = "定期的なTelegramボット通知時間を設定する（crontab時間形式を使用）"
"tgNotifyBackup" = "データベースバックアップ"
"tgNotifyBackupDesc" = "レポート付きのデータベースバックアップファイルを送信"
"tgNotifyLogin" = "ログイン通知"
"tgNotifyLoginDesc" = "誰かがパネルにログインしようとしたときに、ユーザー名、IPアドレス、時間を表示する"
"sessionMaxAge" = "セッション期間"
"sessionMaxAgeDesc" = "ログイン状態を保持する期間（単位：分）"
"expireTimeDiff" = "有効期限通知のしきい値"
"expireTimeDiffDesc" = "このしきい値に達した場合、有効期限に関する通知を受け取る（単位：日）"
"trafficDiff" = "トラフィック消耗しきい値"
"trafficDiffDesc" = "このしきい値に達した場合、トラフィック消耗に関する通知を受け取る（単位：GB）"
"tgNotifyCpu" = "CPU負荷通知しきい値"
"tgNotifyCpuDesc" = "CPU負荷がこのしきい値を超えた場合、通知を受け取る（単位：%）"
"timeZone" = "タイムゾーン"
"timeZoneDesc" = "定時タスクはこのタイムゾーンの時間に従って実行される"
"subSettings" = "サブスクリプション設定"
"subEnable" = "サブスクリプションサービスを有効にする"
"subEnableDesc" = "サブスクリプションサービス機能を有効にする"
"subTitle" = "サブスクリプションタイトル"
"subTitleDesc" = "VPNクライアントに表示されるタイトル"
"subListen" = "監視IP"
"subListenDesc" = "サブスクリプションサービスが監視するIPアドレス（空白にするとすべてのIPを監視）"
"subPort" = "監視ポート"
"subPortDesc" = "サブスクリプションサービスが監視するポート番号（使用されていないポートである必要があります）"
"subCertPath" = "公開鍵パス"
"subCertPathDesc" = "サブスクリプションサービスで使用する公開鍵ファイルのパス（'/'で始まる）"
"subKeyPath" = "秘密鍵パス"
"subKeyPathDesc" = "サブスクリプションサービスで使用する秘密鍵ファイルのパス（'/'で始まる）"
"subPath" = "URIパス"
"subPathDesc" = "サブスクリプションサービスで使用するURIパス（'/'で始まり、'/'で終わる）"
"subDomain" = "監視ドメイン"
"subDomainDesc" = "サブスクリプションサービスが監視するドメイン（空白にするとすべてのドメインとIPを監視）"
"subUpdates" = "更新間隔"
"subUpdatesDesc" = "クライアントアプリケーションでサブスクリプションURLの更新間隔（単位：時間）"
"subEncrypt" = "エンコード"
"subEncryptDesc" = "サブスクリプションサービスが返す内容をBase64エンコードする"
"subShowInfo" = "利用情報を表示"
"subShowInfoDesc" = "クライアントアプリで残りのトラフィックと日付情報を表示する"
"subURI" = "リバースプロキシURI"
"subURIDesc" = "プロキシ後ろのサブスクリプションURLのURIパスに使用する"
"externalTrafficInformEnable" = "外部トラフィック情報"
"externalTrafficInformEnableDesc" = "トラフィックの更新ごとに外部 API に通知します。"
"externalTrafficInformURI" = "外部トラフィック通知 URI"
"externalTrafficInformURIDesc" = "トラフィックの更新ごとに外部 API に通知します。"
"fragment" = "フラグメント"
"fragmentDesc" = "TLS helloパケットのフラグメントを有効にする"
"fragmentSett" = "設定"
"noisesDesc" = "Noisesを有効にする"
"noisesSett" = "Noises設定"
"mux" = "マルチプレクサ"
"muxDesc" = "確立されたストリーム内で複数の独立したストリームを伝送する"
"muxSett" = "マルチプレクサ設定"
"direct" = "直接接続"
"directDesc" = "特定の国のドメインまたはIP範囲に直接接続する"
"notifications" = "通知"
"certs" = "証明書"
"externalTraffic" = "外部トラフィック"
"dateAndTime" = "日付と時刻"
"proxyAndServer" = "プロキシとサーバー"
"intervals" = "間隔"
"information" = "情報"
"language" = "言語"
"telegramBotLanguage" = "Telegram Botの言語"

[pages.xray]
"title" = "Xray 設定"
"save" = "保存"
"restart" = "Xray 再起動"
"restartSuccess" = "Xrayの再起動に成功しました"
"stopSuccess" = "Xrayが正常に停止しました"
"restartError" = "Xrayの再起動中にエラーが発生しました。"
"stopError" = "Xrayの停止中にエラーが発生しました。"
"basicTemplate" = "基本設定"
"advancedTemplate" = "高度な設定"
"generalConfigs" = "一般設定"
"generalConfigsDesc" = "これらのオプションは一般設定を決定します"
"logConfigs" = "ログ"
"logConfigsDesc" = "ログはサーバーのパフォーマンスに影響を与える可能性があるため、必要な場合にのみ有効にすることをお勧めします"
"blockConfigsDesc" = "これらのオプションは、特定のプロトコルやウェブサイトへのユーザー接続をブロックします"
"basicRouting" = "基本ルーティング"
"blockConnectionsConfigsDesc" = "これらのオプションにより、特定のリクエスト元の国に基づいてトラフィックをブロックします。"
"directConnectionsConfigsDesc" = "直接接続により、特定のトラフィックが他のサーバーを経由しないようにします。"
"blockips" = "IPをブロック"
"blockdomains" = "ドメインをブロック"
"directips" = "直接IP"
"directdomains" = "直接ドメイン"
"ipv4Routing" = "IPv4 ルーティング"
"ipv4RoutingDesc" = "このオプションはIPv4のみを介してターゲットドメインへルーティングします"
"warpRouting" = "WARP ルーティング"
"warpRoutingDesc" = "注意：これらのオプションを使用する前に、パネルのGitHubの手順に従って、サーバーにsocks5プロキシモードでWARPをインストールしてください。WARPはCloudflareサーバー経由でトラフィックをウェブサイトにルーティングします。"
"Template" = "高度なXray設定テンプレート"
"TemplateDesc" = "最終的なXray設定ファイルはこのテンプレートに基づいて生成されます"
"FreedomStrategy" = "Freedom プロトコル戦略"
"FreedomStrategyDesc" = "Freedomプロトコル内のネットワークの出力戦略を設定する"
"RoutingStrategy" = "ルーティングドメイン戦略設定"
"RoutingStrategyDesc" = "DNS解決の全体的なルーティング戦略を設定する"
"Torrent" = "BitTorrent プロトコルをブロック"
"Inbounds" = "インバウンドルール"
"InboundsDesc" = "特定のクライアントからのトラフィックを受け入れる"
"Outbounds" = "アウトバウンドルール"
"Balancers" = "負荷分散"
"OutboundsDesc" = "アウトバウンドトラフィックの送信方法を設定する"
"Routings" = "ルーティングルール"
"RoutingsDesc" = "各ルールの優先順位が重要です"
"completeTemplate" = "すべて"
"logLevel" = "ログレベル"
"logLevelDesc" = "エラーログのレベルを指定し、記録する情報を示します"
"accessLog" = "アクセスログ"
"accessLogDesc" = "アクセスログのファイルパス。特殊値 'none' はアクセスログを無効にします"
"errorLog" = "エラーログ"
"errorLogDesc" = "エラーログのファイルパス。特殊値 'none' はエラーログを無効にします"
"dnsLog" = "DNS ログ"
"dnsLogDesc" = "DNSクエリのログを有効にするかどうか"
"maskAddress" = "アドレスをマスク"
"maskAddressDesc" = "IPアドレスをマスクし、有効にするとログに表示されるIPアドレスを自動的に置き換えます"
"statistics" = "統計"
"statsInboundUplink" = "インバウンドアップロード統計"
"statsInboundUplinkDesc" = "すべてのインバウンドプロキシのアップストリームトラフィックの統計収集を有効にします。"
"statsInboundDownlink" = "インバウンドダウンロード統計"
"statsInboundDownlinkDesc" = "すべてのインバウンドプロキシのダウンストリームトラフィックの統計収集を有効にします。"
"statsOutboundUplink" = "アウトバウンドアップロード統計"
"statsOutboundUplinkDesc" = "すべてのアウトバウンドプロキシのアップストリームトラフィックの統計収集を有効にします。"
"statsOutboundDownlink" = "アウトバウンドダウンロード統計"
"statsOutboundDownlinkDesc" = "すべてのアウトバウンドプロキシのダウンストリームトラフィックの統計収集を有効にします。"

[pages.xray.rules]
"first" = "最初"
"last" = "最後"
"up" = "上へ"
"down" = "下へ"
"source" = "ソース"
"dest" = "宛先アドレス"
"inbound" = "インバウンド"
"outbound" = "アウトバウンド"
"balancer" = "負荷分散"
"info" = "情報"
"add" = "ルール追加"
"edit" = "ルール編集"
"useComma" = "カンマ区切りの項目"

[pages.xray.outbound]
"addOutbound" = "アウトバウンド追加"
"addReverse" = "リバース追加"
"editOutbound" = "アウトバウンド編集"
"editReverse" = "リバース編集"
"tag" = "タグ"
"tagDesc" = "一意のタグ"
"address" = "アドレス"
"reverse" = "リバース"
"domain" = "ドメイン"
"type" = "タイプ"
"bridge" = "ブリッジ"
"portal" = "ポータル"
"link" = "リンク"
"intercon" = "インターコネクション"
"settings" = "設定"
"accountInfo" = "アカウント情報"
"outboundStatus" = "アウトバウンドステータス"
"sendThrough" = "送信経路"

[pages.xray.balancer]
"addBalancer" = "負荷分散追加"
"editBalancer" = "負荷分散編集"
"balancerStrategy" = "戦略"
"balancerSelectors" = "セレクター"
"tag" = "タグ"
"tagDesc" = "一意のタグ"
"balancerDesc" = "balancerTagとoutboundTagは同時に使用できません。同時に使用された場合、outboundTagのみが有効になります。"

[pages.xray.wireguard]
"secretKey" = "シークレットキー"
"publicKey" = "公開鍵"
"allowedIPs" = "許可されたIP"
"endpoint" = "エンドポイント"
"psk" = "共有キー"
"domainStrategy" = "ドメイン戦略"

[pages.xray.dns]
"enable" = "DNSを有効にする"
"enableDesc" = "組み込みDNSサーバーを有効にする"
"tag" = "DNSインバウンドタグ"
"tagDesc" = "このタグはルーティングルールでインバウンドタグとして使用できます"
"clientIp" = "クライアントIP"
"clientIpDesc" = "DNSクエリ中に指定されたIPの位置をサーバーに通知するために使用されます"
"disableCache" = "キャッシュを無効にする"
"disableCacheDesc" = "DNSキャッシュを無効にします"
"disableFallback" = "フォールバックを無効にする"
"disableFallbackDesc" = "フォールバックDNSクエリを無効にします"
"disableFallbackIfMatch" = "一致した場合にフォールバックを無効にする"
"disableFallbackIfMatchDesc" = "DNSサーバーの一致するドメインリストにヒットした場合、フォールバックDNSクエリを無効にします"
"strategy" = "クエリ戦略"
"strategyDesc" = "ドメイン名解決の全体的な戦略"
"add" = "サーバー追加"
"edit" = "サーバー編集"
"domains" = "ドメイン"
"expectIPs" = "期待されるIP"
"unexpectIPs" = "予期しないIP"
"useSystemHosts" = "システムのHostsを使用"
"useSystemHostsDesc" = "インストール済みシステムのhostsファイルを使用する"
"usePreset" = "テンプレートを使用"
"dnsPresetTitle" = "DNSテンプレート"
"dnsPresetFamily" = "ファミリー"

[pages.xray.fakedns]
"add" = "フェイクDNS追加"
"edit" = "フェイクDNS編集"
"ipPool" = "IPプールサブネット"
"poolSize" = "プールサイズ"

[pages.settings.security]
"admin" = "管理者の資格情報"
"twoFactor" = "二段階認証"  
"twoFactorEnable" = "2FAを有効化"  
"twoFactorEnableDesc" = "セキュリティを強化するために追加の認証層を追加します。"  
"twoFactorModalSetTitle" = "二段階認証を有効にする"
"twoFactorModalDeleteTitle" = "二段階認証を無効にする"
"twoFactorModalSteps" = "二段階認証を設定するには、次の手順を実行してください:"
"twoFactorModalFirstStep" = "1. 認証アプリでこのQRコードをスキャンするか、QRコード近くのトークンをコピーしてアプリに貼り付けます"
"twoFactorModalSecondStep" = "2. アプリからコードを入力してください"
"twoFactorModalRemoveStep" = "二段階認証を削除するには、アプリからコードを入力してください。"
"twoFactorModalChangeCredentialsTitle" = "認証情報の変更"
"twoFactorModalChangeCredentialsStep" = "管理者の認証情報を変更するには、アプリケーションからコードを入力してください。"
"twoFactorModalSetSuccess" = "二要素認証が正常に設定されました"
"twoFactorModalDeleteSuccess" = "二要素認証が正常に削除されました"
"twoFactorModalError" = "コードが間違っています"

[pages.settings.toasts]
"modifySettings" = "パラメーターが変更されました。"
"getSettings" = "パラメーターの取得中にエラーが発生しました"
"modifyUserError" = "管理者認証情報の変更中にエラーが発生しました。"
"modifyUser" = "管理者の認証情報を正常に変更しました。"
"originalUserPassIncorrect" = "旧ユーザー名または旧パスワードが間違っています"
"userPassMustBeNotEmpty" = "新しいユーザー名と新しいパスワードは空にできません"
"getOutboundTrafficError" = "送信トラフィックの取得エラー"
"resetOutboundTrafficError" = "送信トラフィックのリセットエラー"

[tgbot]
"keyboardClosed" = "❌ カスタムキーボードが閉じられました！"
"noResult" = "❗ 結果がありません！"
"noQuery" = "❌ クエリが見つかりませんでした！もう一度コマンドを使用してください！"
"wentWrong" = "❌ 問題が発生しました！"
"noIpRecord" = "❗ IP記録がありません！"
"noInbounds" = "❗ インバウンド接続が見つかりません！"
"unlimited" = "♾ 無制限"
"add" = "追加"
"month" = "月"
"months" = "月"
"day" = "日"
"days" = "日"
"hours" = "時間"
"unknown" = "不明"
"inbounds" = "インバウンド接続"
"clients" = "クライアント"
"offline" = "🔴 オフライン"
"online" = "🟢 オンライン"

[tgbot.commands]
"unknown" = "❗ 不明なコマンド"
"pleaseChoose" = "👇 選択してください：\r\n"
"help" = "🤖 このボットをご利用いただきありがとうございます！サーバーから特定のデータを提供し、必要な変更を行うことができます。\r\n\r\n"
"start" = "👋 こんにちは、<i>{{ .Firstname }}</i>。\r\n"
"welcome" = "🤖 <b>{{ .Hostname }}</b> 管理ボットへようこそ。\r\n"
"status" = "✅ ボットは正常に動作しています！"
"usage" = "❗ 検索するテキストを入力してください！"
"getID" = "🆔 あなたのIDは：<code>{{ .ID }}</code>"
"helpAdminCommands" = "Xray Coreを再起動するには：\r\n<code>/restart</code>\r\n\r\nクライアントの電子メールを検索するには：\r\n<code>/usage [電子メール]</code>\r\n\r\nインバウンド（クライアントの統計情報を含む）を検索するには：\r\n<code>/inbound [備考]</code>\r\n\r\nTelegramチャットID：\r\n<code>/id</code>"
"helpClientCommands" = "統計情報を検索するには、次のコマンドを使用してください：\r\n<code>/usage [電子メール]</code>\r\n\r\nTelegramチャットID：\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ 操作成功！"
"restartFailed" = "❗ 操作エラー。\r\n\r\n<code>エラー: {{ .Error }}</code>"
"xrayNotRunning" = "❗ Xray Core は動作していません。"
"startDesc" = "メインメニューを表示"
"helpDesc" = "ボットのヘルプ"
"statusDesc" = "ボットの状態を確認"
"idDesc" = "Telegram IDを表示"

[tgbot.messages]
"cpuThreshold" = "🔴 CPU使用率は{{ .Percent }}%、しきい値{{ .Threshold }}%を超えました"
"selectUserFailed" = "❌ ユーザーの選択に失敗しました！"
"userSaved" = "✅ Telegramユーザーが保存されました。"
"loginSuccess" = "✅ パネルに正常にログインしました。\r\n"
"loginFailed" = "❗️ パネルのログインに失敗しました。\r\n"
"report" = "🕰 定期報告：{{ .RunTime }}\r\n"
"datetime" = "⏰ 日時：{{ .DateTime }}\r\n"
"hostname" = "💻 ホスト名：{{ .Hostname }}\r\n"
"version" = "🚀 X-UI バージョン：{{ .Version }}\r\n"
"xrayVersion" = "📡 Xray バージョン: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6：{{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4：{{ .IPv4 }}\r\n"
"ip" = "🌐 IP：{{ .IP }}\r\n"
"ips" = "🔢 IPアドレス：\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ サーバー稼働時間：{{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 サーバー負荷：{{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 サーバーメモリ：{{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 TCP接続数：{{ .Count }}\r\n"
"udpCount" = "🔸 UDP接続数：{{ .Count }}\r\n"
"traffic" = "🚦 トラフィック：{{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ Xrayステータス：{{ .State }}\r\n"
"username" = "👤 ユーザー名：{{ .Username }}\r\n"
"password" = "👤 パスワード: {{ .Password }}\r\n"
"time" = "⏰ 時間：{{ .Time }}\r\n"
"inbound" = "📍 インバウンド：{{ .Remark }}\r\n"
"port" = "🔌 ポート：{{ .Port }}\r\n"
"expire" = "📅 有効期限：{{ .Time }}\r\n"
"expireIn" = "📅 残り時間：{{ .Time }}\r\n"
"active" = "💡 有効：{{ .Enable }}\r\n"
"enabled" = "🚨 有効化済み：{{ .Enable }}\r\n"
"online" = "🌐 接続ステータス：{{ .Status }}\r\n"
"email" = "📧 メール：{{ .Email }}\r\n"
"upload" = "🔼 アップロード↑：{{ .Upload }}\r\n"
"download" = "🔽 ダウンロード↓：{{ .Download }}\r\n"
"total" = "📊 合計：{{ .UpDown }} / {{ .Total }}\r\n"
"TGUser" = "👤 Telegramユーザー：{{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 消耗済みの {{ .Type }}：\r\n"
"exhaustedCount" = "🚨 消耗済みの {{ .Type }} 数量：\r\n"
"onlinesCount" = "🌐 オンラインクライアント：{{ .Count }}\r\n"
"disabled" = "🛑 無効化：{{ .Disabled }}\r\n"
"depleteSoon" = "🔜 間もなく消耗：{{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 バックアップ時間：{{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 更新時間：{{ .Time }}\r\n\r\n"
"yes" = "✅ はい"
"no" = "❌ いいえ"

"received_id" = "🔑📥 IDが更新されました。"
"received_password" = "🔑📥 パスワードが更新されました。"
"received_email" = "📧📥 メールが更新されました。"
"received_comment" = "💬📥 コメントが更新されました。"
"id_prompt" = "🔑 デフォルトID: {{ .ClientId }}\n\nIDを入力してください。"
"pass_prompt" = "🔑 デフォルトパスワード: {{ .ClientPassword }}\n\nパスワードを入力してください。"
"email_prompt" = "📧 デフォルトメール: {{ .ClientEmail }}\n\nメールを入力してください。"
"comment_prompt" = "💬 デフォルトコメント: {{ .ClientComment }}\n\nコメントを入力してください。"
"inbound_client_data_id" = "🔄 インバウンド: {{ .InboundRemark }}\n\n🔑 ID: {{ .ClientId }}\n📧 メール: {{ .ClientEmail }}\n📊 トラフィック: {{ .ClientTraffic }}\n📅 有効期限: {{ .ClientExp }}\n🌐 IP制限: {{ .IpLimit }}\n💬 コメント: {{ .ClientComment }}\n\n今すぐこのクライアントをインバウンドに追加できます！"
"inbound_client_data_pass" = "🔄 インバウンド: {{ .InboundRemark }}\n\n🔑 パスワード: {{ .ClientPass }}\n📧 メール: {{ .ClientEmail }}\n📊 トラフィック: {{ .ClientTraffic }}\n📅 有効期限: {{ .ClientExp }}\n🌐 IP制限: {{ .IpLimit }}\n💬 コメント: {{ .ClientComment }}\n\n今すぐこのクライアントをインバウンドに追加できます！"
"cancel" = "❌ プロセスがキャンセルされました！\n\nいつでも /start で再開できます。 🔄"
"error_add_client"  = "⚠️ エラー:\n\n {{ .error }}"
"using_default_value"  = "わかりました、デフォルト値を使用します。 😊"
"incorrect_input" ="入力が無効です。\nフレーズはスペースなしで続けて入力してください。\n正しい例: aaaaaa\n間違った例: aaa aaa 🚫"
"AreYouSure" = "本当にいいですか？🤔"
"SuccessResetTraffic" = "📧 メール: {{ .ClientEmail }}\n🏁 結果: ✅ 成功"
"FailedResetTraffic" = "📧 メール: {{ .ClientEmail }}\n🏁 結果: ❌ 失敗 \n\n🛠️ エラー: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 すべてのクライアントのトラフィックリセットが完了しました。"


[tgbot.buttons]
"closeKeyboard" = "❌ キーボードを閉じる"
"cancel" = "❌ キャンセル"
"cancelReset" = "❌ リセットをキャンセル"
"cancelIpLimit" = "❌ IP制限をキャンセル"
"confirmResetTraffic" = "✅ トラフィックをリセットしますか？"
"confirmClearIps" = "✅ IPをクリアしますか？"
"confirmRemoveTGUser" = "✅ Telegramユーザーを削除しますか？"
"confirmToggle" = "✅ ユーザーを有効/無効にしますか？"
"dbBackup" = "データベースバックアップを取得"
"serverUsage" = "サーバーの使用状況"
"getInbounds" = "インバウンド情報を取得"
"depleteSoon" = "間もなく消耗"
"clientUsage" = "使用状況を取得"
"onlines" = "オンラインクライアント"
"commands" = "コマンド"
"refresh" = "🔄 更新"
"clearIPs" = "❌ IPをクリア"
"removeTGUser" = "❌ Telegramユーザーを削除"
"selectTGUser" = "👤 Telegramユーザーを選択"
"selectOneTGUser" = "👤 1人のTelegramユーザーを選択："
"resetTraffic" = "📈 トラフィックをリセット"
"resetExpire" = "📅 有効期限を変更"
"ipLog" = "🔢 IPログ"
"ipLimit" = "🔢 IP制限"
"setTGUser" = "👤 Telegramユーザーを設定"
"toggle" = "🔘 有効/無効"
"custom" = "🔢 カスタム"
"confirmNumber" = "✅ 確認: {{ .Num }}"
"confirmNumberAdd" = "✅ 追加を確認：{{ .Num }}"
"limitTraffic" = "🚧 トラフィック制限"
"getBanLogs" = "禁止ログ"
"allClients" = "すべてのクライアント"

"addClient" = "クライアントを追加"
"submitDisable" = "無効として送信 ☑️"
"submitEnable" = "有効として送信 ✅"
"use_default" = "🏷️ デフォルトを使用"
"change_id" = "⚙️🔑 ID"
"change_password" = "⚙️🔑 パスワード"
"change_email" = "⚙️📧 メールアドレス"
"change_comment" = "⚙️💬 コメント"
"ResetAllTraffics" = "すべてのトラフィックをリセット"
"SortedTrafficUsageReport" = "ソートされたトラフィック使用レポート"


[tgbot.answers]
"successfulOperation" = "✅ 成功！"
"errorOperation" = "❗ 操作エラー。"
"getInboundsFailed" = "❌ インバウンド情報の取得に失敗しました。"
"getClientsFailed" = "❌ クライアントの取得に失敗しました。"
"canceled" = "❌ {{ .Email }}：操作がキャンセルされました。"
"clientRefreshSuccess" = "✅ {{ .Email }}：クライアントが正常に更新されました。"
"IpRefreshSuccess" = "✅ {{ .Email }}：IPが正常に更新されました。"
"TGIdRefreshSuccess" = "✅ {{ .Email }}：クライアントのTelegramユーザーが正常に更新されました。"
"resetTrafficSuccess" = "✅ {{ .Email }}：トラフィックが正常にリセットされました。"
"setTrafficLimitSuccess" = "✅ {{ .Email }}：トラフィック制限が正常に保存されました。"
"expireResetSuccess" = "✅ {{ .Email }}：有効期限の日数が正常にリセットされました。"
"resetIpSuccess" = "✅ {{ .Email }}：IP制限数が正常に保存されました：{{ .Count }}。"
"clearIpSuccess" = "✅ {{ .Email }}：IPが正常にクリアされました。"
"getIpLog" = "✅ {{ .Email }}：IPログの取得。"
"getUserInfo" = "✅ {{ .Email }}：Telegramユーザー情報の取得。"
"removedTGUserSuccess" = "✅ {{ .Email }}：Telegramユーザーが正常に削除されました。"
"enableSuccess" = "✅ {{ .Email }}：正常に有効化されました。"
"disableSuccess" = "✅ {{ .Email }}：正常に無効化されました。"
"askToAddUserId" = "設定が見つかりませんでした！\r\n管理者に問い合わせて、設定にTelegramユーザーのChatIDを使用してください。\r\n\r\nあなたのユーザーChatID：<code>{{ .TgUserID }}</code>"
"chooseClient" = "インバウンド {{ .Inbound }} のクライアントを選択"
"chooseInbound" = "インバウンドを選択"
