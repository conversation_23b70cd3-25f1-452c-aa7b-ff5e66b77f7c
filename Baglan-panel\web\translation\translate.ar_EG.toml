"username" = "اسم المستخدم"
"password" = "الباسورد"
"login" = "تسجيل الدخول"
"confirm" = "تأكيد"
"cancel" = "إلغاء"
"close" = "إغلاق"
"create" = "إنشاء"
"update" = "تحديث"
"copy" = "نسخ"
"copied" = "اتنسخ"
"download" = "تحميل"
"remark" = "ملاحظة"
"enable" = "مفعل"
"protocol" = "بروتوكول"
"search" = "بحث"
"filter" = "فلترة"
"loading" = "جاري التحميل..."
"second" = "ثانية"
"minute" = "دقيقة"
"hour" = "ساعة"
"day" = "يوم"
"check" = "شيك"
"indefinite" = "غير محدد"
"unlimited" = "غير محدود"
"none" = "مفيش"
"qrCode" = "كود QR"
"info" = "معلومات أكتر"
"edit" = "تعديل"
"delete" = "مسح"
"reset" = "إعادة ضبط"
"noData" = "لا توجد بيانات."
"copySuccess" = "اتنسخ بنجاح"
"sure" = "متأكد؟"
"encryption" = "تشفير"
"useIPv4ForHost" = "استخدم IPv4 للمضيف"
"transmission" = "نقل"
"host" = "المستضيف"
"path" = "مسار"
"camouflage" = "تمويه"
"status" = "الحالة"
"enabled" = "مفعل"
"disabled" = "معطل"
"depleted" = "خلص"
"depletingSoon" = "هينتهي قريب"
"offline" = "أوفلاين"
"online" = "أونلاين"
"domainName" = "اسم الدومين"
"monitor" = "المسمع IP"
"certificate" = "شهادة رقمية"
"fail" = "فشل"
"comment" = "تعليق"
"success" = "تم بنجاح"
"getVersion" = "جيب النسخة"
"install" = "تثبيت"
"clients" = "عملاء"
"usage" = "استخدام"
"twoFactorCode" = "الكود"
"remained" = "المتبقي"
"security" = "أمان"
"secAlertTitle" = "تنبيه أمني"
"secAlertSsl" = "الاتصال ده مش آمن. ابعد عن إدخال معلومات حساسة لغاية ما تشغل TLS لحماية البيانات."
"secAlertConf" = "بعض الإعدادات معرضة لهجمات. ينصح بتعزيز بروتوكولات الأمان عشان تمنع الاختراقات المحتملة."
"secAlertSSL" = "البانل مش مؤمن. حمّل شهادة TLS لحماية البيانات."
"secAlertPanelPort" = "بورت البانل الافتراضي معرض للخطر. ياريت تغير لبورت عشوائي أو محدد."
"secAlertPanelURI" = "مسار URI الافتراضي للبانل مش آمن. ياريت تضبط مسار URI معقد."
"secAlertSubURI" = "مسار URI الافتراضي للاشتراك مش آمن. ياريت تضبط مسار URI معقد."
"secAlertSubJsonURI" = "مسار URI الافتراضي لاشتراك JSON مش آمن. ياريت تضبط مسار URI معقد."
"emptyDnsDesc" = "مفيش سيرفر DNS مضاف."
"emptyFakeDnsDesc" = "مفيش سيرفر Fake DNS مضاف."
"emptyBalancersDesc" = "مفيش موازن تحميل مضاف."
"emptyReverseDesc" = "مفيش بروكسي عكسي مضاف."
"somethingWentWrong" = "حدث خطأ ما"

[menu]
"theme" = "الثيم"
"dark" = "داكن"
"ultraDark" = "داكن جدًا"
"dashboard" = "نظرة عامة"
"inbounds" = "الإدخالات"
"settings" = "إعدادات البانل"
"xray" = "إعدادات Xray"
"logout" = "تسجيل خروج"
"link" = "إدارة"

[pages.login]
"hello" = "أهلا"
"title" = "أهلاً وسهلاً"
"loginAgain" = "انتهت صلاحية الجلسة، سجل دخول تاني"

[pages.login.toasts]
"invalidFormData" = "تنسيق البيانات المدخلة مش صحيح."
"emptyUsername" = "اسم المستخدم مطلوب"
"emptyPassword" = "الباسورد مطلوب"
"wrongUsernameOrPassword" = "اسم المستخدم أو كلمة المرور أو كود المصادقة الثنائية غير صحيح."  
"successLogin" = "لقد تم تسجيل الدخول إلى حسابك بنجاح."

[pages.index]
"title" = "نظرة عامة"
"cpu" = "المعالج"
"logicalProcessors" = "المعالجات المنطقية"
"frequency" = "التردد"
"swap" = "Swap"
"storage" = "تخزين"
"memory" = "رام"
"threads" = "خيوط المعالجة"
"xrayStatus" = "Xray"
"stopXray" = "إيقاف"
"restartXray" = "إعادة تشغيل"
"xraySwitch" = "النسخة"
"xraySwitchClick" = "اختار النسخة اللي عايز تتحول لها."
"xraySwitchClickDesk" = "اختار بحذر، النسخ القديمة ممكن ما تتوافقش مع الإعدادات الحالية."
"xrayStatusUnknown" = "مش معروف"
"xrayStatusRunning" = "شغالة"
"xrayStatusStop" = "متوقفة"
"xrayStatusError" = "فيها غلطة"
"xrayErrorPopoverTitle" = "حصل خطأ أثناء تشغيل Xray"
"operationHours" = "مدة التشغيل"
"systemLoad" = "تحميل النظام"
"systemLoadDesc" = "متوسط تحميل النظام في الدقائق 1, 5, و15"
"connectionCount" = "إحصائيات الاتصال"
"ipAddresses" = "عناوين IP"
"toggleIpVisibility" = "بدل إظهار IP"
"overallSpeed" = "السرعة الكلية"
"upload" = "رفع"
"download" = "تنزيل"
"totalData" = "إجمالي البيانات"
"sent" = "مرسل"
"received" = "مستقبل"
"documentation" = "التوثيق"
"xraySwitchVersionDialog" = "هل تريد حقًا تغيير إصدار Xray؟"
"xraySwitchVersionDialogDesc" = "سيؤدي هذا إلى تغيير إصدار Xray إلى #version#."
"xraySwitchVersionPopover" = "تم تحديث Xray بنجاح"
"geofileUpdateDialog" = "هل تريد حقًا تحديث ملف الجغرافيا؟"
"geofileUpdateDialogDesc" = "سيؤدي هذا إلى تحديث ملف #filename#."
"geofileUpdatePopover" = "تم تحديث ملف الجغرافيا بنجاح"
"dontRefresh" = "التثبيت شغال، متعملش Refresh للصفحة"
"logs" = "السجلات"
"config" = "الإعدادات"
"backup" = "نسخة احتياطية"
"backupTitle" = "نسخة احتياطية واسترجاع قاعدة البيانات"
"exportDatabase" = "اخزن نسخة"
"exportDatabaseDesc" = "اضغط عشان تحمل ملف .db يحتوي على نسخة احتياطية لقاعدة البيانات الحالية على جهازك."
"importDatabase" = "استرجاع"
"importDatabaseDesc" = "اضغط عشان تختار وتحمل ملف .db من جهازك لاسترجاع قاعدة البيانات من نسخة احتياطية."
"importDatabaseSuccess" = "تم استيراد قاعدة البيانات بنجاح"
"importDatabaseError" = "حدث خطأ أثناء استيراد قاعدة البيانات"
"readDatabaseError" = "حدث خطأ أثناء قراءة قاعدة البيانات"
"getDatabaseError" = "حدث خطأ أثناء استرجاع قاعدة البيانات"
"getConfigError" = "حدث خطأ أثناء استرجاع ملف الإعدادات"

[pages.inbounds]
"title" = "الإدخالات"
"totalDownUp" = "إجمالي المرسل/المستقبل"
"totalUsage" = "إجمالي الاستخدام"
"inboundCount" = "عدد الإدخالات"
"operate" = "القائمة"
"enable" = "مفعل"
"remark" = "ملاحظة"
"protocol" = "بروتوكول"
"port" = "بورت"
"traffic" = "الترافيك"
"details" = "تفاصيل"
"transportConfig" = "نقل"
"expireDate" = "المدة"
"resetTraffic" = "إعادة ضبط الترافيك"
"addInbound" = "أضف إدخال"
"generalActions" = "إجراءات عامة"
"autoRefresh" = "تحديث تلقائي"
"autoRefreshInterval" = "الفاصل"
"modifyInbound" = "تعديل الإدخال"
"deleteInbound" = "حذف الإدخال"
"deleteInboundContent" = "متأكد إنك عايز تحذف الإدخال؟"
"deleteClient" = "حذف العميل"
"deleteClientContent" = "متأكد إنك عايز تحذف العميل؟"
"resetTrafficContent" = "متأكد إنك عايز تعيد ضبط الترافيك؟"
"inboundUpdateSuccess" = "تم تحديث الوارد بنجاح."
"inboundCreateSuccess" = "تم إنشاء الوارد بنجاح."
"copyLink" = "انسخ الرابط"
"address" = "العنوان"
"network" = "الشبكة"
"destinationPort" = "بورت الوجهة"
"targetAddress" = "عنوان الهدف"
"monitorDesc" = "سيبها فاضية لو عايز تستمع على كل الـ IPs"
"meansNoLimit" = "= غير محدود. (الوحدة: جيجابايت)"
"totalFlow" = "إجمالي التدفق"
"leaveBlankToNeverExpire" = "سيبها فاضية عشان ماتنتهيش"
"noRecommendKeepDefault" = "ننصح باستخدام الافتراضي"
"certificatePath" = "مسار الملف"
"certificateContent" = "محتوى الملف"
"publicKey" = "المفتاح العام"
"privatekey" = "المفتاح الخاص"
"clickOnQRcode" = "اضغط على كود QR للنسخ"
"client" = "عميل"
"export" = "تصدير كل الروابط"
"clone" = "استنساخ"
"cloneInbound" = "استنساخ الإدخال"
"cloneInboundContent" = "كل إعدادات الإدخال ده، غير البورت، IP الاستماع، والعملاء، هتتطبق على الاستنساخ."
"cloneInboundOk" = "استنساخ"
"resetAllTraffic" = "إعادة ضبط ترافيك كل الإدخالات"
"resetAllTrafficTitle" = "إعادة ضبط ترافيك كل الإدخالات"
"resetAllTrafficContent" = "متأكد إنك عايز تعيد ضبط الترافيك لكل الإدخالات؟"
"resetInboundClientTraffics" = "إعادة ضبط ترافيك العملاء"
"resetInboundClientTrafficTitle" = "إعادة ضبط ترافيك العملاء"
"resetInboundClientTrafficContent" = "متأكد إنك عايز تعيد ضبط ترافيك عملاء الإدخال ده؟"
"resetAllClientTraffics" = "إعادة ضبط ترافيك كل العملاء"
"resetAllClientTrafficTitle" = "إعادة ضبط ترافيك كل العملاء"
"resetAllClientTrafficContent" = "متأكد إنك عايز تعيد ضبط ترافيك كل العملاء؟"
"delDepletedClients" = "حذف العملاء اللي خلصت"
"delDepletedClientsTitle" = "حذف العملاء اللي خلصت"
"delDepletedClientsContent" = "متأكد إنك عايز تحذف كل العملاء اللي خلصت؟"
"email" = "الإيميل"
"emailDesc" = "ادخل إيميل فريد."
"IPLimit" = "تحديد IP"
"IPLimitDesc" = "بيعطل الإدخال لو العدد زاد عن القيمة المحددة. (0 = تعطيل)"
"IPLimitlog" = "سجل IP"
"IPLimitlogDesc" = "سجل تاريخ الـ IPs. (عشان تفعل الإدخال بعد التعطيل، امسح السجل)"
"IPLimitlogclear" = "امسح السجل"
"setDefaultCert" = "استخدم شهادة البانل"
"telegramDesc" = "ادخل ID شات Telegram. (استخدم '/id' في البوت) أو (@userinfobot)"
"subscriptionDesc" = "عشان تلاقي رابط الاشتراك، ادخل على 'التفاصيل'. وكمان ممكن تستخدم نفس الاسم لعدة عملاء."
"info" = "معلومات"
"same" = "نفسه"
"inboundData" = "بيانات الإدخال"
"exportInbound" = "تصدير الإدخال"
"import" = "استيراد"
"importInbound" = "استيراد إدخال"

[pages.client]
"add" = "أضف عميل"
"edit" = "تعديل عميل"
"submitAdd" = "أضف العميل"
"submitEdit" = "احفظ التعديلات"
"clientCount" = "عدد العملاء"
"bulk" = "إضافة بالجملة"
"method" = "طريقة"
"first" = "أول واحد"
"last" = "آخر واحد"
"prefix" = "بادئة"
"postfix" = "لاحقة"
"delayedStart" = "ابدأ بعد أول استخدام"
"expireDays" = "المدة"
"days" = "يوم/أيام"
"renew" = "تجديد تلقائي"
"renewDesc" = "تجديد تلقائي بعد انتهاء الصلاحية. (0 = تعطيل)(الوحدة: يوم)"

[pages.inbounds.toasts]
"obtain" = "تم الحصول عليه"
"updateSuccess" = "تم التحديث بنجاح"
"logCleanSuccess" = "تم مسح السجل"
"inboundsUpdateSuccess" = "تم تحديث الواردات بنجاح"
"inboundUpdateSuccess" = "تم تحديث الوارد بنجاح"
"inboundCreateSuccess" = "تم إنشاء الوارد بنجاح"
"inboundDeleteSuccess" = "تم حذف الوارد بنجاح"
"inboundClientAddSuccess" = "تمت إضافة عميل(عملاء) وارد"
"inboundClientDeleteSuccess" = "تم حذف عميل وارد"
"inboundClientUpdateSuccess" = "تم تحديث عميل وارد"
"delDepletedClientsSuccess" = "تم حذف جميع العملاء المستنفذين"
"resetAllClientTrafficSuccess" = "تم إعادة تعيين كل حركة المرور من العميل"
"resetAllTrafficSuccess" = "تم إعادة تعيين كل حركة المرور"
"resetInboundClientTrafficSuccess" = "تم إعادة تعيين حركة المرور"
"trafficGetError" = "خطأ في الحصول على حركات المرور"
"getNewX25519CertError" = "حدث خطأ أثناء الحصول على شهادة X25519."

[pages.inbounds.stream.general]
"request" = "طلب"
"response" = "رد"
"name" = "اسم"
"value" = "قيمة"

[pages.inbounds.stream.tcp]
"version" = "نسخة"
"method" = "طريقة"
"path" = "مسار"
"status" = "الحالة"
"statusDescription" = "وصف الحالة"
"requestHeader" = "رأس الطلب"
"responseHeader" = "رأس الرد"

[pages.settings]
"title" = "إعدادات البانل"
"save" = "حفظ"
"infoDesc" = "كل تغيير هتعمله هنا لازم يتخزن. ياريت تعيد تشغيل البانل عشان التعديلات تتفعل."
"restartPanel" = "إعادة تشغيل البانل"
"restartPanelDesc" = "متأكد إنك عايز تعيد تشغيل البانل؟ لو ماقدرتش تدخل بعد إعادة التشغيل، شوف سجل البانل على السيرفر."
"restartPanelSuccess" = "تم إعادة تشغيل اللوحة بنجاح"
"actions" = "إجراءات"
"resetDefaultConfig" = "استرجاع الافتراضي"
"panelSettings" = "عام"
"securitySettings" = "المصادقة"
"TGBotSettings" = "بوت Telegram"
"panelListeningIP" = "IP الاستماع"
"panelListeningIPDesc" = "عنوان IP للبانل. (سيبه فاضي عشان يستمع على كل الـ IPs)"
"panelListeningDomain" = "دومين الاستماع"
"panelListeningDomainDesc" = "اسم الدومين للبانل. (سيبه فاضي عشان يستمع على كل الدومينات والـ IPs)"
"panelPort" = "بورت الاستماع"
"panelPortDesc" = "رقم البورت للبانل. (لازم يكون بورت فاضي)"
"publicKeyPath" = "مسار المفتاح العام"
"publicKeyPathDesc" = "مسار ملف المفتاح العام للبانل. (يبدأ بـ '/')"
"privateKeyPath" = "مسار المفتاح الخاص"
"privateKeyPathDesc" = "مسار ملف المفتاح الخاص للبانل. (يبدأ بـ '/')"
"panelUrlPath" = "مسار URI"
"panelUrlPathDesc" = "مسار URI للبانل. (يبدأ بـ '/' وبينتهي بـ '/')"
"pageSize" = "حجم الصفحة"
"pageSizeDesc" = "حدد حجم الصفحة لجدول الإدخالات. (0 = تعطيل)"
"remarkModel" = "نموذج الملاحظة وحرف الفصل"
"datepicker" = "نوع التقويم"
"datepickerPlaceholder" = "اختار التاريخ"
"datepickerDescription" = "المهام المجدولة هتشتغل بناءً على التقويم ده."
"sampleRemark" = "مثال للملاحظة"
"oldUsername" = "اسم المستخدم الحالي"
"currentPassword" = "الباسورد الحالي"
"newUsername" = "اسم المستخدم الجديد"
"newPassword" = "الباسورد الجديد"
"telegramBotEnable" = "تفعيل بوت Telegram"
"telegramBotEnableDesc" = "يفعل بوت Telegram."
"telegramToken" = "توكن Telegram"
"telegramTokenDesc" = "توكن البوت اللي جبت من '@BotFather'."
"telegramProxy" = "بروكسي SOCKS"
"telegramProxyDesc" = "يفعل بروكسي SOCKS5 للاتصال بـ Telegram. (اضبط الإعدادات حسب الدليل)"
"telegramAPIServer" = "سيرفر Telegram API"
"telegramAPIServerDesc" = "سيرفر Telegram API المستخدم. سيبه فاضي لاستخدام الافتراضي."
"telegramChatId" = "ID شات الأدمن"
"telegramChatIdDesc" = "ID شات الأدمن في Telegram. (مفصول بفواصل)(تقدر تجيبه من @userinfobot) أو (استخدم '/id' في البوت)"
"telegramNotifyTime" = "وقت الإشعار"
"telegramNotifyTimeDesc" = "وقت إشعار البوت للتقارير الدورية. (استخدم صيغة وقت crontab)"
"tgNotifyBackup" = "نسخة احتياطية لقاعدة البيانات"
"tgNotifyBackupDesc" = "ابعت ملف النسخة الاحتياطية لقاعدة البيانات مع التقرير."
"tgNotifyLogin" = "إشعار بتسجيل الدخول"
"tgNotifyLoginDesc" = "استقبل إشعار بكل محاولة تسجيل دخول للبانل مع اسم المستخدم، الـ IP، والوقت."
"sessionMaxAge" = "مدة الجلسة"
"sessionMaxAgeDesc" = "المدة اللي تفضل فيها مسجل دخول. (الوحدة: دقيقة)"
"expireTimeDiff" = "تنبيه بتاريخ الانتهاء"
"expireTimeDiffDesc" = "استقبل تنبيه قبل ما توصل لتاريخ الانتهاء بالمدة المحددة. (الوحدة: يوم)"
"trafficDiff" = "تنبيه حد الترافيك"
"trafficDiffDesc" = "استقبل تنبيه عند وصول الترافيك للحد المحدد. (الوحدة: جيجابايت)"
"tgNotifyCpu" = "تنبيه حمل المعالج"
"tgNotifyCpuDesc" = "استقبل تنبيه لو حمل المعالج عدى الحد المحدد. (الوحدة: %)"
"timeZone" = "المنطقة الزمنية"
"timeZoneDesc" = "المهام المجدولة هتشتغل بناءً على المنطقة الزمنية دي."
"subSettings" = "الاشتراك"
"subEnable" = "تفعيل خدمة الاشتراك"
"subEnableDesc" = "يفعل خدمة الاشتراك."
"subTitle" = "عنوان الاشتراك"
"subTitleDesc" = "العنوان اللي هيظهر في عميل VPN"
"subListen" = "IP الاستماع"
"subListenDesc" = "عنوان IP لخدمة الاشتراك. (سيبه فاضي عشان يستمع على كل الـ IPs)"
"subPort" = "بورت الاستماع"
"subPortDesc" = "رقم البورت لخدمة الاشتراك. (لازم يكون بورت فاضي)"
"subCertPath" = "مسار المفتاح العام"
"subCertPathDesc" = "مسار ملف المفتاح العام لخدمة الاشتراك. (يبدأ بـ '/')"
"subKeyPath" = "مسار المفتاح الخاص"
"subKeyPathDesc" = "مسار ملف المفتاح الخاص لخدمة الاشتراك. (يبدأ بـ '/')"
"subPath" = "مسار URI"
"subPathDesc" = "مسار URI لخدمة الاشتراك. (يبدأ بـ '/' وبينتهي بـ '/')"
"subDomain" = "دومين الاستماع"
"subDomainDesc" = "اسم الدومين لخدمة الاشتراك. (سيبه فاضي عشان يستمع على كل الدومينات والـ IPs)"
"subUpdates" = "فترات التحديث"
"subUpdatesDesc" = "فترات تحديث رابط الاشتراك في تطبيقات العملاء. (الوحدة: ساعة)"
"subEncrypt" = "تشفير"
"subEncryptDesc" = "المحتوى اللي هيترجع من خدمة الاشتراك هيكون مشفر بـ Base64."
"subShowInfo" = "اظهر معلومات الاستخدام"
"subShowInfoDesc" = "هيظهر الترافيك المتبقي والتاريخ في تطبيقات العملاء."
"subURI" = "مسار البروكسي العكسي"
"subURIDesc" = "مسار URI لرابط الاشتراك عشان تستخدمه ورا البروكسي."
"externalTrafficInformEnable" = "تنبيه الترافيك الخارجي"
"externalTrafficInformEnableDesc" = "يبعت تنبيه لـ API خارجي مع كل تحديث للترافيك."
"externalTrafficInformURI" = "مسار تنبيه الترافيك الخارجي"
"externalTrafficInformURIDesc" = "تحديثات الترافيك هتتبعت للمسار ده."
"fragment" = "تجزئة"
"fragmentDesc" = "يفعل تجزئة لحزمة TLS hello."
"fragmentSett" = "إعدادات التجزئة"
"noisesDesc" = "يفعل التشويش."
"noisesSett" = "إعدادات التشويش"
"mux" = "MUX"
"muxDesc" = "ينقل أكثر من تيار بيانات مستقل خلال تيار بيانات واحد قائم."
"muxSett" = "إعدادات MUX"
"direct" = "اتصال مباشر"
"directDesc" = "ينشئ اتصال مباشر مع الدومينات أو نطاقات IP لدولة معينة."
"notifications" = "الإشعارات"
"certs" = "الشهادات"
"externalTraffic" = "الترافيك الخارجي"
"dateAndTime" = "التاريخ والوقت"
"proxyAndServer" = "البروكسي والسيرفر"
"intervals" = "الفترات"
"information" = "المعلومات"
"language" = "اللغة"
"telegramBotLanguage" = "لغة بوت Telegram"

[pages.xray]
"title" = "إعدادات Xray"
"save" = "احفظ"
"restart" = "أعد تشغيل Xray"
"restartSuccess" = "تم إعادة تشغيل Xray بنجاح"
"stopSuccess" = "تم إيقاف Xray بنجاح"
"restartError" = "حدث خطأ أثناء إعادة تشغيل Xray."
"stopError" = "حدث خطأ أثناء إيقاف Xray."
"basicTemplate" = "أساسي"
"advancedTemplate" = "متقدم"
"generalConfigs" = "إعدادات عامة"
"generalConfigsDesc" = "الخيارات دي هتحدد التعديلات العامة."
"logConfigs" = "السجلات"
"logConfigsDesc" = "السجلات ممكن تأثر على كفاءة السيرفر. ننصح بتفعيلها بحكمة لما تكون محتاجها."
"blockConfigsDesc" = "الخيارات دي هتحجب الترافيك بناءً على بروتوكولات ومواقع محددة."
"basicRouting" = "توجيه أساسي"
"blockConnectionsConfigsDesc" = "الخيارات دي هتحجب الترافيك بناءً على الدولة المطلوبة."
"directConnectionsConfigsDesc" = "الاتصال المباشر بيضمن إن الترافيك المعين مايمرش من سيرفر تاني."
"blockips" = "حظر IPs"
"blockdomains" = "حظر دومينات"
"directips" = "اتصالات مباشرة لـ IPs"
"directdomains" = "اتصالات مباشرة للدومينات"
"ipv4Routing" = "توجيه IPv4"
"ipv4RoutingDesc" = "الخيارات دي هتوجه الترافيك بناءً على وجهة معينة عبر IPv4."
"warpRouting" = "توجيه WARP"
"warpRoutingDesc" = "الخيارات دي هتوجه الترافيك بناءً على وجهة معينة عبر WARP."
"Template" = "قالب إعدادات Xray المتقدم"
"TemplateDesc" = "ملف إعدادات Xray النهائي هيتولد بناءً على القالب ده."
"FreedomStrategy" = "استراتيجية بروتوكول الحرية"
"FreedomStrategyDesc" = "اختار استراتيجية المخرجات للشبكة في بروتوكول الحرية."
"RoutingStrategy" = "استراتيجية التوجيه العامة"
"RoutingStrategyDesc" = "حدد استراتيجية التوجيه الإجمالية لحل كل الطلبات."
"Torrent" = "حظر بروتوكول التورنت"
"Inbounds" = "الإدخالات"
"InboundsDesc" = "قبول العملاء المعينين."
"Outbounds" = "المخرجات"
"Balancers" = "موازنات التحميل"
"OutboundsDesc" = "حدد مسار الترافيك الصادر."
"Routings" = "قواعد التوجيه"
"RoutingsDesc" = "أولوية كل قاعدة مهمة جداً!"
"completeTemplate" = "الكل"
"logLevel" = "مستوى السجلات"
"logLevelDesc" = "مستوى السجل الخاص بالأخطاء، اللي بيوضح المعلومات المطلوبة للتسجيل."
"accessLog" = "سجل الوصول"
"accessLogDesc" = "مسار ملف سجل الوصول. القيمة الخاصة 'none' بتعطل سجل الوصول."
"errorLog" = "سجل الأخطاء"
"errorLogDesc" = "مسار ملف سجل الأخطاء. القيمة الخاصة 'none' بتعطل سجل الأخطاء."
"dnsLog" = "سجل DNS"
"dnsLogDesc" = "لو هتسجل استعلامات DNS."
"maskAddress" = "إخفاء العنوان"
"maskAddressDesc" = "إخفاء عنوان الـ IP؛ لو مفعل، هيستبدل تلقائياً عنوان IP اللي بيظهر في السجل."
"statistics" = "إحصائيات"
"statsInboundUplink" = "إحصائيات رفع الإدخال"
"statsInboundUplinkDesc" = "تفعيل جمع الإحصائيات لترافيك الرفع لكل بروكسي من الإدخالات."
"statsInboundDownlink" = "إحصائيات تنزيل الإدخال"
"statsInboundDownlinkDesc" = "تفعيل جمع الإحصائيات لترافيك التنزيل لكل بروكسي من الإدخالات."
"statsOutboundUplink" = "إحصائيات رفع المخرجات"
"statsOutboundUplinkDesc" = "تفعيل جمع الإحصائيات لترافيك الرفع لكل بروكسي من المخرجات."
"statsOutboundDownlink" = "إحصائيات تنزيل المخرجات"
"statsOutboundDownlinkDesc" = "تفعيل جمع الإحصائيات لترافيك التنزيل لكل بروكسي من المخرجات."

[pages.xray.rules]
"first" = "أول"
"last" = "آخر"
"up" = "فوق"
"down" = "تحت"
"source" = "المصدر"
"dest" = "الوجهة"
"inbound" = "إدخال"
"outbound" = "مخرج"
"balancer" = "موازن"
"info" = "معلومات"
"add" = "أضف قاعدة"
"edit" = "عدل القاعدة"
"useComma" = "عناصر مفصولة بفواصل"

[pages.xray.outbound]
"addOutbound" = "أضف مخرج"
"addReverse" = "أضف عكسي"
"editOutbound" = "عدل المخرج"
"editReverse" = "عدل العكسي"
"tag" = "تاج"
"tagDesc" = "تاج فريد"
"address" = "العنوان"
"reverse" = "عكسي"
"domain" = "دومين"
"type" = "النوع"
"bridge" = "جسر"
"portal" = "بوابة"
"link" = "رابط"
"intercon" = "تواصل"
"settings" = "إعدادات"
"accountInfo" = "معلومات الحساب"
"outboundStatus" = "حالة المخرج"
"sendThrough" = "أرسل من خلال"

[pages.xray.balancer]
"addBalancer" = "أضف موازن تحميل"
"editBalancer" = "عدل موازن التحميل"
"balancerStrategy" = "استراتيجية الموازن"
"balancerSelectors" = "المحددات"
"tag" = "تاج"
"tagDesc" = "تاج فريد"
"balancerDesc" = "ماينفعش تستخدم balancerTag و outboundTag مع بعض. لو اتستخدموا مع بعض، outboundTag هو اللي هيشتغل."

[pages.xray.wireguard]
"secretKey" = "المفتاح السري"
"publicKey" = "المفتاح العام"
"allowedIPs" = "عناوين IP المسموح بها"
"endpoint" = "النهاية"
"psk" = "المفتاح المشترك"
"domainStrategy" = "استراتيجية الدومين"

[pages.xray.dns]
"enable" = "فعل DNS"
"enableDesc" = "فعل سيرفر DNS المدمج"
"tag" = "تاج إدخال DNS"
"tagDesc" = "التاج ده هيبقى متاح كإدخال في قواعد التوجيه."
"clientIp" = "IP العميل"
"clientIpDesc" = "بيحدد موقع العميل خلال استعلامات DNS"
"disableCache" = "تعطيل الكاش"
"disableCacheDesc" = "بيعطل تخزين نتائج DNS مؤقتاً"
"disableFallback" = "تعطيل النسخ الاحتياطي"
"disableFallbackDesc" = "بيعطل استعلامات DNS الاحتياطية"
"disableFallbackIfMatch" = "تعطيل النسخ الاحتياطي عند التطابق"
"disableFallbackIfMatchDesc" = "بيعطل استعلامات DNS الاحتياطية لما يتحقق تطابق مع قائمة الدومينات"
"strategy" = "استراتيجية الاستعلام"
"strategyDesc" = "الاستراتيجية العامة لحل أسماء الدومين"
"add" = "أضف سيرفر"
"edit" = "عدل السيرفر"
"domains" = "الدومينات"
"expectIPs" = "العناوين المتوقعة"
"unexpectIPs" = "عناوين IP غير متوقعة"
"useSystemHosts" = "استخدام ملف Hosts الخاص بالنظام"
"useSystemHostsDesc" = "استخدام ملف hosts من نظام مثبت"
"usePreset" = "استخدام النموذج"
"dnsPresetTitle" = "قوالب DNS"
"dnsPresetFamily" = "العائلي"

[pages.xray.fakedns]
"add" = "أضف Fake DNS"
"edit" = "عدل Fake DNS"
"ipPool" = "نطاق IP Pool"
"poolSize" = "حجم المجموعة"

[pages.settings.security]
"admin" = "بيانات الأدمن"
"twoFactor" = "المصادقة الثنائية"  
"twoFactorEnable" = "تفعيل المصادقة الثنائية"  
"twoFactorEnableDesc" = "يضيف طبقة إضافية من المصادقة لتعزيز الأمان."  
"twoFactorModalSetTitle" = "تفعيل المصادقة الثنائية"
"twoFactorModalDeleteTitle" = "تعطيل المصادقة الثنائية"
"twoFactorModalSteps" = "لإعداد المصادقة الثنائية، قم ببعض الخطوات:"
"twoFactorModalFirstStep" = "1. امسح رمز QR هذا في تطبيق المصادقة أو انسخ الرمز الموجود بجانب رمز QR والصقه في التطبيق"
"twoFactorModalSecondStep" = "2. أدخل الرمز من التطبيق"
"twoFactorModalRemoveStep" = "أدخل الرمز من التطبيق لإزالة المصادقة الثنائية."
"twoFactorModalChangeCredentialsTitle" = "تغيير بيانات الاعتماد"
"twoFactorModalChangeCredentialsStep" = "أدخل الرمز من التطبيق لتغيير بيانات اعتماد المسؤول."
"twoFactorModalSetSuccess" = "تم إنشاء المصادقة الثنائية بنجاح"
"twoFactorModalDeleteSuccess" = "تم حذف المصادقة الثنائية بنجاح"
"twoFactorModalError" = "رمز خاطئ"

[pages.settings.toasts]
"modifySettings" = "تم تغيير المعلمات."
"getSettings" = "حدث خطأ أثناء استرداد المعلمات."
"modifyUserError" = "حدث خطأ أثناء تغيير بيانات اعتماد المسؤول."
"modifyUser" = "لقد قمت بتغيير بيانات اعتماد المسؤول بنجاح."
"originalUserPassIncorrect" = "اسم المستخدم أو الباسورد الحالي غير صحيح"
"userPassMustBeNotEmpty" = "اسم المستخدم والباسورد الجديدين فاضيين"
"getOutboundTrafficError" = "خطأ في الحصول على حركات المرور الصادرة"
"resetOutboundTrafficError" = "خطأ في إعادة تعيين حركات المرور الصادرة"

[tgbot]
"keyboardClosed" = "❌ الكيبورد المخصص اتقفلت!"
"noResult" = "❗ مفيش نتيجة!"
"noQuery" = "❌ مش لاقي السؤال! استخدم الأمر تاني!"
"wentWrong" = "❌ حصل خطأ!"
"noIpRecord" = "❗ مفيش سجل IP!"
"noInbounds" = "❗ مفيش إدخال متواجد!"
"unlimited" = "♾ غير محدود (إعادة ضبط)"
"add" = "أضف"
"month" = "شهر"
"months" = "شهور"
"day" = "يوم"
"days" = "أيام"
"hours" = "ساعات"
"unknown" = "مش معروف"
"inbounds" = "الإدخالات"
"clients" = "العملاء"
"offline" = "🔴 أوفلاين"
"online" = "🟢 أونلاين"

[tgbot.commands]
"unknown" = "❗ أمر مش معروف."
"pleaseChoose" = "👇 من فضلك اختار:\r\n"
"help" = "🤖 أهلا بيك في البوت! البوت ده معمول عشان يديك بيانات معينة من البانل ويسمحلك بالتعديلات."
"start" = "👋 أهلا <i>{{ .Firstname }}</i>.\r\n"
"welcome" = "🤖 أهلا بيك في بوت إدارة <b>{{ .Hostname }}</b>.\r\n"
"status" = "✅ البوت شغال!"
"usage" = "❗ من فضلك ادخل نص للتبحث عنه!"
"getID" = "🆔 الـ ID بتاعك: <code>{{ .ID }}</code>"
"helpAdminCommands" = "عشان تعيد تشغيل Xray Core:\r\n<code>/restart</code>\r\n\r\nعشان تدور على إيميل عميل:\r\n<code>/usage [Email]</code>\r\n\r\nعشان تدور على إدخالات (مع إحصائيات العملاء):\r\n<code>/inbound [Remark]</code>\r\n\r\nID شات Telegram:\r\n<code>/id</code>"
"helpClientCommands" = "عشان تدور على الإحصائيات، استخدم الأمر ده:\r\n\r\n<code>/usage [Email]</code>\r\n\r\nID شات Telegram:\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ العملية نجحت!"
"restartFailed" = "❗ حصل خطأ في العملية.\r\n\r\n<code>Error: {{ .Error }}</code>."
"xrayNotRunning" = "❗ Xray Core مش شغال."
"startDesc" = "عرض القائمة الرئيسية"
"helpDesc" = "مساعدة البوت"
"statusDesc" = "التحقق من حالة البوت"
"idDesc" = "عرض معرف Telegram الخاص بك"

[tgbot.messages]
"cpuThreshold" = "🔴 حمل المعالج {{ .Percent }}% عدى الحد المسموح ({{ .Threshold }}%)"
"selectUserFailed" = "❌ حصل خطأ في اختيار المستخدم!"
"userSaved" = "✅ حفظت بيانات مستخدم Telegram."
"loginSuccess" = "✅ تسجيل الدخول للبانل تم بنجاح.\r\n"
"loginFailed" = "❗️فشل محاولة تسجيل الدخول للبانل.\r\n"
"report" = "🕰 التقارير المجدولة: {{ .RunTime }}\r\n"
"datetime" = "⏰ التاريخ والوقت: {{ .DateTime }}\r\n"
"hostname" = "💻 السيرفر: {{ .Hostname }}\r\n"
"version" = "🚀 نسخة 3X-UI: {{ .Version }}\r\n"
"xrayVersion" = "📡 نسخة Xray: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6: {{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4: {{ .IPv4 }}\r\n"
"ip" = "🌐 IP: {{ .IP }}\r\n"
"ips" = "🔢 عناوين IP:\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ وقت التشغيل: {{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 تحميل النظام: {{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 الرام: {{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 TCP: {{ .Count }}\r\n"
"udpCount" = "🔸 UDP: {{ .Count }}\r\n"
"traffic" = "🚦 الترافيك: {{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ الحالة: {{ .State }}\r\n"
"username" = "👤 اسم المستخدم: {{ .Username }}\r\n"
"password" = "👤 الباسورد: {{ .Password }}\r\n"
"time" = "⏰ الوقت: {{ .Time }}\r\n"
"inbound" = "📍 الإدخال: {{ .Remark }}\r\n"
"port" = "🔌 البورت: {{ .Port }}\r\n"
"expire" = "📅 تاريخ الانتهاء: {{ .Time }}\r\n"
"expireIn" = "📅 هيخلص بعد: {{ .Time }}\r\n"
"active" = "💡 مفعل: {{ .Enable }}\r\n"
"enabled" = "🚨 مفعل: {{ .Enable }}\r\n"
"online" = "🌐 حالة الاتصال: {{ .Status }}\r\n"
"email" = "📧 الإيميل: {{ .Email }}\r\n"
"upload" = "🔼 رفع: ↑{{ .Upload }}\r\n"
"download" = "🔽 تنزيل: ↓{{ .Download }}\r\n"
"total" = "📊 الإجمالي: ↑↓{{ .UpDown }} / {{ .Total }}\r\n"
"TGUser" = "👤 مستخدم Telegram: {{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 نفذ {{ .Type }}:\r\n"
"exhaustedCount" = "🚨 عدد النفاذ لـ {{ .Type }}:\r\n"
"onlinesCount" = "🌐 العملاء الأونلاين: {{ .Count }}\r\n"
"disabled" = "🛑 معطل: {{ .Disabled }}\r\n"
"depleteSoon" = "🔜 هينتهي قريب: {{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 وقت النسخة الاحتياطية: {{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 اتحدّث في: {{ .Time }}\r\n\r\n"
"yes" = "✅ أيوه"
"no" = "❌ لأ"

"received_id" = "🔑📥 الـ ID اتحدث."
"received_password" = "🔑📥 الباسورد اتحدث."
"received_email" = "📧📥 الإيميل اتحدث."
"received_comment" = "💬📥 التعليق اتحدث."
"id_prompt" = "🔑 الـ ID الافتراضي: {{ .ClientId }}\n\nادخل الـ ID بتاعك."
"pass_prompt" = "🔑 الباسورد الافتراضي: {{ .ClientPassword }}\n\nادخل الباسورد بتاعك."
"email_prompt" = "📧 الإيميل الافتراضي: {{ .ClientEmail }}\n\nادخل الإيميل بتاعك."
"comment_prompt" = "💬 التعليق الافتراضي: {{ .ClientComment }}\n\nادخل تعليقك."
"inbound_client_data_id" = "🔄 الدخول: {{ .InboundRemark }}\n\n🔑 المعرف: {{ .ClientId }}\n📧 البريد الإلكتروني: {{ .ClientEmail }}\n📊 الترافيك: {{ .ClientTraffic }}\n📅 تاريخ الانتهاء: {{ .ClientExp }}\n🌐 حدّ IP: {{ .IpLimit }}\n💬 تعليق: {{ .ClientComment }}\n\nدلوقتي تقدر تضيف العميل على الدخول!"
"inbound_client_data_pass" = "🔄 الدخول: {{ .InboundRemark }}\n\n🔑 كلمة المرور: {{ .ClientPass }}\n📧 البريد الإلكتروني: {{ .ClientEmail }}\n📊 الترافيك: {{ .ClientTraffic }}\n📅 تاريخ الانتهاء: {{ .ClientExp }}\n🌐 حدّ IP: {{ .IpLimit }}\n💬 تعليق: {{ .ClientComment }}\n\nدلوقتي تقدر تضيف العميل على الدخول!"
"cancel" = "❌ العملية اتلغت! \n\nممكن تبدأ من /start في أي وقت. 🔄"
"error_add_client" = "⚠️ حصل خطأ:\n\n {{ .error }}"
"using_default_value" = "تمام، هشيل على القيمة الافتراضية. 😊"
"incorrect_input" = "المدخلات مش صحيحة.\nالكلمات لازم تكون متصلة من غير فراغات.\nمثال صحيح: aaaaaa\nمثال غلط: aaa aaa 🚫"
"AreYouSure" = "إنت متأكد؟ 🤔"
"SuccessResetTraffic" = "📧 البريد الإلكتروني: {{ .ClientEmail }}\n🏁 النتيجة: ✅ تم بنجاح"
"FailedResetTraffic" = "📧 البريد الإلكتروني: {{ .ClientEmail }}\n🏁 النتيجة: ❌ فشل \n\n🛠️ الخطأ: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 عملية إعادة ضبط الترافيك خلصت لكل العملاء."


[tgbot.buttons]
"closeKeyboard" = "❌ اقفل الكيبورد"
"cancel" = "❌ إلغاء"
"cancelReset" = "❌ إلغاء إعادة الضبط"
"cancelIpLimit" = "❌ إلغاء حد الـ IP"
"confirmResetTraffic" = "✅ تأكيد إعادة ضبط الترافيك؟"
"confirmClearIps" = "✅ تأكيد مسح الـ IPs؟"
"confirmRemoveTGUser" = "✅ تأكيد حذف مستخدم Telegram؟"
"confirmToggle" = "✅ تأكيد تفعيل/تعطيل المستخدم؟"
"dbBackup" = "احصل على نسخة DB"
"serverUsage" = "استخدام السيرفر"
"getInbounds" = "احصل على الإدخالات"
"depleteSoon" = "هينتهي قريب"
"clientUsage" = "استخدام العميل"
"onlines" = "العملاء الأونلاين"
"commands" = "الأوامر"
"refresh" = "🔄 تجديد"
"clearIPs" = "❌ مسح الـ IPs"
"removeTGUser" = "❌ حذف مستخدم Telegram"
"selectTGUser" = "👤 اختار مستخدم Telegram"
"selectOneTGUser" = "👤 اختار مستخدم Telegram:"
"resetTraffic" = "📈 إعادة ضبط الترافيك"
"resetExpire" = "📅 تغيير تاريخ الانتهاء"
"ipLog" = "🔢 سجل الـ IP"
"ipLimit" = "🔢 حد الـ IP"
"setTGUser" = "👤 ضبط مستخدم Telegram"
"toggle" = "🔘 تفعيل / تعطيل"
"custom" = "🔢 مخصص"
"confirmNumber" = "✅ تأكيد: {{ .Num }}"
"confirmNumberAdd" = "✅ تأكيد إضافة: {{ .Num }}"
"limitTraffic" = "🚧 حد الترافيك"
"getBanLogs" = "احصل على سجلات الحظر"
"allClients" = "كل العملاء"

"addClient" = "إضافة عميل"
"submitDisable" = "إرسال كمعطّل ☑️"
"submitEnable" = "إرسال كمفعّل ✅"
"use_default" = "🏷️ استخدام الإعدادات الافتراضية"
"change_id" = "⚙️🔑 المعرّف"
"change_password" = "⚙️🔑 كلمة السر"
"change_email" = "⚙️📧 البريد الإلكتروني"
"change_comment" = "⚙️💬 تعليق"
"ResetAllTraffics" = "إعادة ضبط جميع الترافيك"
"SortedTrafficUsageReport" = "تقرير استخدام الترافيك المرتب"


[tgbot.answers]
"successfulOperation" = "✅ العملية نجحت!"
"errorOperation" = "❗ حصل خطأ في العملية."
"getInboundsFailed" = "❌ فشل الحصول على الإدخالات."
"getClientsFailed" = "❌ فشل الحصول على العملاء."
"canceled" = "❌ {{ .Email }}: العملية اتلغت."
"clientRefreshSuccess" = "✅ {{ .Email }}: العميل اتحدث بنجاح."
"IpRefreshSuccess" = "✅ {{ .Email }}: الـ IPs اتحدثت بنجاح."
"TGIdRefreshSuccess" = "✅ {{ .Email }}: مستخدم Telegram اتحدث بنجاح."
"resetTrafficSuccess" = "✅ {{ .Email }}: الترافيك اتظبط بنجاح."
"setTrafficLimitSuccess" = "✅ {{ .Email }}: حد الترافيك اتسجل بنجاح."
"expireResetSuccess" = "✅ {{ .Email }}: أيام الانتهاء اتظبطت بنجاح."
"resetIpSuccess" = "✅ {{ .Email }}: حد الـ IP ({{ .Count }}) اتسجل بنجاح."
"clearIpSuccess" = "✅ {{ .Email }}: الـ IPs اتمسحت بنجاح."
"getIpLog" = "✅ {{ .Email }}: سجل الـ IP اتجاب."
"getUserInfo" = "✅ {{ .Email }}: بيانات مستخدم Telegram اتجاب."
"removedTGUserSuccess" = "✅ {{ .Email }}: مستخدم Telegram اتحذف بنجاح."
"enableSuccess" = "✅ {{ .Email }}: اتفعل بنجاح."
"disableSuccess" = "✅ {{ .Email }}: اتعطل بنجاح."
"askToAddUserId" = "مافيش إعدادات ليك!\r\nاطلب من الأدمن يضيف الـ Telegram ChatID الخاص بيك في إعداداتك.\r\n\r\nالـ ChatID بتاعك: <code>{{ .TgUserID }}</code>"
"chooseClient" = "اختار عميل للإدخال {{ .Inbound }}"
"chooseInbound" = "اختار الإدخال"
