{{define "form/dokodemo"}}
<a-form :colon="false" :label-col="{ md: {span:8} }" :wrapper-col="{ md: {span:14} }">
    <a-form-item label='{{ i18n "pages.inbounds.targetAddress"}}'>
        <a-input v-model.trim="inbound.settings.address"></a-input>
    </a-form-item>
    <a-form-item label='{{ i18n "pages.inbounds.destinationPort"}}'>
        <a-input-number v-model.number="inbound.settings.port"></a-input-number>
    </a-form-item>
    <a-form-item label='{{ i18n "pages.inbounds.network"}}'>
        <a-select v-model="inbound.settings.network" :dropdown-class-name="themeSwitcher.currentTheme">
            <a-select-option value="tcp,udp">TCP,UDP</a-select-option>
            <a-select-option value="tcp">TCP</a-select-option>
            <a-select-option value="udp">UDP</a-select-option>
        </a-select>
    </a-form-item>           
    <a-form-item label='Follow Redirect'>
        <a-switch v-model="inbound.settings.followRedirect"></a-switch>
    </a-form-item>
</a-form>
{{end}}
