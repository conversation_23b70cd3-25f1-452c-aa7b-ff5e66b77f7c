{{define "component/customStatistic"}}
<template>
    <a-statistic :title="title" :value="value">
        <template #prefix>
            <slot name="prefix"></slot>
        </template>
        <template #suffix>
            <slot name="suffix"></slot>
        </template>
    </a-statistic>
</template>
{{end}}

{{define "component/aCustomStatistic"}}
<style>
  .dark .ant-statistic-content {
    color: var(--dark-color-text-primary)
  }
  .dark .ant-statistic-title {
    color: rgba(255, 255, 255, 0.55)
  }
  .ant-statistic-content {
    font-size: 16px;
  }
</style>

<script>
  Vue.component('a-custom-statistic', {
    props: {
      'title': {
        type: String,
        required: false,
      },
      'value': {
        type: String,
        required: false
      }
    },
    template: `{{template "component/customStatistic"}}`,
  });
</script>
{{end}}