# Baglan-panel

<p align="center">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="./media/baglan.jpg">
    <img alt="Baglan-panel" src="./media/baglan.jpg" width="300">
  </picture>
</p>

**Baglan-panel** — Türkmen dili üçin <PERSON>, Xray-core serweri dolandyrmak üçin döredilen ösen, açyk çeşmeli web esasly dolandyryş paneli. Dürli VPN we proksi protokollaryny sazlamak we gözegçilik etmek üçin ulanyjy üçin amatly interfeýs hödürleýär.

> [!MÖHÜM]
> Bu taslama diňe şahsy ulanyş üçin, bunu bikanun maksatlar üçin ulanmaň, önümçilik gurşawynda ulanmaň.

Asyl X-UI taslamasynyň ösdürilen şahasy hökmünde, Baglan-panel has gowulaşdy<PERSON><PERSON> durnuklylyk, has giň protokol goldawy we goşmaça aýratynlyklar hödürleýär.

## Çalt başlamak

```bash
# Gurnamak üçin:
bash <(curl -Ls https://raw.githubusercontent.com/mhsanaei/3x-ui/master/install.sh)
```

## Aýratynlyklar

- **Türkmen dili goldawy** - Doly Türkmen dilinde interfeýs
- **Öýlenen tema** - Baglan temasyna laýyk gelýän reňk shemasy
- **Ulanyjy üçin amatly** - Ýönekeý we düşnükli interfeýs
- **Köp protokol goldawy** - Dürli VPN we proksi protokollary
- **Howpsuzlyk** - Ösen howpsuzlyk aýratynlyklary

## Gurnamak

1. Ulgamyňyzda Go programma diliniň gurlandygyna göz ýetiriň
2. Taslama faýllaryny ýükläň
3. Gerekli baglylyklary guruň: `go mod tidy`
4. Taslamany işlediň: `go run main.go`

## Ulanyş

Panel deslapky boýunça 54321 portunda işleýär. Web brauzeriňizde `http://localhost:54321` salgysynda açyň.

Deslapky giriş maglumatlary:
- Ulanyjy ady: admin
- Açar sözi: admin

## Goldaw

Bu taslama 3x-ui taslamasynyň esasynda döredildi we Türkmen dili üçin öýlendi.

## A Special Thanks to

- [alireza0](https://github.com/alireza0/)

## Acknowledgment

- [Iran v2ray rules](https://github.com/chocolate4u/Iran-v2ray-rules) (License: **GPL-3.0**): _Enhanced v2ray/xray and v2ray/xray-clients routing rules with built-in Iranian domains and a focus on security and adblocking._
- [Russia v2ray rules](https://github.com/runetfreedom/russia-v2ray-rules-dat) (License: **GPL-3.0**): _This repository contains automatically updated V2Ray routing rules based on data on blocked domains and addresses in Russia._

## Support project

**If this project is helpful to you, you may wish to give it a**:star2:

<p align="left">
  <a href="https://buymeacoffee.com/mhsanaei" target="_blank">
    <img src="./media/buymeacoffe.png" alt="Image">
  </a>
</p>

- USDT (TRC20): `TXncxkvhkDWGts487Pjqq1qT9JmwRUz8CC`
- MATIC (polygon): `0x41C9548675D044c6Bfb425786C765bc37427256A`
- LTC (Litecoin): `ltc1q2ach7x6d2zq0n4l0t4zl7d7xe2s6fs7a3vspwv`

## Stargazers over Time

[![Stargazers over time](https://starchart.cc/MHSanaei/3x-ui.svg?variant=adaptive)](https://starchart.cc/MHSanaei/3x-ui)
