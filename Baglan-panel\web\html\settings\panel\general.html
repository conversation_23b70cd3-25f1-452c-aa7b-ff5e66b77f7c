{{define "settings/panel/general"}}
<a-collapse default-active-key="1">
    <a-collapse-panel key="1" header='{{ i18n "pages.xray.generalConfigs"}}'>
        <a-setting-list-item paddings="small">
            <template #title>
                {{ i18n "pages.settings.remarkModel"}}
            </template>
            <template #description>
                {{ i18n "pages.settings.sampleRemark"}}: <i>#[[ remarkSample ]]</i>
            </template>
            <template #control>
                <a-input-group :style="{ width: '100%' }">
                    <a-select :style="{ paddingRight: '.5rem', minWidth: '80%', width: 'auto' }" mode="multiple"
                        v-model="remarkModel" :dropdown-class-name="themeSwitcher.currentTheme">
                        <a-select-option v-for="(value, key) in remarkModels" :value="key">[[ value ]]</a-select-option>
                    </a-select>
                    <a-select :style="{ width: '20%' }" v-model="remarkSeparator"
                        :dropdown-class-name="themeSwitcher.currentTheme">
                        <a-select-option v-for="key in remarkSeparators" :value="key">[[ key ]]</a-select-option>
                    </a-select>
                </a-input-group>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.panelListeningIP"}}</template>
            <template #description>{{ i18n "pages.settings.panelListeningIPDesc"}}</template>
            <template #control>
                <a-input type="text" v-model="allSetting.webListen"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.panelListeningDomain"}}</template>
            <template #description>{{ i18n "pages.settings.panelListeningDomainDesc"}}</template>
            <template #control>
                <a-input type="text" v-model="allSetting.webDomain"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.panelPort"}}</template>
            <template #description>{{ i18n "pages.settings.panelPortDesc"}}</template>
            <template #control>
                <a-input-number :min="1" :min="65531" v-model="allSetting.webPort" :style="{ width: '100%' }"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.panelUrlPath"}}</template>
            <template #description>{{ i18n "pages.settings.panelUrlPathDesc"}}</template>
            <template #control>
                <a-input type="text" v-model="allSetting.webBasePath"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.sessionMaxAge" }}</template>
            <template #description>{{ i18n "pages.settings.sessionMaxAgeDesc" }}</template>
            <template #control>
                <a-input-number :min="60" v-model="allSetting.sessionMaxAge" :style="{ width: '100%' }"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.pageSize" }}</template>
            <template #description>{{ i18n "pages.settings.pageSizeDesc" }}</template>
            <template #control>
                <a-input-number :min="0" step="5" v-model="allSetting.pageSize" :style="{ width: '100%' }"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.language"}}</template>
            <template #control>
                <a-select ref="selectLang" v-model="lang" @change="LanguageManager.setLanguage(lang)"
                    :dropdown-class-name="themeSwitcher.currentTheme" :style="{ width: '100%' }">
                    <a-select-option :value="l.value" :label="l.value" v-for="l in LanguageManager.supportedLanguages">
                        <span role="img" :aria-label="l.name" v-text="l.icon"></span> &nbsp;&nbsp; <span
                            v-text="l.name"></span>
                    </a-select-option>
                </a-select>
            </template>
        </a-setting-list-item>
    </a-collapse-panel>
    <a-collapse-panel key="2" header='{{ i18n "pages.settings.notifications" }}'>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.expireTimeDiff" }}</template>
            <template #description>{{ i18n "pages.settings.expireTimeDiffDesc" }}</template>
            <template #control>
                <a-input-number :min="0" v-model="allSetting.expireDiff" :style="{ width: '100%' }"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.trafficDiff" }}</template>
            <template #description>{{ i18n "pages.settings.trafficDiffDesc" }}</template>
            <template #control>
                <a-input-number :min="0" v-model="allSetting.trafficDiff" :style="{ width: '100%' }"></a-input>
            </template>
        </a-setting-list-item>
    </a-collapse-panel>
    <a-collapse-panel key="3" header='{{ i18n "pages.settings.certs" }}'>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.publicKeyPath"}}</template>
            <template #description>{{ i18n "pages.settings.publicKeyPathDesc"}}</template>
            <template #control>
                <a-input type="text" v-model="allSetting.webCertFile"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.privateKeyPath"}}</template>
            <template #description>{{ i18n "pages.settings.privateKeyPathDesc"}}</template>
            <template #control>
                <a-input type="text" v-model="allSetting.webKeyFile"></a-input>
            </template>
        </a-setting-list-item>
    </a-collapse-panel>
    <a-collapse-panel key="4" header='{{ i18n "pages.settings.externalTraffic" }}'>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.externalTrafficInformEnable"}}</template>
            <template #description>{{ i18n "pages.settings.externalTrafficInformEnableDesc"}}</template>
            <template #control>
                <a-switch v-model="allSetting.externalTrafficInformEnable"></a-switch>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.externalTrafficInformURI"}}</template>
            <template #description>{{ i18n "pages.settings.externalTrafficInformURIDesc"}}</template>
            <template #control>
                <a-input type="text" placeholder="(http|https)://domain[:port]/path/"
                    v-model="allSetting.externalTrafficInformURI"></a-input>
            </template>
        </a-setting-list-item>
    </a-collapse-panel>
    <a-collapse-panel key="5" header='{{ i18n "pages.settings.dateAndTime" }}'>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.timeZone"}}</template>
            <template #description>{{ i18n "pages.settings.timeZoneDesc"}}</template>
            <template #control>
                <a-input type="text" v-model="allSetting.timeLocation"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.datepicker"}}</template>
            <template #description>{{ i18n "pages.settings.datepickerDescription"}}</template>
            <template #control>
                <a-select :style="{ width: '100%' }" :dropdown-class-name="themeSwitcher.currentTheme" v-model="datepicker">
                    <a-select-option v-for="item in datepickerList" :value="item.value">
                        <span v-text="item.name"></span>
                    </a-select-option>
                </a-select>
            </template>
        </a-setting-list-item>
    </a-collapse-panel>
</a-collapse>
{{end}}