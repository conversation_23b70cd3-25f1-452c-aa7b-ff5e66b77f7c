"username" = "Ім'я користувача"
"password" = "Пароль"
"login" = "Увійти"
"confirm" = "Підтвердити"
"cancel" = "Скасувати"
"close" = "Закрити"
"create" = "Створити"
"update" = "Оновити"
"copy" = "Копіювати"
"copied" = "Скопійовано"
"download" = "Завантажити"
"remark" = "Примітка"
"enable" = "Увімкнути"
"protocol" = "Протокол"
"search" = "Пошук"
"filter" = "Фільтр"
"loading" = "Завантаження..."
"second" = "Секунда"
"minute" = "Хвилина"
"hour" = "Година"
"day" = "День"
"check" = "Перевірка"
"indefinite" = "Безстроково"
"unlimited" = "Безлімітний"
"none" = "Немає"
"qrCode" = "QR-Код"
"info" = "Більше інформації"
"edit" = "Редагувати"
"delete" = "Видалити"
"reset" = "Скидання"
"noData" = "Немає даних."
"copySuccess" = "Скопійовано успішно"
"sure" = "Звичайно"
"encryption" = "Шифрування"
"useIPv4ForHost" = "Використовувати IPv4 для хоста"
"transmission" = "Протокол передачи"
"host" = "Хост"
"path" = "Шлях"
"camouflage" = "Маскування"
"status" = "Статус"
"enabled" = "Увімкнено"
"disabled" = "Вимкнено"
"depleted" = "Вичерпано"
"depletingSoon" = "Вичерпується"
"offline" = "Офлайн"
"online" = "Онлайн"
"domainName" = "Доменне ім`я"
"monitor" = "Слухати IP"
"certificate" = "Цифровий сертифікат"
"fail" = "Помилка"
"comment" = "Коментар"
"success" = "Успішно"
"getVersion" = "Отримати версію"
"install" = "Встановити"
"clients" = "Клієнти"
"usage" = "Використання"
"twoFactorCode" = "Код"
"remained" = "Залишилося"
"security" = "Беспека"
"secAlertTitle" = "Попередження системи безпеки"
"secAlertSsl" = "Це з'єднання не є безпечним. Будь ласка, уникайте введення конфіденційної інформації, поки TLS не буде активовано для захисту даних."
"secAlertConf" = "Деякі налаштування вразливі до атак. Рекомендується посилити протоколи безпеки, щоб запобігти можливим порушенням."
"secAlertSSL" = "Панель не має безпечного з'єднання. Будь ласка, встановіть сертифікат TLS для захисту даних."
"secAlertPanelPort" = "Стандартний порт панелі вразливий. Будь ласка, сконфігуруйте випадковий або конкретний порт."
"secAlertPanelURI" = "Стандартний URI-шлях панелі небезпечний. Будь ласка, сконфігуруйте складний URI-шлях."
"secAlertSubURI" = "Стандартний URI-шлях підписки небезпечний. Будь ласка, сконфігуруйте складний URI-шлях."
"secAlertSubJsonURI" = "Стандартний URI-шлях JSON підписки небезпечний. Будь ласка, сконфігуруйте складний URI-шлях."
"emptyDnsDesc" = "Немає доданих DNS-серверів."
"emptyFakeDnsDesc" = "Немає доданих Fake DNS-серверів."
"emptyBalancersDesc" = "Немає доданих балансувальників."
"emptyReverseDesc" = "Немає доданих зворотних проксі."
"somethingWentWrong" = "Щось пішло не так"

[menu]
"theme" = "Тема"
"dark" = "Темна"
"ultraDark" = "Ультра темна"
"dashboard" = "Огляд"
"inbounds" = "Вхідні"
"settings" = "Параметри панелі"
"xray" = "Конфігурації Xray"
"logout" = "Вийти"
"link" = "Керувати"

[pages.login]
"hello" = "Привіт"
"title" = "Ласкаво просимо"
"loginAgain" = "Ваш сеанс закінчився, увійдіть знову"

[pages.login.toasts]
"invalidFormData" = "Формат вхідних даних недійсний."
"emptyUsername" = "Потрібне ім'я користувача"
"emptyPassword" = "Потрібен пароль"
"wrongUsernameOrPassword" = "Невірне ім’я користувача, пароль або код двофакторної аутентифікації."  
"successLogin" = "Ви успішно увійшли до свого облікового запису."

[pages.index]
"title" = "Огляд"
"cpu" = "ЦП"
"logicalProcessors" = "Логічні процесори"
"frequency" = "Частота"
"swap" = "Своп"
"storage" = "Сховище"
"memory" = "ОЗП"
"threads" = "Потоки"
"xrayStatus" = "Xray"
"stopXray" = "Зупинити"
"restartXray" = "Перезапустити"
"xraySwitch" = "Версія"
"xraySwitchClick" = "Виберіть версію, на яку ви хочете перейти."
"xraySwitchClickDesk" = "Вибирайте уважно, оскільки старіші версії можуть бути несумісними з поточними конфігураціями."
"xrayStatusUnknown" = "Невідомо"
"xrayStatusRunning" = "Запущено"
"xrayStatusStop" = "Зупинено"
"xrayStatusError" = "Помилка"
"xrayErrorPopoverTitle" = "Під час роботи Xray сталася помилка"
"operationHours" = "Час роботи"
"systemLoad" = "Завантаження системи"
"systemLoadDesc" = "Середнє завантаження системи за останні 1, 5 і 15 хвилин"
"connectionTcpCountDesc" = "Загальна кількість TCP-з'єднань у системі"
"connectionUdpCountDesc" = "Загальна кількість UDP-з'єднань у системі"
"connectionCount" = "Статистика з'єднання"
"ipAddresses" = "IP-адреси"
"toggleIpVisibility" = "Перемкнути видимість IP"
"overallSpeed" = "Загальна швидкість"
"upload" = "Відправка"
"download" = "Завантаження"
"totalData" = "Загальний обсяг даних"
"sent" = "Відправлено"
"received" = "Отримано"
"documentation" = "Документація"
"xraySwitchVersionDialog" = "Ви дійсно хочете змінити версію Xray?"
"xraySwitchVersionDialogDesc" = "Це змінить версію Xray на #version#."
"xraySwitchVersionPopover" = "Xray успішно оновлено"
"geofileUpdateDialog" = "Ви дійсно хочете оновити геофайл?"
"geofileUpdateDialogDesc" = "Це оновить файл #filename#."
"geofileUpdatePopover" = "Геофайл успішно оновлено"
"dontRefresh" = "Інсталяція триває, будь ласка, не оновлюйте цю сторінку"
"logs" = "Журнали"
"config" = "Конфігурація"
"backup" = "Резервна копія"
"backupTitle" = "Резервне копіювання та відновлення бази даних"
"exportDatabase" = "Резервна копія"
"exportDatabaseDesc" = "Натисніть, щоб завантажити файл .db, що містить резервну копію вашої поточної бази даних на ваш пристрій."
"importDatabase" = "Відновити"
"importDatabaseDesc" = "Натисніть, щоб вибрати та завантажити файл .db з вашого пристрою для відновлення бази даних з резервної копії."
"importDatabaseSuccess" = "Базу даних успішно імпортовано"
"importDatabaseError" = "Виникла помилка під час імпорту бази даних"
"readDatabaseError" = "Виникла помилка під час читання бази даних"
"getDatabaseError" = "Виникла помилка під час отримання бази даних"
"getConfigError" = "Виникла помилка під час отримання файлу конфігурації"

[pages.inbounds]
"title" = "Вхідні"
"totalDownUp" = "Всього надісланих/отриманих"
"totalUsage" = "Всього використанно"
"inboundCount" = "Загальна кількість вхідних"
"operate" = "Меню"
"enable" = "Увімкнено"
"remark" = "Примітка"
"protocol" = "Протокол"
"port" = "Порт"
"traffic" = "Трафік"
"details" = "Деталі"
"transportConfig" = "Транспорт"
"expireDate" = "Тривалість"
"resetTraffic" = "Скинути трафік"
"addInbound" = "Додати вхідний"
"generalActions" = "Загальні дії"
"autoRefresh" = "Автооновлення"
"autoRefreshInterval" = "Інтервал"
"modifyInbound" = "Змінити вхідний"
"deleteInbound" = "Видалити вхідні"
"deleteInboundContent" = "Ви впевнені, що хочете видалити вхідні?"
"deleteClient" = "Видалити клієнта"
"deleteClientContent" = "Ви впевнені, що хочете видалити клієнт?"
"resetTrafficContent" = "Ви впевнені, що хочете скинути трафік?"
"inboundUpdateSuccess" = "Вхідне підключення успішно оновлено."
"inboundCreateSuccess" = "Вхідне підключення успішно створено."
"copyLink" = "Копіювати URL"
"address" = "Адреса"
"network" = "Мережа"
"destinationPort" = "Порт призначення"
"targetAddress" = "Цільова адреса"
"monitorDesc" = "Залиште порожнім, щоб слухати всі IP-адреси"
"meansNoLimit" = "= Необмежено. (одиниця: ГБ)"
"totalFlow" = "Загальна витрата"
"leaveBlankToNeverExpire" = "Залиште порожнім, щоб ніколи не закінчувався"
"noRecommendKeepDefault" = "Рекомендується зберегти значення за замовчуванням"
"certificatePath" = "Шлях до файлу"
"certificateContent" = "Вміст файлу"
"publicKey" = "Публічний ключ"
"privatekey" = "Закритий ключ"
"clickOnQRcode" = "Натисніть QR-код, щоб скопіювати"
"client" = "Клієнт"
"export" = "Експортувати всі URL-адреси"
"clone" = "Клон"
"cloneInbound" = "Клонувати"
"cloneInboundContent" = "Усі налаштування цього вхідного потоку, крім порту, IP-адреси прослуховування та клієнтів, будуть застосовані до клону."
"cloneInboundOk" = "Клонувати"
"resetAllTraffic" = "Скинути весь вхідний трафік"
"resetAllTrafficTitle" = "Скинути весь вхідний трафік"
"resetAllTrafficContent" = "Ви впевнені, що бажаєте скинути трафік усіх вхідних?"
"resetInboundClientTraffics" = "Скинути трафік клієнтів"
"resetInboundClientTrafficTitle" = "Скинути трафік клієнтів"
"resetInboundClientTrafficContent" = "Ви впевнені, що бажаєте скинути трафік клієнтів цього вхідного потоку?"
"resetAllClientTraffics" = "Скинути весь трафік клієнтів"
"resetAllClientTrafficTitle" = "Скинути весь трафік клієнтів"
"resetAllClientTrafficContent" = "Ви впевнені, що бажаєте скинути трафік усіх клієнтів?"
"delDepletedClients" = "Видалити вичерпані клієнти"
"delDepletedClientsTitle" = "Видалити вичерпані клієнти"
"delDepletedClientsContent" = "Ви впевнені, що хочете видалити всі вичерпані клієнти?"
"email" = "Електронна пошта"
"emailDesc" = "Будь ласка, надайте унікальну адресу електронної пошти."
"IPLimit" = "Обмеження IP"
"IPLimitDesc" = "Вимикає вхідний, якщо кількість перевищує встановлене значення. (0 = вимкнено)"
"IPLimitlog" = "Журнал IP"
"IPLimitlogDesc" = "Журнал історії IP-адрес. (щоб увімкнути вхідну після вимкнення, очистіть журнал)"
"IPLimitlogclear" = "Очистити журнал"
"setDefaultCert" = "Установити сертифікат з панелі"
"telegramDesc" = "Будь ласка, вкажіть ID чату Telegram. (використовуйте команду '/id' у боті) або (@userinfobot)"
"subscriptionDesc" = "Щоб знайти URL-адресу вашої підписки, перейдіть до «Деталі». Крім того, ви можете використовувати одне ім'я для кількох клієнтів."
"info" = "Інформація"
"same" = "Те саме"
"inboundData" = "Вхідні дані"
"exportInbound" = "Експортувати вхідні"
"import" = "Імпорт"
"importInbound" = "Імпортувати вхідний"

[pages.client]
"add" = "Додати клієнта"
"edit" = "Редагувати клієнта"
"submitAdd" = "Додати клієнта"
"submitEdit" = "Зберегти зміни"
"clientCount" = "Кількість клієнтів"
"bulk" = "Додати групу"
"method" = "Метод"
"first" = "Перший"
"last" = "Останній"
"prefix" = "Префікс"
"postfix" = "Постфікс"
"delayedStart" = "Початок використання"
"expireDays" = "Тривалість"
"days" = "Дні(в)"
"renew" = "Автоматичне оновлення"
"renewDesc" = "Автоматичне поновлення після закінчення терміну дії. (0 = вимкнено)(одиниця: день)"

[pages.inbounds.toasts]
"obtain" = "Отримати"
"updateSuccess" = "Оновлення пройшло успішно"
"logCleanSuccess" = "Журнал очищено"
"inboundsUpdateSuccess" = "Вхідні підключення успішно оновлено"
"inboundUpdateSuccess" = "Вхідне підключення успішно оновлено"
"inboundCreateSuccess" = "Вхідне підключення успішно створено"
"inboundDeleteSuccess" = "Вхідне підключення успішно видалено"
"inboundClientAddSuccess" = "Клієнт(и) вхідного підключення додано"
"inboundClientDeleteSuccess" = "Клієнта вхідного підключення видалено"
"inboundClientUpdateSuccess" = "Клієнта вхідного підключення оновлено"
"delDepletedClientsSuccess" = "Усі вичерпані клієнти видалені"
"resetAllClientTrafficSuccess" = "Весь трафік клієнта скинуто"
"resetAllTrafficSuccess" = "Весь трафік скинуто"
"resetInboundClientTrafficSuccess" = "Трафік скинуто"
"trafficGetError" = "Помилка отримання даних про трафік"
"getNewX25519CertError" = "Помилка при отриманні сертифіката X25519."

[pages.inbounds.stream.general]
"request" = "Запит"
"response" = "Відповідь"
"name" = "Ім'я"
"value" = "Значення"

[pages.inbounds.stream.tcp]
"version" = "Версія"
"method" = "Метод"
"path" = "Шлях"
"status" = "Статус"
"statusDescription" = "Опис стану"
"requestHeader" = "Заголовок запиту"
"responseHeader" = "Заголовок відповіді"

[pages.settings]
"title" = "Параметри панелі"
"save" = "Зберегти"
"infoDesc" = "Кожна внесена тут зміна повинна бути збережена. Перезапустіть панель, щоб застосувати зміни."
"restartPanel" = "Перезапустити панель"
"restartPanelDesc" = "Ви впевнені, що бажаєте перезапустити панель? Якщо ви не можете отримати доступ до панелі після перезапуску, будь ласка, перегляньте інформацію журналу панелі на сервері."
"restartPanelSuccess" = "Панель успішно перезапущено"
"actions" = "Дії"
"resetDefaultConfig" = "Відновити значення за замовчуванням"
"panelSettings" = "Загальні"
"securitySettings" = "Автентифікація"
"TGBotSettings" = "Telegram Бот"
"panelListeningIP" = "Слухати IP"
"panelListeningIPDesc" = "IP-адреса для веб-панелі. (залиште порожнім, щоб слухати всі IP-адреси)"
"panelListeningDomain" = "Домен прослуховування"
"panelListeningDomainDesc" = "Доменне ім'я для веб-панелі. (залиште порожнім, щоб слухати всі домени та IP-адреси)"
"panelPort" = "Порт прослуховування"
"panelPortDesc" = "Номер порту для веб-панелі. (має бути невикористаний порт)"
"publicKeyPath" = "Шлях відкритого ключа"
"publicKeyPathDesc" = "Шлях до файлу відкритого ключа для веб-панелі. (починається з ‘/‘)"
"privateKeyPath" = "Шлях приватного ключа"
"privateKeyPathDesc" = "Шлях до файлу приватного ключа для веб-панелі. (починається з ‘/‘)"
"panelUrlPath" = "Шлях URL"
"panelUrlPathDesc" = "Шлях URL для веб-панелі. (починається з ‘/‘ і закінчується ‘/‘)"
"pageSize" = "Розмір сторінки"
"pageSizeDesc" = "Визначити розмір сторінки для вхідної таблиці. (0 = вимкнено)"
"remarkModel" = "Модель зауваження та роздільний символ"
"datepicker" = "Тип календаря"
"datepickerPlaceholder" = "Виберіть дату"
"datepickerDescription" = "Заплановані завдання виконуватимуться на основі цього календаря."
"sampleRemark" = "Зразок зауваження"
"oldUsername" = "Поточне ім'я користувача"
"currentPassword" = "Поточний пароль"
"newUsername" = "Нове ім'я користувача"
"newPassword" = "Новий пароль"
"telegramBotEnable" = "Увімкнути Telegram Bot"
"telegramBotEnableDesc" = "Вмикає бота Telegram."
"telegramToken" = "Telegram Токен"
"telegramTokenDesc" = "Токен бота Telegram, отриманий від '@BotFather'."
"telegramProxy" = "SOCKS Проксі"
"telegramProxyDesc" = "Вмикає проксі-сервер SOCKS5 для підключення до Telegram. (відкоригуйте параметри відповідно до посібника)"
"telegramAPIServer" = "Сервер Telegram API"
"telegramAPIServerDesc" = "Сервер Telegram API для використання. Залиште поле порожнім, щоб використовувати сервер за умовчанням."
"telegramChatId" = "Ідентифікатор чату адміністратора"
"telegramChatIdDesc" = "Ідентифікатори чату адміністратора Telegram. (розділені комами) (отримайте тут @userinfobot) або (використовуйте команду '/id' у боті)"
"telegramNotifyTime" = "Час сповіщення"
"telegramNotifyTimeDesc" = "Час повідомлення бота Telegram, встановлений для періодичних звітів. (використовуйте формат часу crontab)"
"tgNotifyBackup" = "Резервне копіювання бази даних"
"tgNotifyBackupDesc" = "Надіслати файл резервної копії бази даних зі звітом."
"tgNotifyLogin" = "Сповіщення про вхід"
"tgNotifyLoginDesc" = "Отримувати сповіщення про ім'я користувача, IP-адресу та час щоразу, коли хтось намагається увійти у вашу веб-панель."
"sessionMaxAge" = "Тривалість сеансу"
"sessionMaxAgeDesc" = "Тривалість, протягом якої ви можете залишатися в системі. (одиниця: хвилина)"
"expireTimeDiff" = "Повідомлення про дату закінчення"
"expireTimeDiffDesc" = "Отримувати сповіщення про термін дії при досягненні цього порогу. (одиниця: день)"
"trafficDiff" = "Повідомлення про обмеження трафіку"
"trafficDiffDesc" = "Отримувати сповіщення про обмеження трафіку при досягненні цього порогу. (одиниця: ГБ)"
"tgNotifyCpu" = "Сповіщення про завантаження ЦП"
"tgNotifyCpuDesc" = "Отримувати сповіщення, якщо навантаження ЦП перевищує це порогове значення. (одиниця: %)"
"timeZone" = "Часовий пояс"
"timeZoneDesc" = "Заплановані завдання виконуватимуться на основі цього часового поясу."
"subSettings" = "Підписка"
"subEnable" = "Увімкнути службу підписки"
"subEnableDesc" = "Вмикає службу підписки."
"subTitle" = "Назва Підписки"
"subTitleDesc" = "Назва, яка відображається у VPN-клієнті"
"subListen" = "Слухати IP"
"subListenDesc" = "IP-адреса для служби підписки. (залиште порожнім, щоб слухати всі IP-адреси)"
"subPort" = "Слухати порт"
"subPortDesc" = "Номер порту для служби підписки. (має бути невикористаний порт)"
"subCertPath" = "Шлях відкритого ключа"
"subCertPathDesc" = "Шлях до файлу відкритого ключа для служби підписки. (починається з ‘/‘)"
"subKeyPath" = "Шлях приватного ключа"
"subKeyPathDesc" = "Шлях до файлу приватного ключа для служби підписки. (починається з ‘/‘)"
"subPath" = "Шлях URI"
"subPathDesc" = "Шлях URI для служби підписки. (починається з ‘/‘ і закінчується ‘/‘)"
"subDomain" = "Домен прослуховування"
"subDomainDesc" = "Ім'я домену для служби підписки. (залиште порожнім, щоб слухати всі домени та IP-адреси)"
"subUpdates" = "Інтервали оновлення"
"subUpdatesDesc" = "Інтервали оновлення URL-адреси підписки в клієнтських програмах. (одиниця: година)"
"subEncrypt" = "Закодувати"
"subEncryptDesc" = "Повернений вміст послуги підписки матиме кодування Base64."
"subShowInfo" = "Показати інформацію про використання"
"subShowInfoDesc" = "Залишок трафіку та дата відображатимуться в клієнтських програмах."
"subURI" = "URI зворотного проксі"
"subURIDesc" = "URI до URL-адреси підписки для використання за проксі."
"externalTrafficInformEnable" = "Інформація про зовнішній трафік"
"externalTrafficInformEnableDesc" = "Інформувати зовнішній API про кожне оновлення трафіку."
"externalTrafficInformURI" = "Інформаційний URI зовнішнього трафіку"
"externalTrafficInformURIDesc" = "Оновлення трафіку надсилаються на цей URI."
"fragment" = "Фрагментація"
"fragmentDesc" = "Увімкнути фрагментацію для пакету привітання TLS"
"fragmentSett" = "Параметри фрагментації"
"noisesDesc" = "Увімкнути Noises."
"noisesSett" = "Налаштування Noises"
"mux" = "Mux"
"muxDesc" = "Передавати кілька незалежних потоків даних у межах встановленого потоку даних."
"muxSett" = "Налаштування Mux"
"direct" = "Пряме підключення"
"directDesc" = "Безпосередньо встановлює з’єднання з доменами або діапазонами IP певної країни."
"notifications" = "Сповіщення"
"certs" = "Сертифікати"
"externalTraffic" = "Зовнішній трафік"
"dateAndTime" = "Дата та час"
"proxyAndServer" = "Проксі та сервер"
"intervals" = "Інтервали"
"information" = "Інформація"
"language" = "Мова"
"telegramBotLanguage" = "Мова Telegram-бота"

[pages.xray]
"title" = "Xray конфігурації"
"save" = "Зберегти"
"restart" = "Перезапустити Xray"
"restartSuccess" = "Xray успішно перезапущено"
"stopSuccess" = "Xray успішно зупинено"
"restartError" = "Виникла помилка під час перезапуску Xray."
"stopError" = "Виникла помилка під час зупинки Xray."
"basicTemplate" = "Базовий шаблон"
"advancedTemplate" = "Додатково"
"generalConfigs" = "Загальні конфігурації"
"generalConfigsDesc" = "Ці параметри визначатимуть загальні налаштування."
"logConfigs" = "Журнал"
"logConfigsDesc" = "Журнали можуть вплинути на ефективність вашого сервера. Рекомендується вмикати його з розумом лише у випадку ваших потреб"
"blockConfigsDesc" = "Ці параметри блокуватимуть трафік на основі конкретних запитуваних протоколів і веб-сайтів."
"basicRouting" = "Основна Маршрутизація"
"blockConnectionsConfigsDesc" = "Ці параметри блокуватимуть трафік на основі запитаних країн."
"directConnectionsConfigsDesc" = "Пряме з'єднання гарантує, що певний трафік не буде маршрутизовано через інший сервер."
"blockips" = "Блокувати IP"
"blockdomains" = "Блокувати домени"
"directips" = "Прямі IP"
"directdomains" = "Прямі домени"
"ipv4Routing" = "Маршрутизація IPv4"
"ipv4RoutingDesc" = "Ці параметри спрямовуватимуть трафік на основі певного призначення через IPv4."
"warpRouting" = "WARP Маршрутизація"
"warpRoutingDesc" = "Ці параметри маршрутизуватимуть трафік на основі певного пункту призначення через WARP."
"Template" = "Шаблон розширеної конфігурації Xray"
"TemplateDesc" = "Остаточний конфігураційний файл Xray буде створено на основі цього шаблону."
"FreedomStrategy" = "Стратегія протоколу свободи"
"FreedomStrategyDesc" = "Установити стратегію виведення для мережі в протоколі свободи."
"RoutingStrategy" = "Загальна стратегія маршрутизації"
"RoutingStrategyDesc" = "Установити загальну стратегію маршрутизації трафіку для вирішення всіх запитів."
"Torrent" = "Блокувати протокол BitTorrent"
"Inbounds" = "Вхідні"
"InboundsDesc" = "Прийняття певних клієнтів."
"Outbounds" = "Вихід"
"Balancers" = "Балансери"
"OutboundsDesc" = "Встановити шлях вихідного трафіку."
"Routings" = "Правила маршрутизації"
"RoutingsDesc" = "Пріоритет кожного правила важливий!"
"completeTemplate" = "Усі"
"logLevel" = "Рівень журналу"
"logLevelDesc" = "Рівень журналу для журналів помилок із зазначенням інформації, яку потрібно записати."
"accessLog" = "Журнал доступу"
"accessLogDesc" = "Шлях до файлу журналу доступу. Спеціальне значення 'none' вимикає журнали доступу"
"errorLog" = "Журнал помилок"
"errorLogDesc" = "Шлях до файлу журналу помилок. Спеціальне значення 'none' вимикає журнали помилок"
"dnsLog" = "Журнал DNS"
"dnsLogDesc" = "Чи включити журнали запитів DNS"
"maskAddress" = "Маскувати Адресу"
"maskAddressDesc" = "Маска IP-адреси, при активації автоматично замінює IP-адресу, яка з'являється у журналі."
"statistics" = "Статистика"
"statsInboundUplink" = "Статистика вхідного аплінку"
"statsInboundUplinkDesc" = "Увімкнення збору статистики для вхідного трафіку всіх вхідних проксі."
"statsInboundDownlink" = "Статистика вхідного даунлінку"
"statsInboundDownlinkDesc" = "Увімкнення збору статистики для вихідного трафіку всіх вхідних проксі."
"statsOutboundUplink" = "Статистика вихідного аплінку"
"statsOutboundUplinkDesc" = "Увімкнення збору статистики для вхідного трафіку всіх вихідних проксі."
"statsOutboundDownlink" = "Статистика вихідного даунлінку"
"statsOutboundDownlinkDesc" = "Увімкнення збору статистики для вихідного трафіку всіх вихідних проксі."

[pages.xray.rules]
"first" = "Перший"
"last" = "Останній"
"up" = "Вгору"
"down" = "Вниз"
"source" = "Джерело"
"dest" = "Пункт призначення"
"inbound" = "Вхідний"
"outbound" = "Вихідний"
"balancer" = "Балансувальник"
"info" = "Інформація"
"add" = "Додати правило"
"edit" = "Редагувати правило"
"useComma" = "Елементи, розділені комами"

[pages.xray.outbound]
"addOutbound" = "Додати вихідний"
"addReverse" = "Додати реверс"
"editOutbound" = "Редагувати вихідні"
"editReverse" = "Редагувати реверс"
"tag" = "Тег"
"tagDesc" = "Унікальний тег"
"address" = "Адреса"
"reverse" = "Зворотний"
"domain" = "Домен"
"type" = "Тип"
"bridge" = "Міст"
"portal" = "Портал"
"link" = "Посилання"
"intercon" = "Взаємозв'язок"
"settings" = "Налаштування"
"accountInfo" = "Інформація про обліковий запис"
"outboundStatus" = "Статус виходу"
"sendThrough" = "Надіслати через"

[pages.xray.balancer]
"addBalancer" = "Додати балансир"
"editBalancer" = "Редагувати балансир"
"balancerStrategy" = "Стратегія"
"balancerSelectors" = "Селектори"
"tag" = "Тег"
"tagDesc" = "Унікальний тег"
"balancerDesc" = "Неможливо використовувати balancerTag і outboundTag одночасно. Якщо використовувати одночасно, працюватиме лише outboundTag."

[pages.xray.wireguard]
"secretKey" = "Приватний ключ"
"publicKey" = "Публічний ключ"
"allowedIPs" = "Дозволені IP-адреси"
"endpoint" = "Кінцева точка"
"psk" = "Спільний ключ"
"domainStrategy" = "Стратегія домену"

[pages.xray.dns]
"enable" = "Увімкнути DNS"
"enableDesc" = "Увімкнути вбудований DNS-сервер"
"tag" = "Мітка вхідного DNS"
"tagDesc" = "Ця мітка буде доступна як вхідна мітка в правилах маршрутизації."
"clientIp" = "IP клієнта"
"clientIpDesc" = "Використовується для повідомлення серверу про вказане місцезнаходження IP під час DNS-запитів"
"disableCache" = "Вимкнути кеш"
"disableCacheDesc" = "Вимкнути кешування DNS"
"disableFallback" = "Вимкнути резервний DNS"
"disableFallbackDesc" = "Вимкнути резервні DNS-запити"
"disableFallbackIfMatch" = "Вимкнути резервний DNS при збігу"
"disableFallbackIfMatchDesc" = "Вимкнути резервні DNS-запити при збігу списку доменів DNS-сервера"
"strategy" = "Стратегія запиту"
"strategyDesc" = "Загальна стратегія вирішення доменних імен"
"add" = "Додати сервер"
"edit" = "Редагувати сервер"
"domains" = "Домени"
"expectIPs" = "Очікувані IP"
"unexpectIPs" = "Неочікувані IP"
"useSystemHosts" = "Використовувати системні Hosts"
"useSystemHostsDesc" = "Використовувати файл hosts з встановленої системи"
"usePreset" = "Використати шаблон"
"dnsPresetTitle" = "Шаблони DNS"
"dnsPresetFamily" = "Сімейний"

[pages.xray.fakedns]
"add" = "Додати підроблений DNS"
"edit" = "Редагувати підроблений DNS"
"ipPool" = "Підмережа IP-пулу"
"poolSize" = "Розмір пулу"

[pages.settings.security]
"admin" = "Облікові дані адміністратора"
"twoFactor" = "Двофакторна аутентифікація"  
"twoFactorEnable" = "Увімкнути 2FA"  
"twoFactorEnableDesc" = "Додає додатковий рівень аутентифікації для підвищення безпеки."  
"twoFactorModalSetTitle" = "Увімкнути двофакторну аутентифікацію"
"twoFactorModalDeleteTitle" = "Вимкнути двофакторну аутентифікацію"
"twoFactorModalSteps" = "Щоб налаштувати двофакторну аутентифікацію, виконайте кілька кроків:"
"twoFactorModalFirstStep" = "1. Відскануйте цей QR-код у програмі для аутентифікації або скопіюйте токен біля QR-коду та вставте його в програму"
"twoFactorModalSecondStep" = "2. Введіть код з програми"
"twoFactorModalRemoveStep" = "Введіть код з програми, щоб вимкнути двофакторну аутентифікацію."
"twoFactorModalChangeCredentialsTitle" = "Змінити облікові дані"
"twoFactorModalChangeCredentialsStep" = "Введіть код з додатку, щоб змінити облікові дані адміністратора."
"twoFactorModalSetSuccess" = "Двофакторна аутентифікація була успішно встановлена"
"twoFactorModalDeleteSuccess" = "Двофакторна аутентифікація була успішно видалена"
"twoFactorModalError" = "Невірний код"

[pages.settings.toasts]
"modifySettings" = "Параметри було змінено."
"getSettings" = "Виникла помилка під час отримання параметрів."
"modifyUserError" = "Виникла помилка під час зміни облікових даних адміністратора."
"modifyUser" = "Ви успішно змінили облікові дані адміністратора."
"originalUserPassIncorrect" = "Поточне ім'я користувача або пароль недійсні"
"userPassMustBeNotEmpty" = "Нове ім'я користувача та пароль порожні"
"getOutboundTrafficError" = "Помилка отримання вихідного трафіку"
"resetOutboundTrafficError" = "Помилка скидання вихідного трафіку"

[tgbot]
"keyboardClosed" = "❌ Спеціальна клавіатура закрита!"
"noResult" = "❗ Немає результату!"
"noQuery" = "❌ Запит не знайдено! Скористайтеся командою ще раз!"
"wentWrong" = "❌ Щось пішло не так!"
"noIpRecord" = "❗ Немає IP-запису!"
"noInbounds" = "❗ Вхідних не знайдено!"
"unlimited" = "♾ Необмежений (скинути)"
"add" = "Додати"
"month" = "Місяць"
"months" = "Місяці"
"day" = "День"
"days" = "Дні"
"hours" = "Годинник"
"unknown" = "Невідомо"
"inbounds" = "Вхідні"
"clients" = "Клієнти"
"offline" = "🔴 Офлайн"
"online" = "🟢 Онлайн"

[tgbot.commands]
"unknown" = "❗ Невідома команда."
"pleaseChoose" = "👇 Будь ласка, виберіть:\r\n"
"help" = "🤖 Ласкаво просимо до цього бота! Він розроблений, щоб надавати певні дані з веб-панелі та дозволяє вносити зміни за потреби.\r\n\r\n"
"start" = "👋 Привіт <i>{{ .Firstname }}</i>.\r\n"
"welcome" = "🤖 Ласкаво просимо до <b>{{ .Hostname }}</b> бота керування.\r\n"
"status" = "✅ Бот в порядку!"
"usage" = "❗ Введіть текст для пошуку!"
"getID" = "🆔 Ваш ідентифікатор: <code>{{ .ID }}</code>"
"helpAdminCommands" = "Для перезапуску Xray Core:\r\n<code>/restart</code>\r\n\r\nДля пошуку електронної пошти клієнта:\r\n<code>/usage [Електронна пошта]</code>\r\n\r\nДля пошуку вхідних (зі статистикою клієнта):\r\n<code>/inbound [Примітка]</code>\r\n\r\nID чату Telegram:\r\n<code>/id</code>"
"helpClientCommands" = "Для пошуку статистики використовуйте наступну команду:\r\n<code>/usage [Електронна пошта]</code>\r\n\r\nID чату Telegram:\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ Операція успішна!"
"restartFailed" = "❗ Помилка в операції.\r\n\r\n<code>Помилка: {{ .Error }}</code>."
"xrayNotRunning" = "❗ Xray Core не запущений."
"startDesc" = "Показати головне меню"
"helpDesc" = "Довідка по боту"
"statusDesc" = "Перевірити статус бота"
"idDesc" = "Показати ваш Telegram ID"

[tgbot.messages]
"cpuThreshold" = "🔴 Навантаження ЦП  {{ .Percent }}% перевищує порогове значення {{ .Threshold }}%"
"selectUserFailed" = "❌ Помилка під час вибору користувача!"
"userSaved" = "✅ Користувача Telegram збережено."
"loginSuccess" = "✅ Успішно ввійшли в панель\r\n"
"loginFailed" = "❗️ Помилка входу в панель.\r\n"
"report" = "🕰 Заплановані звіти: {{ .RunTime }}\r\n"
"datetime" = "⏰ Дата й час: {{ .DateTime }}\r\n"
"hostname" = "💻 Хост: {{ .Hostname }}\r\n"
"version" = "🚀 3X-UI Версія: {{ .Version }}\r\n"
"xrayVersion" = "📡 Xray Версія: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6: {{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4: {{ .IPv4 }}\r\n"
"ip" = "🌐 IP: {{ .IP }}\r\n"
"ips" = "🔢 IP-адреси:\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ Час роботи: {{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 Завантаження системи: {{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 RAM: {{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 TCP: {{ .Count }}\r\n"
"udpCount" = "🔸 UDP: {{ .Count }}\r\n"
"traffic" = "🚦 Трафік: {{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ Статус: {{ .State }}\r\n"
"username" = "👤 Ім'я користувача: {{ .Username }}\r\n"
"password" = "👤 Пароль: {{ .Password }}\r\n"
"time" = "⏰ Час: {{ .Time }}\r\n"
"inbound" = "📍 Inbound: {{ .Remark }}\r\n"
"port" = "🔌 Порт: {{ .Port }}\r\n"
"expire" = "📅 Дата закінчення: {{ .Time }}\r\n"
"expireIn" = "📅 Термін дії: {{ .Time }}\r\n"
"active" = "💡 Активний: {{ .Enable }}\r\n"
"enabled" = "🚨 Увімкнено: {{ .Enable }}\r\n"
"online" = "🌐 Стан підключення: {{ .Status }}\r\n"
"email" = "📧 Електронна пошта: {{ .Email }}\r\n"
"upload" = "🔼 Upload: ↑{{ .Upload }}\r\n"
"download" = "🔽 Download: ↓{{ .Download }}\r\n"
"total" = "📊 Всього: ↑↓{{ .UpDown }} / {{ .Total }}\r\n"
"TGUser" = "👤 Користувач Telegram: {{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 Вичерпано {{ .Type }}:\r\n"
"exhaustedCount" = "🚨 Вичерпано кількість {{ .Type }} count:\r\n"
"onlinesCount" = "🌐 Онлайн-клієнти: {{ .Count }}\r\n"
"disabled" = "🛑 Вимкнено: {{ .Disabled }}\r\n"
"depleteSoon" = "🔜 Скоро вичерпається: {{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 Час резервного копіювання: {{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 Оновлено: {{ .Time }}\r\n\r\n"
"yes" = "✅ Так"
"no" = "❌ Ні"

"received_id" = "🔑📥 ID оновлено."
"received_password" = "🔑📥 Пароль оновлено."
"received_email" = "📧📥 Електронна пошта оновлена."
"received_comment" = "💬📥 Коментар оновлено."
"id_prompt" = "🔑 Стандартний ID: {{ .ClientId }}\n\nВведіть ваш ID."
"pass_prompt" = "🔑 Стандартний пароль: {{ .ClientPassword }}\n\nВведіть ваш пароль."
"email_prompt" = "📧 Стандартний email: {{ .ClientEmail }}\n\nВведіть ваш email."
"comment_prompt" = "💬 Стандартний коментар: {{ .ClientComment }}\n\nВведіть ваш коментар."
"inbound_client_data_id" = "🔄 Вхід: {{ .InboundRemark }}\n\n🔑 ID: {{ .ClientId }}\n📧 Електронна пошта: {{ .ClientEmail }}\n📊 Трафік: {{ .ClientTraffic }}\n📅 Дата завершення: {{ .ClientExp }}\n🌐 Обмеження IP: {{ .IpLimit }}\n💬 Коментар: {{ .ClientComment }}\n\nТепер ви можете додати клієнта до вхідного з'єднання!"
"inbound_client_data_pass" = "🔄 Вхід: {{ .InboundRemark }}\n\n🔑 Пароль: {{ .ClientPass }}\n📧 Електронна пошта: {{ .ClientEmail }}\n📊 Трафік: {{ .ClientTraffic }}\n📅 Дата завершення: {{ .ClientExp }}\n🌐 Обмеження IP: {{ .IpLimit }}\n💬 Коментар: {{ .ClientComment }}\n\nТепер ви можете додати клієнта до вхідного з'єднання!"
"cancel" = "❌ Процес скасовано! \n\nВи можете знову розпочати, використовуючи /start у будь-який час. 🔄"
"error_add_client"  = "⚠️ Помилка:\n\n {{ .error }}"
"using_default_value"  = "Гаразд, залишу значення за замовчуванням. 😊"
"incorrect_input" ="Ваш ввід невірний.\nФрази повинні бути без пробілів.\nПравильний приклад: aaaaaa\nНеправильний приклад: aaa aaa 🚫"
"AreYouSure" = "Ви впевнені? 🤔"
"SuccessResetTraffic" = "📧 Електронна пошта: {{ .ClientEmail }}\n🏁 Результат: ✅ Успішно"
"FailedResetTraffic" = "📧 Електронна пошта: {{ .ClientEmail }}\n🏁 Результат: ❌ Невдача \n\n🛠️ Помилка: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 Процес скидання трафіку завершено для всіх клієнтів."


[tgbot.buttons]
"closeKeyboard" = "❌ Закрити клавіатуру"
"cancel" = "❌ Скасувати"
"cancelReset" = "❌ Скасувати скидання"
"cancelIpLimit" = "❌ Скасувати обмеження IP"
"confirmResetTraffic" = "✅ Підтвердити скидання трафіку?"
"confirmClearIps" = "✅ Підтвердити очищення IP-адрес?"
"confirmRemoveTGUser" = "✅ Підтвердити видалення користувача Telegram?"
"confirmToggle" = "✅ Підтвердити ввімкнути/вимкнути користувача?"
"dbBackup" = "Отримати резервну копію БД"
"serverUsage" = "Використання сервера"
"getInbounds" = "Отримати вхідні"
"depleteSoon" = "Скоро вичерпати"
"clientUsage" = "Отримати використання"
"onlines" = "Онлайн-клієнти"
"commands" = "Команди"
"refresh" = "🔄 Оновити"
"clearIPs" = "❌ Очистити IP-адреси"
"removeTGUser" = "❌ Видалити користувача Telegram"
"selectTGUser" = "👤 Виберіть користувача Telegram"
"selectOneTGUser" = "👤 Виберіть користувача Telegram:"
"resetTraffic" = "📈 Скинути трафік"
"resetExpire" = "📅 Змінити термін дії"
"ipLog" = "🔢 IP журнал"
"ipLimit" = "🔢 IP Ліміт"
"setTGUser" = "👤 Встановити користувача Telegram"
"toggle" = "🔘 Увімкнути / Вимкнути"
"custom" = "🔢 Custom"
"confirmNumber" = "✅ Підтвердити: {{ .Num }}"
"confirmNumberAdd" = "✅ Підтвердити додавання: {{ .Num }}"
"limitTraffic" = "🚧 Ліміт трафіку"
"getBanLogs" = "Отримати журнали заборон"
"allClients" = "Всі Клієнти"

"addClient" = "Додати клієнта"
"submitDisable" = "Надіслати як вимкнено ☑️"
"submitEnable" = "Надіслати як увімкнено ✅"
"use_default" = "🏷️ Використати типове"
"change_id" = "⚙️🔑 ID"
"change_password" = "⚙️🔑 Пароль"
"change_email" = "⚙️📧 Електронна пошта"
"change_comment" = "⚙️💬 Коментар"
"ResetAllTraffics" = "Скинути весь трафік"
"SortedTrafficUsageReport" = "Відсортований звіт про використання трафіку"


[tgbot.answers]
"successfulOperation" = "✅ Операція успішна!"
"errorOperation" = "❗ Помилка в роботі."
"getInboundsFailed" = "❌ Не вдалося отримати вхідні повідомлення."
"getClientsFailed" = "❌ Не вдалося отримати клієнтів."
"canceled" = "❌ {{ .Email }}: Операцію скасовано."
"clientRefreshSuccess" = "✅ {{ .Email }}: Клієнт успішно оновлено."
"IpRefreshSuccess" = "✅ {{ .Email }}: IP-адреси успішно оновлено."
"TGIdRefreshSuccess" = "✅ {{ .Email }}: Користувач Telegram клієнта успішно оновлено."
"resetTrafficSuccess" = "✅ {{ .Email }}: Трафік скинуто успішно."
"setTrafficLimitSuccess" = "✅ {{ .Email }}: Ліміт трафіку успішно збережено."
"expireResetSuccess" = "✅ {{ .Email }}: Успішно скинуто дні закінчення терміну дії."
"resetIpSuccess" = "✅ {{ .Email }}: IP обмеження {{ .Count }} успішно збережено."
"clearIpSuccess" = "✅ {{ .Email }}: IP успішно очищено."
"getIpLog" = "✅ {{ .Email }}: Отримати IP-журнал."
"getUserInfo" = "✅ {{ .Email }}: Отримати інформацію про користувача Telegram."
"removedTGUserSuccess" = "✅ {{ .Email }}: Користувача Telegram видалено успішно."
"enableSuccess" = "✅ {{ .Email }}: Увімкнути успішно."
"disableSuccess" = "✅ {{ .Email }}: Успішно вимкнено."
"askToAddUserId" = "Вашу конфігурацію не знайдено!\r\nБудь ласка, попросіть свого адміністратора використовувати ваш ідентифікатор Telegram у вашій конфігурації.\r\n\r\nВаш ідентифікатор користувача: <code>{{ .TgUserID }}</code>"
"chooseClient" = "Виберіть клієнта для Вхідного {{ .Inbound }}"
"chooseInbound" = "Виберіть Вхідний"
