"username" = "Nombre de Usuario"
"password" = "Contraseña"
"login" = "Acceder"
"confirm" = "Confirmar"
"cancel" = "Cancelar"
"close" = "Cerrar"
"create" = "Crear"
"update" = "Actualizar"
"copy" = "Copiar"
"copied" = "Copiado"
"download" = "Descargar"
"remark" = "Nota"
"enable" = "Habilitar"
"protocol" = "Protocolo"
"search" = "Buscar"
"filter" = "Filtrar"
"loading" = "Cargando..."
"second" = "Segundo"
"minute" = "Minuto"
"hour" = "Hora"
"day" = "Día"
"check" = "Verificar"
"indefinite" = "Indefinido"
"unlimited" = "Ilimitado"
"none" = "None"
"qrCode" = "Código QR"
"info" = "Más Información"
"edit" = "Editar"
"delete" = "Eliminar"
"reset" = "Restablecer"
"noData" = "Sin datos."
"copySuccess" = "Copiado exitosamente"
"sure" = "Seguro"
"encryption" = "Encriptación"
"useIPv4ForHost" = "Usar IPv4 para el host"
"transmission" = "Transmisión"
"host" = "Anfitrión"
"path" = "Ruta"
"camouflage" = "Camuflaje"
"status" = "Estado"
"enabled" = "Habilitado"
"disabled" = "Deshabilitado"
"depleted" = "Agotado"
"depletingSoon" = "Agotándose"
"offline" = "fuera de línea"
"online" = "en línea"
"domainName" = "Nombre de dominio"
"monitor" = "Listening IP"
"certificate" = "Certificado Digital"
"fail" = "Falló"
"comment" = "Comentario"
"success" = "Éxito"
"getVersion" = "Obtener versión"
"install" = "Instalar"
"clients" = "Clientes"
"usage" = "Uso"
"twoFactorCode" = "Código"
"remained" = "Restante"
"security" = "Seguridad"
"secAlertTitle" = "Alerta de Seguridad"
"secAlertSsl" = "Esta conexión no es segura. Por favor, evite ingresar información sensible hasta que se active TLS para la protección de datos."
"secAlertConf" = "Ciertas configuraciones son vulnerables a ataques. Se recomienda reforzar los protocolos de seguridad para prevenir posibles violaciones."
"secAlertSSL" = "El panel carece de una conexión segura. Por favor, instale un certificado TLS para la protección de datos."
"secAlertPanelPort" = "El puerto predeterminado del panel es vulnerable. Por favor, configure un puerto aleatorio o específico."
"secAlertPanelURI" = "La ruta URI predeterminada del panel no es segura. Por favor, configure una ruta URI compleja."
"secAlertSubURI" = "La ruta URI predeterminada de la suscripción no es segura. Por favor, configure una ruta URI compleja."
"secAlertSubJsonURI" = "La ruta URI JSON predeterminada de la suscripción no es segura. Por favor, configure una ruta URI compleja."
"emptyDnsDesc" = "No hay servidores DNS añadidos."
"emptyFakeDnsDesc" = "No hay servidores Fake DNS añadidos."
"emptyBalancersDesc" = "No hay balanceadores añadidos."
"emptyReverseDesc" = "No hay proxies inversos añadidos."
"somethingWentWrong" = "Algo salió mal"

[menu]
"theme" = "Tema"
"dark" = "Oscuro"
"ultraDark" = "Ultra Oscuro"
"dashboard" = "Estado del Sistema"
"inbounds" = "Entradas"
"settings" = "Configuraciones"
"xray" = "Ajustes Xray"
"logout" = "Cerrar Sesión"
"link" = "Gestionar"

[pages.login]
"hello" = "Hola"
"title" = "Bienvenido"
"loginAgain" = "El límite de tiempo de inicio de sesión ha expirado. Por favor, inicia sesión nuevamente."

[pages.login.toasts]
"invalidFormData" = "El formato de los datos de entrada es inválido."
"emptyUsername" = "Por favor ingresa el nombre de usuario."
"emptyPassword" = "Por favor ingresa la contraseña."
"wrongUsernameOrPassword" = "Nombre de usuario, contraseña o código de dos factores incorrecto."  
"successLogin" = "Has iniciado sesión en tu cuenta correctamente."

[pages.index]
"title" = "Estado del Sistema"
"cpu" = "CPU"
"logicalProcessors" = "Procesadores lógicos"
"frequency" = "Frecuencia"
"swap" = "Intercambio"
"storage" = "Almacenamiento"
"memory" = "RAM"
"threads" = "Hilos"
"xrayStatus" = "Xray"
"stopXray" = "Detener"
"restartXray" = "Reiniciar"
"xraySwitch" = "Versión"
"xraySwitchClick" = "Elige la versión a la que deseas cambiar."
"xraySwitchClickDesk" = "Elige sabiamente, ya que las versiones anteriores pueden no ser compatibles con las configuraciones actuales."
"xrayStatusUnknown" = "Desconocido"
"xrayStatusRunning" = "En ejecución"
"xrayStatusStop" = "Detenido"
"xrayStatusError" = "Error"
"xrayErrorPopoverTitle" = "Se produjo un error al ejecutar Xray"
"operationHours" = "Tiempo de Funcionamiento"
"systemLoad" = "Carga del Sistema"
"systemLoadDesc" = "promedio de carga del sistema en los últimos 1, 5 y 15 minutos"
"connectionTcpCountDesc" = "Conexiones TCP totales en todas las tarjetas de red."
"connectionUdpCountDesc" = "Conexiones UDP totales en todas las tarjetas de red."
"connectionCount" = "Número de Conexiones"
"ipAddresses" = "Direcciones IP"
"toggleIpVisibility" = "Alternar visibilidad de la IP"
"overallSpeed" = "Velocidad general"
"upload" = "Subida"
"download" = "Descarga"
"totalData" = "Datos totales"
"sent" = "Enviado"
"received" = "Recibido"
"documentation" = "Documentación"
"xraySwitchVersionDialog" = "¿Realmente deseas cambiar la versión de Xray?"
"xraySwitchVersionDialogDesc" = "Esto cambiará la versión de Xray a #version#."
"xraySwitchVersionPopover" = "Xray se actualizó correctamente"
"geofileUpdateDialog" = "¿Realmente deseas actualizar el geofichero?"
"geofileUpdateDialogDesc" = "Esto actualizará el archivo #filename#."
"geofileUpdatePopover" = "Geofichero actualizado correctamente"
"dontRefresh" = "La instalación está en progreso, por favor no actualices esta página."
"logs" = "Registros"
"config" = "Configuración"
"backup" = "Сopia de Seguridad"
"backupTitle" = "Copia de Seguridad y Restauración de la Base de Datos"
"exportDatabase" = "Copia de seguridad"
"exportDatabaseDesc" = "Haz clic para descargar un archivo .db que contiene una copia de seguridad de tu base de datos actual en tu dispositivo."
"importDatabase" = "Restaurar"
"importDatabaseDesc" = "Haz clic para seleccionar y cargar un archivo .db desde tu dispositivo para restaurar tu base de datos desde una copia de seguridad."
"importDatabaseSuccess" = "La base de datos se ha importado correctamente"
"importDatabaseError" = "Ocurrió un error al importar la base de datos"
"readDatabaseError" = "Ocurrió un error al leer la base de datos"
"getDatabaseError" = "Ocurrió un error al obtener la base de datos"
"getConfigError" = "Ocurrió un error al obtener el archivo de configuración"

[pages.inbounds]
"title" = "Entradas"
"totalDownUp" = "Subidas/Descargas Totales"
"totalUsage" = "Uso Total"
"inboundCount" = "Número de Entradas"
"operate" = "Menú"
"enable" = "Habilitar"
"remark" = "Notas"
"protocol" = "Protocolo"
"port" = "Puerto"
"traffic" = "Tráfico"
"details" = "Detalles"
"transportConfig" = "Transporte"
"expireDate" = "Fecha de Expiración"
"resetTraffic" = "Restablecer Tráfico"
"addInbound" = "Agregar Entrada"
"generalActions" = "Acciones Generales"
"autoRefresh" = "Auto-actualizar"
"autoRefreshInterval" = "Intervalo"
"modifyInbound" = "Modificar Entrada"
"deleteInbound" = "Eliminar Entrada"
"deleteInboundContent" = "¿Confirmar eliminación de entrada?"
"deleteClient" = "Eliminar cliente"
"deleteClientContent" = "¿Está seguro de que desea eliminar el cliente?"
"resetTrafficContent" = "¿Confirmar restablecimiento de tráfico?"
"inboundUpdateSuccess" = "La entrada se ha actualizado correctamente."
"inboundCreateSuccess" = "La entrada se ha creado correctamente."
"copyLink" = "Copiar Enlace"
"address" = "Dirección"
"network" = "Red"
"destinationPort" = "Puerto de Destino"
"targetAddress" = "Dirección de Destino"
"monitorDesc" = "Dejar en blanco por defecto"
"meansNoLimit" = "= illimitata. (unidad: GB)"
"totalFlow" = "Flujo Total"
"leaveBlankToNeverExpire" = "Dejar en Blanco para Nunca Expirar"
"noRecommendKeepDefault" = "No hay requisitos especiales para mantener la configuración predeterminada"
"certificatePath" = "Ruta Cert"
"certificateContent" = "Datos Cert"
"publicKey" = "Clave Pública"
"privatekey" = "Clave Privada"
"clickOnQRcode" = "Haz clic en el Código QR para Copiar"
"client" = "Cliente"
"export" = "Exportar Enlaces"
"clone" = "Clonar"
"cloneInbound" = "Clonar Entradas"
"cloneInboundContent" = "Se aplicarán todas las configuraciones de esta entrada, excepto el Puerto, la IP de Escucha y los Clientes, al clon."
"cloneInboundOk" = "Clonar"
"resetAllTraffic" = "Restablecer Tráfico de Todas las Entradas"
"resetAllTrafficTitle" = "Restablecer tráfico de todas las entradas"
"resetAllTrafficContent" = "¿Estás seguro de que deseas restablecer el tráfico de todas las entradas?"
"resetInboundClientTraffics" = "Restablecer Tráfico de Clientes"
"resetInboundClientTrafficTitle" = "Restablecer todo el tráfico de clientes"
"resetInboundClientTrafficContent" = "¿Estás seguro de que deseas restablecer todo el tráfico para los clientes de esta entrada?"
"resetAllClientTraffics" = "Restablecer Tráfico de Todos los Clientes"
"resetAllClientTrafficTitle" = "Restablecer todo el tráfico de clientes"
"resetAllClientTrafficContent" = "¿Estás seguro de que deseas restablecer todo el tráfico para todos los clientes?"
"delDepletedClients" = "Eliminar Clientes Agotados"
"delDepletedClientsTitle" = "Eliminar clientes agotados"
"delDepletedClientsContent" = "¿Estás seguro de que deseas eliminar todos los clientes agotados?"
"email" = "Email"
"emailDesc" = "Por favor proporciona una dirección de correo electrónico única."
"IPLimit" = "Límite de IP"
"IPLimitDesc" = "Desactiva la entrada si la cantidad supera el valor ingresado (ingresa 0 para desactivar el límite de IP)."
"IPLimitlog" = "Registro de IP"
"IPLimitlogDesc" = "Registro de historial de IPs (antes de habilitar la entrada después de que haya sido desactivada por el límite de IP, debes borrar el registro)."
"IPLimitlogclear" = "Limpiar el Registro"
"setDefaultCert" = "Establecer certificado desde el panel"
"telegramDesc" = "Por favor, proporciona el ID de Chat de Telegram. (usa el comando '/id' en el bot) o (@userinfobot)"
"subscriptionDesc" = "Puedes encontrar tu enlace de suscripción en Detalles, también puedes usar el mismo nombre para varias configuraciones."
"info" = "Info"
"same" = "misma"
"inboundData" = "Datos de entrada"
"exportInbound" = "Exportación entrante"
"import" = "Importar"
"importInbound" = "Importar un entrante"

[pages.client]
"add" = "Agregar Cliente"
"edit" = "Editar Cliente"
"submitAdd" = "Agregar Cliente"
"submitEdit" = "Guardar Cambios"
"clientCount" = "Número de Clientes"
"bulk" = "Agregar en Lote"
"method" = "Método"
"first" = "Primero"
"last" = "Último"
"prefix" = "Prefijo"
"postfix" = "Sufijo"
"delayedStart" = "Iniciar después del primer uso"
"expireDays" = "Duración"
"days" = "Día(s)"
"renew" = "Renovación automática"
"renewDesc" = "Renovación automática después de la expiración. (0 = desactivar) (unidad: día)"

[pages.inbounds.toasts]
"obtain" = "Recibir"
"updateSuccess" = "La actualización fue exitosa"
"logCleanSuccess" = "El registro ha sido limpiado"
"inboundsUpdateSuccess" = "Entradas actualizadas correctamente"
"inboundUpdateSuccess" = "Entrada actualizada correctamente"
"inboundCreateSuccess" = "Entrada creada correctamente"
"inboundDeleteSuccess" = "Entrada eliminada correctamente"
"inboundClientAddSuccess" = "Cliente(s) de entrada añadido(s)"
"inboundClientDeleteSuccess" = "Cliente de entrada eliminado"
"inboundClientUpdateSuccess" = "Cliente de entrada actualizado"
"delDepletedClientsSuccess" = "Todos los clientes agotados fueron eliminados"
"resetAllClientTrafficSuccess" = "Todo el tráfico del cliente ha sido reiniciado"
"resetAllTrafficSuccess" = "Todo el tráfico ha sido reiniciado"
"resetInboundClientTrafficSuccess" = "El tráfico ha sido reiniciado"
"trafficGetError" = "Error al obtener los tráficos"
"getNewX25519CertError" = "Error al obtener el certificado X25519."

[pages.inbounds.stream.general]
"request" = "Pedido"
"response" = "Respuesta"
"name" = "Nombre"
"value" = "Valor"

[pages.inbounds.stream.tcp]
"version" = "Versión"
"method" = "Método"
"path" = "Camino"
"status" = "Estado"
"statusDescription" = "Descripción de la Situación"
"requestHeader" = "Encabezado de solicitud"
"responseHeader" = "Encabezado de respuesta"

[pages.settings]
"title" = "Configuraciones"
"save" = "Guardar"
"infoDesc" = "Cada cambio realizado aquí debe ser guardado. Por favor, reinicie el panel para aplicar los cambios."
"restartPanel" = "Reiniciar Panel"
"restartPanelDesc" = "¿Está seguro de que desea reiniciar el panel? Haga clic en Aceptar para reiniciar después de 3 segundos. Si no puede acceder al panel después de reiniciar, por favor, consulte la información de registro del panel en el servidor."
"restartPanelSuccess" = "El panel se reinició correctamente"
"actions" = "Acciones"
"resetDefaultConfig" = "Restablecer a Configuración Predeterminada"
"panelSettings" = "Configuraciones del Panel"
"securitySettings" = "Configuraciones de Seguridad"
"TGBotSettings" = "Configuraciones de Bot de Telegram"
"panelListeningIP" = "IP de Escucha del Panel"
"panelListeningIPDesc" = "Dejar en blanco por defecto para monitorear todas las IPs."
"panelListeningDomain" = "Dominio de Escucha del Panel"
"panelListeningDomainDesc" = "Dejar en blanco por defecto para monitorear todos los dominios e IPs."
"panelPort" = "Puerto del Panel"
"panelPortDesc" = "El puerto utilizado para mostrar este panel."
"publicKeyPath" = "Ruta del Archivo de Clave Pública del Certificado del Panel"
"publicKeyPathDesc" = "Complete con una ruta absoluta que comience con."
"privateKeyPath" = "Ruta del Archivo de Clave Privada del Certificado del Panel"
"privateKeyPathDesc" = "Complete con una ruta absoluta que comience con."
"panelUrlPath" = "Ruta Raíz de la URL del Panel"
"panelUrlPathDesc" = "Debe empezar con '/' y terminar con."
"pageSize" = "Tamaño de paginación"
"pageSizeDesc" = "Defina el tamaño de página para la tabla de entradas. Establezca 0 para desactivar"
"remarkModel" = "Modelo de observación y carácter de separación"
"datepicker" = "selector de fechas"
"datepickerPlaceholder" = "Seleccionar fecha"
"datepickerDescription" = "El tipo de calendario selector especifica la fecha de vencimiento"
"sampleRemark" = "Observación de muestra"
"oldUsername" = "Nombre de Usuario Actual"
"currentPassword" = "Contraseña Actual"
"newUsername" = "Nuevo Nombre de Usuario"
"newPassword" = "Nueva Contraseña"
"telegramBotEnable" = "Habilitar bot de Telegram"
"telegramBotEnableDesc" = "Conéctese a las funciones de este panel a través del bot de Telegram."
"telegramToken" = "Token de Telegram"
"telegramTokenDesc" = "Debe obtener el token del administrador de bots de Telegram @botfather."
"telegramProxy" = "Socks5 Proxy"
"telegramProxyDesc" = "Si necesita el proxy Socks5 para conectarse a Telegram. Ajuste su configuración según la guía."
"telegramAPIServer" = "API Server de Telegram"
"telegramAPIServerDesc" = "El servidor API de Telegram a utilizar. Déjelo en blanco para utilizar el servidor predeterminado."
"telegramChatId" = "IDs de Chat de Telegram para Administradores"
"telegramChatIdDesc" = "IDs de Chat múltiples separados por comas. Use @userinfobot o use el comando '/id' en el bot para obtener sus IDs de Chat."
"telegramNotifyTime" = "Hora de Notificación del Bot de Telegram"
"telegramNotifyTimeDesc" = "Usar el formato de tiempo de Crontab."
"tgNotifyBackup" = "Respaldo de Base de Datos"
"tgNotifyBackupDesc" = "Incluir archivo de respaldo de base de datos con notificación de informe."
"tgNotifyLogin" = "Notificación de Inicio de Sesión"
"tgNotifyLoginDesc" = "Muestra el nombre de usuario, dirección IP y hora cuando alguien intenta iniciar sesión en su panel."
"sessionMaxAge" = "Edad Máxima de Sesión"
"sessionMaxAgeDesc" = "La duración de una sesión de inicio de sesión (unidad: minutos)."
"expireTimeDiff" = "Umbral de Expiración para Notificación"
"expireTimeDiffDesc" = "Reciba notificaciones sobre la expiración de la cuenta antes del umbral (unidad: días)."
"trafficDiff" = "Umbral de Tráfico para Notificación"
"trafficDiffDesc" = "Reciba notificaciones sobre el agotamiento del tráfico antes de alcanzar el umbral (unidad: GB)."
"tgNotifyCpu" = "Umbral de Alerta de Porcentaje de CPU"
"tgNotifyCpuDesc" = "Reciba notificaciones si el uso de la CPU supera este umbral (unidad: %)."
"timeZone" = "Zona Horaria"
"timeZoneDesc" = "Las tareas programadas se ejecutan de acuerdo con la hora en esta zona horaria."
"subSettings" = "Suscripción"
"subEnable" = "Habilitar Servicio"
"subEnableDesc" = "Función de suscripción con configuración separada."
"subTitle" = "Título de la Suscripción"
"subTitleDesc" = "Título mostrado en el cliente de VPN"
"subListen" = "Listening IP"
"subListenDesc" = "Dejar en blanco por defecto para monitorear todas las IPs."
"subPort" = "Puerto de Suscripción"
"subPortDesc" = "El número de puerto para el servicio de suscripción debe estar sin usar en el servidor."
"subCertPath" = "Ruta del Archivo de Clave Pública del Certificado de Suscripción"
"subCertPathDesc" = "Complete con una ruta absoluta que comience con '/'"
"subKeyPath" = "Ruta del Archivo de Clave Privada del Certificado de Suscripción"
"subKeyPathDesc" = "Complete con una ruta absoluta que comience con '/'"
"subPath" = "Ruta Raíz de la URL de Suscripción"
"subPathDesc" = "Debe empezar con '/' y terminar con '/'"
"subDomain" = "Dominio de Escucha"
"subDomainDesc" = "Dejar en blanco por defecto para monitorear todos los dominios e IPs."
"subUpdates" = "Intervalos de Actualización de Suscripción"
"subUpdatesDesc" = "Horas de intervalo entre actualizaciones en la aplicación del cliente."
"subEncrypt" = "Encriptar configuraciones"
"subEncryptDesc" = "Encriptar las configuraciones devueltas en la suscripción."
"subShowInfo" = "Mostrar información de uso"
"subShowInfoDesc" = "Mostrar tráfico restante y fecha después del nombre de configuración."
"subURI" = "URI de proxy inverso"
"externalTrafficInformEnable" = "Informe de tráfico externo"
"externalTrafficInformEnableDesc" = "Informar a la API externa sobre cada actualización de tráfico."
"externalTrafficInformURI" = "URI de información de tráfico externo"
"externalTrafficInformURIDesc" = "Las actualizaciones de tráfico se envían a este URI."
"subURIDesc" = "Cambiar el URI base de la URL de suscripción para usar detrás de los servidores proxy"
"fragment" = "Fragmentación"
"fragmentDesc" = "Habilitar la fragmentación para el paquete de saludo de TLS"
"fragmentSett" = "Configuración de Fragmentación"
"noisesDesc" = "Activar Noises."
"noisesSett" = "Configuración de Noises"
"mux" = "Mux"
"muxDesc" = "Transmite múltiples flujos de datos independientes dentro de un flujo de datos establecido."
"muxSett" = "Configuración Mux"
"direct" = "Conexión Directa"
"directDesc" = "Establece conexiones directas con dominios o rangos de IP de un país específico."
"notifications" = "Notificaciones"
"certs" = "Certificados"
"externalTraffic" = "Tráfico Externo"
"dateAndTime" = "Fecha y Hora"
"proxyAndServer" = "Proxy y Servidor"
"intervals" = "Intervalos"
"information" = "Información"
"language" = "Idioma"
"telegramBotLanguage" = "Idioma del Bot de Telegram"

[pages.xray]
"title" = "Xray Configuración"
"save" = "Guardar configuración"
"restart" = "Reiniciar Xray"
"restartSuccess" = "Xray se ha reiniciado correctamente"
"stopSuccess" = "Xray se ha detenido correctamente"
"restartError" = "Ocurrió un error al reiniciar Xray."
"stopError" = "Ocurrió un error al detener Xray."
"basicTemplate" = "Plantilla Básica"
"advancedTemplate" = "Plantilla Avanzada"
"generalConfigs" = "Configuraciones Generales"
"generalConfigsDesc" = "Estas opciones proporcionarán ajustes generales."
"logConfigs" = "Registro"
"logConfigsDesc" = "Los registros pueden afectar la eficiencia de su servidor. Se recomienda habilitarlos sabiamente solo en caso de sus necesidades."
"blockConfigsDesc" = "Estas opciones evitarán que los usuarios se conecten a protocolos y sitios web específicos."
"basicRouting" = "Enrutamiento Básico"
"blockConnectionsConfigsDesc" = "Estas opciones bloquearán el tráfico según el país solicitado específico."
"directConnectionsConfigsDesc" = "Una conexión directa asegura que el tráfico específico no sea enrutado a través de otro servidor."
"blockips" = "Bloquear IPs"
"blockdomains" = "Bloquear Dominios"
"directips" = "IPs Directas"
"directdomains" = "Dominios Directos"
"ipv4Routing" = "Enrutamiento IPv4"
"ipv4RoutingDesc" = "Estas opciones solo enrutarán a los dominios objetivo a través de IPv4."
"warpRouting" = "Enrutamiento WARP"
"warpRoutingDesc" = "Precaución: Antes de usar estas opciones, instale WARP en modo de proxy socks5 en su servidor siguiendo los pasos en el GitHub del panel. WARP enrutará el tráfico a los sitios web a través de los servidores de Cloudflare."
"Template" = "Plantilla de Configuración de Xray"
"TemplateDesc" = "Genera el archivo de configuración final de Xray basado en esta plantilla."
"FreedomStrategy" = "Configurar Estrategia para el Protocolo Freedom"
"FreedomStrategyDesc" = "Establece la estrategia de salida de la red en el Protocolo Freedom."
"RoutingStrategy" = "Configurar Estrategia de Enrutamiento de Dominios"
"RoutingStrategyDesc" = "Establece la estrategia general de enrutamiento para la resolución de DNS."
"Torrent" = "Prohibir Uso de BitTorrent"
"Inbounds" = "Entrante"
"InboundsDesc" = "Cambia la plantilla de configuración para aceptar clientes específicos."
"Outbounds" = "Salidas"
"Balancers" = "Equilibradores"
"OutboundsDesc" = "Cambia la plantilla de configuración para definir formas de salida para este servidor."
"Routings" = "Reglas de enrutamiento"
"RoutingsDesc" = "¡La prioridad de cada regla es importante!"
"completeTemplate" = "Todos"
"logLevel" = "Nivel de registro"
"logLevelDesc" = "El nivel de registro para registros de errores, que indica la información que debe registrarse."
"accessLog" = "Registro de acceso"
"accessLogDesc" = "La ruta del archivo para el registro de acceso. El valor especial 'ninguno' deshabilita los registros de acceso"
"errorLog" = "Registro de Errores"
"errorLogDesc" = "La ruta del archivo para el registro de errores. El valor especial 'none' desactiva los registros de errores."
"dnsLog" = "Registro DNS"
"dnsLogDesc" = "Si habilitar los registros de consulta DNS"
"maskAddress" = "Enmascarar Dirección"
"maskAddressDesc" = "Máscara de dirección IP, cuando se habilita, reemplazará automáticamente la dirección IP que aparece en el registro."
"statistics" = "Estadísticas"
"statsInboundUplink" = "Estadísticas de Subida de Entrada"
"statsInboundUplinkDesc" = "Habilita la recopilación de estadísticas para el tráfico ascendente de todos los proxies de entrada."
"statsInboundDownlink" = "Estadísticas de Bajada de Entrada"
"statsInboundDownlinkDesc" = "Habilita la recopilación de estadísticas para el tráfico descendente de todos los proxies de entrada."
"statsOutboundUplink" = "Estadísticas de Subida de Salida"
"statsOutboundUplinkDesc" = "Habilita la recopilación de estadísticas para el tráfico ascendente de todos los proxies de salida."
"statsOutboundDownlink" = "Estadísticas de Bajada de Salida"
"statsOutboundDownlinkDesc" = "Habilita la recopilación de estadísticas para el tráfico descendente de todos los proxies de salida."

[pages.xray.rules]
"first" = "Primero"
"last" = "Último"
"up" = "Arriba"
"down" = "Abajo"
"source" = "Fuente"
"dest" = "Destino"
"inbound" = "Entrante"
"outbound" = "Saliente"
"balancer" = "Equilibrador"
"info" = "Información"
"add" = "Agregar Regla"
"edit" = "Editar Regla"
"useComma" = "Elementos separados por comas"

[pages.xray.outbound]
"addOutbound" = "Agregar salida"
"addReverse" = "Agregar reverso"
"editOutbound" = "Editar salida"
"editReverse" = "Editar reverso"
"tag" = "Etiqueta"
"tagDesc" = "etiqueta única"
"address" = "Dirección"
"reverse" = "Reverso"
"domain" = "Dominio"
"type" = "Tipo"
"bridge" = "puente"
"portal" = "portal"
"link" = "Enlace"
"intercon" = "Interconexión"
"settings" = "Configuración"
"accountInfo" = "Información de la Cuenta"
"outboundStatus" = "Estado de Salida"
"sendThrough" = "Enviar a través de"

[pages.xray.balancer]
"addBalancer" = "Agregar equilibrador"
"editBalancer" = "Editar balanceador"
"balancerStrategy" = "Estrategia"
"balancerSelectors" = "Selectores"
"tag" = "Etiqueta"
"tagDesc" = "etiqueta única"
"balancerDesc" = "No es posible utilizar balancerTag y outboundTag al mismo tiempo. Si se utilizan al mismo tiempo, sólo funcionará outboundTag."

[pages.xray.wireguard]
"secretKey" = "Llave secreta"
"publicKey" = "Llave pública"
"allowedIPs" = "IP permitidas"
"endpoint" = "Punto final"
"psk" = "Clave precompartida"
"domainStrategy" = "Estrategia de dominio"

[pages.xray.dns]
"enable" = "Habilitar DNS"
"enableDesc" = "Habilitar servidor DNS incorporado"
"tag" = "Etiqueta de Entrada DNS"
"tagDesc" = "Esta etiqueta estará disponible como una etiqueta de entrada en las reglas de enrutamiento."
"clientIp" = "IP del cliente"
"clientIpDesc" = "Se utiliza para notificar al servidor la ubicación IP especificada durante las consultas DNS"
"disableCache" = "Desactivar caché"
"disableCacheDesc" = "Desactiva el almacenamiento en caché de DNS"
"disableFallback" = "Desactivar respaldo"
"disableFallbackDesc" = "Desactiva las consultas DNS de respaldo"
"disableFallbackIfMatch" = "Desactivar respaldo si coincide"
"disableFallbackIfMatchDesc" = "Desactiva las consultas DNS de respaldo cuando se acierta en la lista de dominios coincidentes del servidor DNS"
"strategy" = "Estrategia de Consulta"
"strategyDesc" = "Estrategia general para resolver nombres de dominio"
"add" = "Agregar Servidor"
"edit" = "Editar Servidor"
"domains" = "Dominios"
"expectIPs" = "IPs esperadas"
"unexpectIPs" = "IPs inesperadas"
"useSystemHosts" = "Usar Hosts del sistema"
"useSystemHostsDesc" = "Usar el archivo hosts de un sistema instalado"
"usePreset" = "Usar plantilla"
"dnsPresetTitle" = "Plantillas DNS"
"dnsPresetFamily" = "Familiar"

[pages.xray.fakedns]
"add" = "Agregar DNS Falso"
"edit" = "Editar DNS Falso"
"ipPool" = "Subred del grupo de IP"
"poolSize" = "Tamaño del grupo"

[pages.settings.security]
"admin" = "Credenciales de administrador"
"twoFactor" = "Autenticación de dos factores"  
"twoFactorEnable" = "Habilitar 2FA"  
"twoFactorEnableDesc" = "Añade una capa adicional de autenticación para mayor seguridad."  
"twoFactorModalSetTitle" = "Activar autenticación de dos factores"
"twoFactorModalDeleteTitle" = "Desactivar autenticación de dos factores"
"twoFactorModalSteps" = "Para configurar la autenticación de dos factores, sigue estos pasos:"
"twoFactorModalFirstStep" = "1. Escanea este código QR en la aplicación de autenticación o copia el token cerca del código QR y pégalo en la aplicación"
"twoFactorModalSecondStep" = "2. Ingresa el código de la aplicación"
"twoFactorModalRemoveStep" = "Ingresa el código de la aplicación para eliminar la autenticación de dos factores."
"twoFactorModalChangeCredentialsTitle" = "Cambiar credenciales"
"twoFactorModalChangeCredentialsStep" = "Ingrese el código de la aplicación para cambiar las credenciales del administrador."
"twoFactorModalSetSuccess" = "La autenticación de dos factores se ha establecido con éxito"
"twoFactorModalDeleteSuccess" = "La autenticación de dos factores se ha eliminado con éxito"
"twoFactorModalError" = "Código incorrecto"

[pages.settings.toasts]
"modifySettings" = "Los parámetros han sido modificados."
"getSettings" = "Ocurrió un error al obtener los parámetros."
"modifyUserError" = "Ocurrió un error al cambiar las credenciales del administrador."
"modifyUser" = "Has cambiado exitosamente las credenciales del administrador."
"originalUserPassIncorrect" = "Nombre de usuario o contraseña original incorrectos"
"userPassMustBeNotEmpty" = "El nuevo nombre de usuario y la nueva contraseña no pueden estar vacíos"
"getOutboundTrafficError" = "Error al obtener el tráfico saliente"
"resetOutboundTrafficError" = "Error al reiniciar el tráfico saliente"

[tgbot]
"keyboardClosed" = "❌ ¡Teclado personalizado cerrado!"
"noResult" = "❗ ¡Sin resultados!"
"noQuery" = "❌ ¡Consulta no encontrada! ¡Por favor utiliza el comando nuevamente!"
"wentWrong" = "❌ ¡Algo salió mal!"
"noIpRecord" = "❗ ¡Sin Registro de IP!"
"noInbounds" = "❗ ¡No se encontraron entradas!"
"unlimited" = "♾ Ilimitado"
"add" = "Agregar"
"month" = "Mes"
"months" = "Meses"
"day" = "Día"
"days" = "Días"
"hours" = "Horas"
"unknown" = "Desconocido"
"inbounds" = "Entradas"
"clients" = "Clientes"
"offline" = "🔴 Sin conexión"
"online" = "🟢 En línea"

[tgbot.commands]
"unknown" = "❗ Comando desconocido"
"pleaseChoose" = "👇 Por favor elige:\r\n"
"help" = "🤖 ¡Bienvenido a este bot! Está diseñado para ofrecerte datos específicos del servidor y te permite hacer modificaciones según sea necesario.\r\n\r\n"
"start" = "👋 Hola <i>{{ .Firstname }}</i>.\r\n"
"welcome" = "🤖 Bienvenido al bot de gestión de <b>{{ .Hostname }}</b>.\r\n"
"status" = "✅ ¡El bot está bien!"
"usage" = "❗ ¡Por favor proporciona un texto para buscar!"
"getID" = "🆔 Tu ID: <code>{{ .ID }}</code>"
"helpAdminCommands" = "Para reiniciar Xray Core:\r\n<code>/restart</code>\r\n\r\nPara buscar un correo electrónico de cliente:\r\n<code>/usage [Correo electrónico]</code>\r\n\r\nPara buscar entradas (con estadísticas de cliente):\r\n<code>/inbound [Observación]</code>\r\n\r\nID de Chat de Telegram:\r\n<code>/id</code>"
"helpClientCommands" = "Para buscar estadísticas, utiliza el siguiente comando:\r\n<code>/usage [Correo electrónico]</code>\r\n\r\nID de Chat de Telegram:\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ ¡Operación exitosa!"
"restartFailed" = "❗ Error en la operación.\r\n\r\n<code>Error: {{ .Error }}</code>."
"xrayNotRunning" = "❗ Xray Core no está en ejecución."
"startDesc" = "Mostrar el menú principal"
"helpDesc" = "Ayuda del bot"
"statusDesc" = "Comprobar el estado del bot"
"idDesc" = "Mostrar tu ID de Telegram"

[tgbot.messages]
"cpuThreshold" = "🔴 El uso de CPU {{ .Percent }}% es mayor que el umbral {{ .Threshold }}%"
"selectUserFailed" = "❌ ¡Error al seleccionar usuario!"
"userSaved" = "✅ Usuario de Telegram guardado."
"loginSuccess" = "✅ Has iniciado sesión en el panel con éxito.\r\n"
"loginFailed" = "❗️ Falló el inicio de sesión en el panel.\r\n"
"report" = "🕰 Informes programados: {{ .RunTime }}\r\n"
"datetime" = "⏰ Fecha y Hora: {{ .DateTime }}\r\n"
"hostname" = "💻 Nombre del Host: {{ .Hostname }}\r\n"
"version" = "🚀 Versión de X-UI: {{ .Version }}\r\n"
"xrayVersion" = "📡 Versión de Xray: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6: {{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4: {{ .IPv4 }}\r\n"
"ip" = "🌐 IP: {{ .IP }}\r\n"
"ips" = "🔢 IPs:\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ Tiempo de actividad del servidor: {{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 Carga del servidor: {{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 Memoria del servidor: {{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 Conteo de TCP: {{ .Count }}\r\n"
"udpCount" = "🔸 Conteo de UDP: {{ .Count }}\r\n"
"traffic" = "🚦 Tráfico: {{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ Estado de Xray: {{ .State }}\r\n"
"username" = "👤 Nombre de usuario: {{ .Username }}\r\n"
"password" = "👤 Contraseña: {{ .Password }}\r\n"
"time" = "⏰ Hora: {{ .Time }}\r\n"
"inbound" = "📍 Inbound: {{ .Remark }}\r\n"
"port" = "🔌 Puerto: {{ .Port }}\r\n"
"expire" = "📅 Fecha de Vencimiento: {{ .Time }}\r\n"
"expireIn" = "📅 Vence en: {{ .Time }}\r\n"
"active" = "💡 Activo: {{ .Enable }}\r\n"
"enabled" = "🚨 Habilitado: {{ .Enable }}\r\n"
"online" = "🌐 Estado de conexión: {{ .Status }}\r\n"
"email" = "📧 Email: {{ .Email }}\r\n"
"upload" = "🔼 Subida: ↑{{ .Upload }}\r\n"
"download" = "🔽 Bajada: ↓{{ .Download }}\r\n"
"total" = "📊 Total: ↑↓{{ .UpDown }} / {{ .Total }}\r\n"
"TGUser" = "👤 Usuario de Telegram: {{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 Agotado {{ .Type }}:\r\n"
"exhaustedCount" = "🚨 Cantidad de Agotados {{ .Type }}:\r\n"
"onlinesCount" = "🌐 Clientes en línea: {{ .Count }}\r\n"
"disabled" = "🛑 Desactivado: {{ .Disabled }}\r\n"
"depleteSoon" = "🔜 Se agotará pronto: {{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 Hora de la Copia de Seguridad: {{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 Actualizado en: {{ .Time }}\r\n\r\n"
"yes" = "✅ Sí"
"no" = "❌ No"

"received_id" = "🔑📥 ID actualizado."
"received_password" = "🔑📥 Contraseña actualizada."
"received_email" = "📧📥 Correo electrónico actualizado."
"received_comment" = "💬📥 Comentario actualizado."
"id_prompt" = "🔑 ID predeterminado: {{ .ClientId }}\n\nIntroduce tu ID."
"pass_prompt" = "🔑 Contraseña predeterminada: {{ .ClientPassword }}\n\nIntroduce tu contraseña."
"email_prompt" = "📧 Correo electrónico predeterminado: {{ .ClientEmail }}\n\nIntroduce tu correo electrónico."
"comment_prompt" = "💬 Comentario predeterminado: {{ .ClientComment }}\n\nIntroduce tu comentario."
"inbound_client_data_id" = "🔄 Entrada: {{ .InboundRemark }}\n\n🔑 ID: {{ .ClientId }}\n📧 Correo: {{ .ClientEmail }}\n📊 Tráfico: {{ .ClientTraffic }}\n📅 Fecha de expiración: {{ .ClientExp }}\n🌐 Límite de IP: {{ .IpLimit }}\n💬 Comentario: {{ .ClientComment }}\n\n¡Ahora puedes agregar al cliente a la entrada!"
"inbound_client_data_pass" = "🔄 Entrada: {{ .InboundRemark }}\n\n🔑 Contraseña: {{ .ClientPass }}\n📧 Correo: {{ .ClientEmail }}\n📊 Tráfico: {{ .ClientTraffic }}\n📅 Fecha de expiración: {{ .ClientExp }}\n🌐 Límite de IP: {{ .IpLimit }}\n💬 Comentario: {{ .ClientComment }}\n\n¡Ahora puedes agregar al cliente a la entrada!"
"cancel" = "❌ ¡Proceso cancelado! \n\nPuedes /start de nuevo en cualquier momento. 🔄"
"error_add_client"  = "⚠️ Error:\n\n {{ .error }}"
"using_default_value"  = "Está bien, me quedaré con el valor predeterminado. 😊"
"incorrect_input" ="Tu entrada no es válida.\nLas frases deben ser continuas sin espacios.\nEjemplo correcto: aaaaaa\nEjemplo incorrecto: aaa aaa 🚫"
"AreYouSure" = "¿Estás seguro? 🤔"
"SuccessResetTraffic" = "📧 Correo: {{ .ClientEmail }}\n🏁 Resultado: ✅ Éxito"
"FailedResetTraffic" = "📧 Correo: {{ .ClientEmail }}\n🏁 Resultado: ❌ Fallido \n\n🛠️ Error: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 Proceso de reinicio de tráfico finalizado para todos los clientes."


[tgbot.buttons]
"closeKeyboard" = "❌ Cerrar Teclado"
"cancel" = "❌ Cancelar"
"cancelReset" = "❌ Cancelar Reinicio"
"cancelIpLimit" = "❌ Cancelar Límite de IP"
"confirmResetTraffic" = "✅ ¿Confirmar Reinicio de Tráfico?"
"confirmClearIps" = "✅ ¿Confirmar Limpiar IPs?"
"confirmRemoveTGUser" = "✅ ¿Confirmar Eliminar Usuario de Telegram?"
"confirmToggle" = "✅ ¿Confirmar habilitar/deshabilitar usuario?"
"dbBackup" = "Obtener Copia de Seguridad de BD"
"serverUsage" = "Uso del Servidor"
"getInbounds" = "Obtener Entradas"
"depleteSoon" = "Pronto se Agotará"
"clientUsage" = "Obtener Uso"
"onlines" = "Clientes en línea"
"commands" = "Comandos"
"refresh" = "🔄 Actualizar"
"clearIPs" = "❌ Limpiar IPs"
"removeTGUser" = "❌ Eliminar Usuario de Telegram"
"selectTGUser" = "👤 Seleccionar Usuario de Telegram"
"selectOneTGUser" = "👤 Selecciona un usuario de telegram:"
"resetTraffic" = "📈 Reiniciar Tráfico"
"resetExpire" = "📅 Cambiar fecha de Vencimiento"
"ipLog" = "🔢 Registro de IP"
"ipLimit" = "🔢 Límite de IP"
"setTGUser" = "👤 Establecer Usuario de Telegram"
"toggle" = "🔘 Habilitar / Deshabilitar"
"custom" = "🔢 Costumbre"
"confirmNumber" = "✅ Confirmar: {{ .Num }}"
"confirmNumberAdd" = "✅ Confirmar agregando: {{ .Num }}"
"limitTraffic" = "🚧 Límite de tráfico"
"getBanLogs" = "Registros de prohibición"
"allClients" = "Todos los Clientes"

"addClient" = "Añadir cliente"
"submitDisable" = "Enviar como deshabilitado ☑️"
"submitEnable" = "Enviar como habilitado ✅"
"use_default" = "🏷️ Usar por defecto"
"change_id" = "⚙️🔑 ID"
"change_password" = "⚙️🔑 Contraseña"
"change_email" = "⚙️📧 Correo electrónico"
"change_comment" = "⚙️💬 Comentario"
"ResetAllTraffics" = "Reiniciar todo el tráfico"
"SortedTrafficUsageReport" = "Informe de uso de tráfico ordenado"


[tgbot.answers]
"successfulOperation" = "✅ ¡Exitosa!"
"errorOperation" = "❗ Error en la Operación."
"getInboundsFailed" = "❌ Error al obtener las entradas"
"getClientsFailed" = "❌ No se pudo obtener los clientes."
"canceled" = "❌ {{ .Email }} : Operación cancelada."
"clientRefreshSuccess" = "✅ {{ .Email }} : Cliente actualizado exitosamente."
"IpRefreshSuccess" = "✅ {{ .Email }} : IPs actualizadas exitosamente."
"TGIdRefreshSuccess" = "✅ {{ .Email }} : Usuario de Telegram del cliente actualizado exitosamente."
"resetTrafficSuccess" = "✅ {{ .Email }} : Tráfico reiniciado exitosamente."
"setTrafficLimitSuccess" = "✅ {{ .Email }} : Límite de Tráfico guardado exitosamente."
"expireResetSuccess" = "✅ {{ .Email }} : Días de vencimiento reiniciados exitosamente."
"resetIpSuccess" = "✅ {{ .Email }} : Límite de IP {{ .Count }} guardado exitosamente."
"clearIpSuccess" = "✅ {{ .Email }} : IPs limpiadas exitosamente."
"getIpLog" = "✅ {{ .Email }} : Obtener Registro de IP."
"getUserInfo" = "✅ {{ .Email }} : Obtener Información de Usuario de Telegram."
"removedTGUserSuccess" = "✅ {{ .Email }} : Usuario de Telegram eliminado exitosamente."
"enableSuccess" = "✅ {{ .Email }} : Habilitado exitosamente."
"disableSuccess" = "✅ {{ .Email }} : Deshabilitado exitosamente."
"askToAddUserId" = "¡No se encuentra su configuración!\r\nPor favor, pídale a su administrador que use su ChatID de usuario de Telegram en su(s) configuración(es).\r\n\r\nSu ChatID de usuario: <code>{{ .TgUserID }}</code>"
"chooseClient" = "Elige un Cliente para Inbound {{ .Inbound }}"
"chooseInbound" = "Elige un Inbound"
