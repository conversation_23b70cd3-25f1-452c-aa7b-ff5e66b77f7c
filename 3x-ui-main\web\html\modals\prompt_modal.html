{{define "modals/promptModal"}}
<a-modal id="prompt-modal" v-model="promptModal.visible" :title="promptModal.title"
         :closable="true" @ok="promptModal.ok" :mask-closable="false"
         :confirm-loading="promptModal.confirmLoading"
         :ok-text="promptModal.okText" cancel-text='{{ i18n "cancel" }}' :class="themeSwitcher.currentTheme">
    <a-input id="prompt-modal-input" :type="promptModal.type"
             v-model="promptModal.value"
             :autosize="{minRows: 10, maxRows: 20}"
             @keydown.enter.native="promptModal.keyEnter"
             @keydown.ctrl.83="promptModal.ctrlS"></a-input>
</a-modal>

<script>

    const promptModal = {
        title: '',
        type: '',
        value: '',
        okText: '{{ i18n "sure"}}',
        visible: false,
        confirmLoading: false,
        keyEnter(e) {
            if (this.type !== 'textarea') {
                e.preventDefault();
                this.ok();
            }
        },
        ctrlS(e) {
            if (this.type === 'textarea') {
                e.preventDefault();
                promptModal.confirm(promptModal.value);
            }
        },
        ok() {
            promptModal.confirm(promptModal.value);
        },
        confirm() {},
        open({
            title = '',
            type = 'text',
            value = '',
            okText = '{{ i18n "sure"}}',
            confirm = () => {},
        }) {
            this.title = title;
            this.type = type;
            this.value = value;
            this.okText = okText;
            this.confirm = confirm;
            this.visible = true;
            promptModalApp.$nextTick(() => {
                document.querySelector('#prompt-modal-input').focus();
            });
        },
        close() {
            this.visible = false;
        },
        loading(loading=true) {
            this.confirmLoading = loading;
        },
    };

    const promptModalApp = new Vue({
        el: '#prompt-modal',
        data: {
            promptModal: promptModal,
        },
    });

</script>
{{end}}