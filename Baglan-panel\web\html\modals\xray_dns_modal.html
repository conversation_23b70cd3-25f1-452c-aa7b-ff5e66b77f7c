{{define "modals/dnsModal"}}
<a-modal id="dns-modal" v-model="dnsModal.visible" :title="dnsModal.title" @ok="dnsModal.ok" :closable="true"
  :mask-closable="false" :ok-text="dnsModal.okText" cancel-text='{{ i18n "close" }}'
  :class="themeSwitcher.currentTheme">
  <a-form :colon="false" :label-col="{ md: {span:8} }" :wrapper-col="{ md: {span:14} }">
    <a-form-item label='{{ i18n "pages.xray.outbound.address" }}'>
      <a-input v-model.trim="dnsModal.dnsServer.address"></a-input>
    </a-form-item>
    <a-form-item label='{{ i18n "pages.inbounds.port" }}'>
      <a-input-number v-model.number="dnsModal.dnsServer.port" :min="1" :max="65531"></a-input-number>
    </a-form-item>
    <a-form-item label='{{ i18n "pages.xray.dns.strategy" }}'>
      <a-select v-model="dnsModal.dnsServer.queryStrategy" :style="{ width: '100%' }"
        :dropdown-class-name="themeSwitcher.currentTheme">
        <a-select-option :value="l" :label="l" v-for="l in ['UseSystem', 'UseIP', 'UseIPv4', 'UseIPv6']"> [[ l ]] </a-select-option>
      </a-select>
    </a-form-item>
    <a-divider :style="{ margin: '5px 0' }"></a-divider>
    <a-form-item label='{{ i18n "pages.xray.dns.domains" }}'>
      <a-button icon="plus" size="small" type="primary" @click="dnsModal.dnsServer.domains.push('')"></a-button>
      <template v-for="(domain, index) in dnsModal.dnsServer.domains">
        <a-input v-model.trim="dnsModal.dnsServer.domains[index]">
          <a-button icon="minus" size="small" slot="addonAfter"
            @click="dnsModal.dnsServer.domains.splice(index,1)"></a-button>
        </a-input>
      </template>
    </a-form-item>
    <a-form-item label='{{ i18n "pages.xray.dns.expectIPs"}}'>
      <a-button icon="plus" size="small" type="primary" @click="dnsModal.dnsServer.expectIPs.push('')"></a-button>
      <template v-for="(domain, index) in dnsModal.dnsServer.expectIPs">
        <a-input v-model.trim="dnsModal.dnsServer.expectIPs[index]">
          <a-button icon="minus" size="small" slot="addonAfter"
            @click="dnsModal.dnsServer.expectIPs.splice(index,1)"></a-button>
        </a-input>
      </template>
    </a-form-item>
    <a-form-item label='{{ i18n "pages.xray.dns.unexpectIPs"}}'>
      <a-button icon="plus" size="small" type="primary" @click="dnsModal.dnsServer.unexpectedIPs.push('')"></a-button>
      <template v-for="(domain, index) in dnsModal.dnsServer.unexpectedIPs">
        <a-input v-model.trim="dnsModal.dnsServer.unexpectedIPs[index]">
          <a-button icon="minus" size="small" slot="addonAfter"
            @click="dnsModal.dnsServer.unexpectedIPs.splice(index,1)"></a-button>
        </a-input>
      </template>
    </a-form-item>
    <a-divider :style="{ margin: '5px 0' }"></a-divider>
    <a-form-item label='Skip Fallback'>
      <a-switch v-model="dnsModal.dnsServer.skipFallback"></a-switch>
    </a-form-item>
    <a-form-item label='Disable Cache'>
      <a-switch v-model="dnsModal.dnsServer.disableCache"></a-switch>
    </a-form-item>
    <a-form-item label='Final Query'>
      <a-switch v-model="dnsModal.dnsServer.finalQuery"></a-switch>
    </a-form-item>
  </a-form>
</a-modal>
<script>
  const defaultDnsObject = {
    address: "localhost",
    port: 53,
    domains: [],
    expectIPs: [],
    unexpectedIPs: [],
    queryStrategy: 'UseIP',
    skipFallback: true,
    disableCache: false,
    finalQuery: false
  }

  const dnsModal = {
    title: '',
    visible: false,
    okText: '{{ i18n "confirm" }}',
    isEdit: false,
    confirm: null,
    dnsServer: { ...defaultDnsObject },
    ok() {      
      ObjectUtil.execute(dnsModal.confirm, { ...dnsModal.dnsServer });
    },
    show({
      title = '',
      okText = '{{ i18n "confirm" }}',
      dnsServer,
      confirm = (dnsServer) => { },
      isEdit = false
    }) {
      this.title = title;
      this.okText = okText;
      this.confirm = confirm;
      this.visible = true;
      this.isEdit = isEdit;

      if (isEdit) {
        switch (typeof dnsServer) {
          case 'string':
            const dnsObj = { ...defaultDnsObject };

            dnsObj.address = dnsServer;

            this.dnsServer = dnsObj;
            break;
          case 'object':
            this.dnsServer = dnsServer;
            break;
        }
      } else {
        this.dnsServer = { ...defaultDnsObject };
      
        this.dnsServer.domains = [];
        this.dnsServer.expectIPs = [];
        this.dnsServer.unexpectedIPs = [];
      }
    },
    close() {
      dnsModal.visible = false;
    },
  };
  new Vue({
    delimiters: ['[[', ']]'],
    el: '#dns-modal',
    data: {
      dnsModal: dnsModal,
    }
  });
</script>
{{end}}