{{define "form/client"}}
<a-form layout="horizontal" v-if="client" :colon="false" :label-col="{ md: {span:8} }" :wrapper-col="{ md: {span:14} }">
    <a-form-item label='{{ i18n "pages.inbounds.enable" }}'>
        <a-switch v-model="client.enable"></a-switch>
    </a-form-item>
    <a-form-item>
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    <span>{{ i18n "pages.inbounds.emailDesc" }}</span>
                </template>
                {{ i18n "pages.inbounds.email" }}
                <a-icon type="sync" @click="client.email = RandomUtil.randomLowerAndNum(9)"></a-icon>
            </a-tooltip>
        </template>
        <a-input v-model.trim="client.email"></a-input>
    </a-form-item>
    <a-form-item v-if="inbound.protocol === Protocols.TROJAN || inbound.protocol === Protocols.SHADOWSOCKS">
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    <span>{{ i18n "reset" }}</span>
                </template>
                {{ i18n "password" }}
                <a-icon v-if="inbound.protocol === Protocols.SHADOWSOCKS" @click="client.password = RandomUtil.randomShadowsocksPassword(inbound.settings.method)" type="sync"></a-icon>
                <a-icon v-if="inbound.protocol === Protocols.TROJAN" @click="client.password = RandomUtil.randomSeq(10)"type="sync"> </a-icon>
            </a-tooltip>
        </template>
        <a-input v-model.trim="client.password"></a-input>
    </a-form-item>
    <a-form-item v-if="inbound.protocol === Protocols.VMESS || inbound.protocol === Protocols.VLESS">
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    <span>{{ i18n "reset" }}</span>
                </template>
                ID <a-icon @click="client.id = RandomUtil.randomUUID()" type="sync"></a-icon>
            </a-tooltip>
        </template>
        <a-input v-model.trim="client.id"></a-input>
    </a-form-item>
    <a-form-item v-if="inbound.protocol === Protocols.VMESS" label='{{ i18n "security" }}'>
        <a-select v-model="client.security" :dropdown-class-name="themeSwitcher.currentTheme">
            <a-select-option v-for="key in USERS_SECURITY" :value="key">[[ key ]]</a-select-option>
        </a-select>
    </a-form-item>
    <a-form-item v-if="client.email && app.subSettings.enable">
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    <span>{{ i18n "pages.inbounds.subscriptionDesc" }}</span>
                </template>
                Subscription
                <a-icon @click="client.subId = RandomUtil.randomLowerAndNum(16)" type="sync"></a-icon>
            </a-tooltip>
        </template>
        <a-input v-model.trim="client.subId"></a-input>
    </a-form-item>
    <a-form-item v-if="client.email && app.tgBotEnable">
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    <span>{{ i18n "pages.inbounds.telegramDesc" }}</span>
                </template>
                Telegram ChatID
                <a-icon type="question-circle"></a-icon>
            </a-tooltip>
        </template>
        <a-input-number :style="{ width: '50%' }" v-model.number="client.tgId" min="0"></a-input-number>
    </a-form-item>
    <a-form-item v-if="client.email" label='{{ i18n "comment" }}'>
        <a-input v-model.trim="client.comment"></a-input>
    </a-form-item>
    <a-form-item v-if="app.ipLimitEnable">
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    <span>{{ i18n "pages.inbounds.IPLimitDesc"}}</span>
                </template>
                    <span>{{ i18n "pages.inbounds.IPLimit"}} </span>
                <a-icon type="question-circle"></a-icon>
            </a-tooltip>
        </template>
        <a-input-number v-model.number="client.limitIp" min="0"></a-input-number>
    </a-form-item>
    <a-form-item v-if="app.ipLimitEnable && client.limitIp > 0 && client.email && isEdit">
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    <span>{{ i18n "pages.inbounds.IPLimitlogDesc" }}</span>
                </template>
                    <span>{{ i18n "pages.inbounds.IPLimitlog" }} </span>
                <a-icon type="question-circle"></a-icon>
            </a-tooltip>
        </template>
        <a-tooltip>
            <template slot="title">
                <span>{{ i18n "pages.inbounds.IPLimitlogclear" }}</span>
            </template>
            <span :style="{ color: '#FF4D4F' }">
                <a-icon type="delete" @click="clearDBClientIps(client.email)"></a-icon>
            </span>
        </a-tooltip>
        <a-form layout="block">
            <a-textarea id="clientIPs" readonly @click="getDBClientIps(client.email)" placeholder="Click To Get IPs"
                :auto-size="{ minRows: 5, maxRows: 10 }">
            </a-textarea>
        </a-form>
    </a-form-item>
    <a-form-item v-if="inbound.canEnableTlsFlow()" label='Flow'>
        <a-select v-model="client.flow" :dropdown-class-name="themeSwitcher.currentTheme">
            <a-select-option value="" selected>{{ i18n "none" }}</a-select-option>
            <a-select-option v-for="key in TLS_FLOW_CONTROL" :value="key">[[ key ]]</a-select-option>
        </a-select>
    </a-form-item>
    <a-form-item>
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    0 <span>{{ i18n "pages.inbounds.meansNoLimit" }}</span>
                </template>
                {{ i18n "pages.inbounds.totalFlow" }}
                <a-icon type="question-circle"></a-icon>
            </a-tooltip>
        </template>
        <a-input-number v-model.number="client._totalGB" :min="0"></a-input-number>
    </a-form-item>
    <a-form-item v-if="isEdit && clientStats" label='{{ i18n "usage" }}'>
        <a-tag :color="ColorUtils.clientUsageColor(clientStats, app.trafficDiff)">
            [[ SizeFormatter.sizeFormat(clientStats.up) ]] /
            [[ SizeFormatter.sizeFormat(clientStats.down) ]]
            ([[ SizeFormatter.sizeFormat(clientStats.up + clientStats.down) ]])
        </a-tag>
        <a-tooltip>
            <template slot="title">{{ i18n "pages.inbounds.resetTraffic" }}</template>
            <a-icon type="retweet"
                @click="resetClientTraffic(client.email,clientStats.inboundId,$event.target)"
                v-if="client.email.length > 0"></a-icon>
        </a-tooltip>
    </a-form-item>
    <a-form-item label='{{ i18n "pages.client.delayedStart" }}'>
        <a-switch v-model="delayedStart" @click="client._expiryTime=0"></a-switch>
    </a-form-item>
    <a-form-item v-if="delayedStart" label='{{ i18n "pages.client.expireDays" }}'>
        <a-input-number v-model.number="delayedExpireDays" :min="0"></a-input-number>
    </a-form-item>
    <a-form-item v-else>
        <template slot="label">
            <a-tooltip>
                <template slot="title">{{ i18n "pages.inbounds.leaveBlankToNeverExpire" }}</template>
                {{ i18n "pages.inbounds.expireDate" }}
                <a-icon type="question-circle"></a-icon>
            </a-tooltip>
        </template>
        <a-date-picker v-if="datepicker == 'gregorian'" :show-time="{ format: 'HH:mm:ss' }" format="YYYY-MM-DD HH:mm:ss"
            :dropdown-class-name="themeSwitcher.currentTheme" v-model="client._expiryTime"></a-date-picker>
        <a-persian-datepicker v-else placeholder='{{ i18n "pages.settings.datepickerPlaceholder" }}'
                            value="client._expiryTime" v-model="client._expiryTime"></a-persian-datepicker>
        <a-tag color="red" v-if="isEdit && isExpiry">Expired</a-tag>
    </a-form-item>
    <a-form-item v-if="client.expiryTime != 0">
        <template slot="label">
            <a-tooltip>
                <template slot="title">{{ i18n "pages.client.renewDesc" }}</template>
                {{ i18n "pages.client.renew" }}
                <a-icon type="question-circle"></a-icon>
            </a-tooltip>
        </template>
        <a-input-number v-model.number="client.reset" :min="0"></a-input-number>
    </a-form-item>
</a-form>
{{end}}