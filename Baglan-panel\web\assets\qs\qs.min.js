!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Qs=e()}}(function(){return function i(a,l,c){function f(r,e){if(!l[r]){if(!a[r]){var t="function"==typeof require&&require;if(!e&&t)return t(r,!0);if(s)return s(r,!0);var o=new Error("Cannot find module '"+r+"'");throw o.code="MODULE_NOT_FOUND",o}var n=l[r]={exports:{}};a[r][0].call(n.exports,function(e){return f(a[r][1][e]||e)},n,n.exports,i,a,l,c)}return l[r].exports}for(var s="function"==typeof require&&require,e=0;e<c.length;e++)f(c[e]);return f}({1:[function(e,r,t){"use strict";var o=String.prototype.replace,n=/%20/g;r.exports={default:"RFC3986",formatters:{RFC1738:function(e){return o.call(e,n,"+")},RFC3986:function(e){return e}},RFC1738:"RFC1738",RFC3986:"RFC3986"}},{}],2:[function(e,r,t){"use strict";var o=e("./stringify"),n=e("./parse"),i=e("./formats");r.exports={formats:i,parse:n,stringify:o}},{"./formats":1,"./parse":3,"./stringify":4}],3:[function(e,r,t){"use strict";var f=e("./utils"),p=Object.prototype.hasOwnProperty,d={allowDots:!1,allowPrototypes:!1,arrayLimit:20,decoder:f.decode,delimiter:"&",depth:5,parameterLimit:1e3,plainObjects:!1,strictNullHandling:!1},s=function(e,r,t){if(e){var o=t.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,n=/(\[[^[\]]*])/g,i=/(\[[^[\]]*])/.exec(o),a=i?o.slice(0,i.index):o,l=[];if(a){if(!t.plainObjects&&p.call(Object.prototype,a)&&!t.allowPrototypes)return;l.push(a)}for(var c=0;null!==(i=n.exec(o))&&c<t.depth;){if(c+=1,!t.plainObjects&&p.call(Object.prototype,i[1].slice(1,-1))&&!t.allowPrototypes)return;l.push(i[1])}return i&&l.push("["+o.slice(i.index)+"]"),function(e,r,t){for(var o=r,n=e.length-1;0<=n;--n){var i,a=e[n];if("[]"===a)i=(i=[]).concat(o);else{i=t.plainObjects?Object.create(null):{};var l="["===a.charAt(0)&&"]"===a.charAt(a.length-1)?a.slice(1,-1):a,c=parseInt(l,10);!isNaN(c)&&a!==l&&String(c)===l&&0<=c&&t.parseArrays&&c<=t.arrayLimit?(i=[])[c]=o:i[l]=o}o=i}return o}(l,r,t)}};r.exports=function(e,r){var t=r?f.assign({},r):{};if(null!==t.decoder&&void 0!==t.decoder&&"function"!=typeof t.decoder)throw new TypeError("Decoder has to be a function.");if(t.ignoreQueryPrefix=!0===t.ignoreQueryPrefix,t.delimiter="string"==typeof t.delimiter||f.isRegExp(t.delimiter)?t.delimiter:d.delimiter,t.depth="number"==typeof t.depth?t.depth:d.depth,t.arrayLimit="number"==typeof t.arrayLimit?t.arrayLimit:d.arrayLimit,t.parseArrays=!1!==t.parseArrays,t.decoder="function"==typeof t.decoder?t.decoder:d.decoder,t.allowDots="boolean"==typeof t.allowDots?t.allowDots:d.allowDots,t.plainObjects="boolean"==typeof t.plainObjects?t.plainObjects:d.plainObjects,t.allowPrototypes="boolean"==typeof t.allowPrototypes?t.allowPrototypes:d.allowPrototypes,t.parameterLimit="number"==typeof t.parameterLimit?t.parameterLimit:d.parameterLimit,t.strictNullHandling="boolean"==typeof t.strictNullHandling?t.strictNullHandling:d.strictNullHandling,""===e||null==e)return t.plainObjects?Object.create(null):{};for(var o="string"==typeof e?function(e,r){for(var t={},o=r.ignoreQueryPrefix?e.replace(/^\?/,""):e,n=r.parameterLimit===1/0?void 0:r.parameterLimit,i=o.split(r.delimiter,n),a=0;a<i.length;++a){var l,c,f=i[a],s=f.indexOf("]="),u=-1===s?f.indexOf("="):s+1;-1===u?(l=r.decoder(f,d.decoder),c=r.strictNullHandling?null:""):(l=r.decoder(f.slice(0,u),d.decoder),c=r.decoder(f.slice(u+1),d.decoder)),p.call(t,l)?t[l]=[].concat(t[l]).concat(c):t[l]=c}return t}(e,t):e,n=t.plainObjects?Object.create(null):{},i=Object.keys(o),a=0;a<i.length;++a){var l=i[a],c=s(l,o[l],t);n=f.merge(n,c,t)}return f.compact(n)}},{"./utils":5}],4:[function(e,r,t){"use strict";var A=e("./utils"),x=e("./formats"),N={brackets:function(e){return e+"[]"},indices:function(e,r){return e+"["+r+"]"},repeat:function(e){return e}},o=Date.prototype.toISOString,D={delimiter:"&",encode:!0,encoder:A.encode,encodeValuesOnly:!1,serializeDate:function(e){return o.call(e)},skipNulls:!1,strictNullHandling:!1},P=function e(r,t,o,n,i,a,l,c,f,s,u,p){var d=r;if("function"==typeof l)d=l(t,d);else if(d instanceof Date)d=s(d);else if(null===d){if(n)return a&&!p?a(t,D.encoder):t;d=""}if("string"==typeof d||"number"==typeof d||"boolean"==typeof d||A.isBuffer(d))return a?[u(p?t:a(t,D.encoder))+"="+u(a(d,D.encoder))]:[u(t)+"="+u(String(d))];var y,b=[];if(void 0===d)return b;if(Array.isArray(l))y=l;else{var m=Object.keys(d);y=c?m.sort(c):m}for(var g=0;g<y.length;++g){var v=y[g];i&&null===d[v]||(b=Array.isArray(d)?b.concat(e(d[v],o(t,v),o,n,i,a,l,c,f,s,u,p)):b.concat(e(d[v],t+(f?"."+v:"["+v+"]"),o,n,i,a,l,c,f,s,u,p)))}return b};r.exports=function(e,r){var t=e,o=r?A.assign({},r):{};if(null!==o.encoder&&void 0!==o.encoder&&"function"!=typeof o.encoder)throw new TypeError("Encoder has to be a function.");var n=void 0===o.delimiter?D.delimiter:o.delimiter,i="boolean"==typeof o.strictNullHandling?o.strictNullHandling:D.strictNullHandling,a="boolean"==typeof o.skipNulls?o.skipNulls:D.skipNulls,l="boolean"==typeof o.encode?o.encode:D.encode,c="function"==typeof o.encoder?o.encoder:D.encoder,f="function"==typeof o.sort?o.sort:null,s=void 0!==o.allowDots&&o.allowDots,u="function"==typeof o.serializeDate?o.serializeDate:D.serializeDate,p="boolean"==typeof o.encodeValuesOnly?o.encodeValuesOnly:D.encodeValuesOnly;if(void 0===o.format)o.format=x.default;else if(!Object.prototype.hasOwnProperty.call(x.formatters,o.format))throw new TypeError("Unknown format option provided.");var d,y,b=x.formatters[o.format];"function"==typeof o.filter?t=(y=o.filter)("",t):Array.isArray(o.filter)&&(d=y=o.filter);var m,g=[];if("object"!=typeof t||null===t)return"";m=o.arrayFormat in N?o.arrayFormat:"indices"in o?o.indices?"indices":"repeat":"indices";var v=N[m];d||(d=Object.keys(t)),f&&d.sort(f);for(var h=0;h<d.length;++h){var j=d[h];a&&null===t[j]||(g=g.concat(P(t[j],j,v,i,a,l?c:null,y,f,s,u,b,p)))}var O=g.join(n),w=!0===o.addQueryPrefix?"?":"";return 0<O.length?w+O:""}},{"./formats":1,"./utils":5}],5:[function(e,r,t){"use strict";var a=Object.prototype.hasOwnProperty,i=function(){for(var e=[],r=0;r<256;++r)e.push("%"+((r<16?"0":"")+r.toString(16)).toUpperCase());return e}(),l=function(e,r){for(var t=r&&r.plainObjects?Object.create(null):{},o=0;o<e.length;++o)void 0!==e[o]&&(t[o]=e[o]);return t};r.exports={arrayToObject:l,assign:function(e,t){return Object.keys(t).reduce(function(e,r){return e[r]=t[r],e},e)},compact:function(e){for(var r=[{obj:{o:e},prop:"o"}],t=[],o=0;o<r.length;++o)for(var n=r[o],i=n.obj[n.prop],a=Object.keys(i),l=0;l<a.length;++l){var c=a[l],f=i[c];"object"==typeof f&&null!==f&&-1===t.indexOf(f)&&(r.push({obj:i,prop:c}),t.push(f))}return function(e){for(var r;e.length;){var t=e.pop();if(r=t.obj[t.prop],Array.isArray(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}return r}(r)},decode:function(r){try{return decodeURIComponent(r.replace(/\+/g," "))}catch(e){return r}},encode:function(e){if(0===e.length)return e;for(var r="string"==typeof e?e:String(e),t="",o=0;o<r.length;++o){var n=r.charCodeAt(o);45===n||46===n||95===n||126===n||48<=n&&n<=57||65<=n&&n<=90||97<=n&&n<=122?t+=r.charAt(o):n<128?t+=i[n]:n<2048?t+=i[192|n>>6]+i[128|63&n]:n<55296||57344<=n?t+=i[224|n>>12]+i[128|n>>6&63]+i[128|63&n]:(o+=1,n=65536+((1023&n)<<10|1023&r.charCodeAt(o)),t+=i[240|n>>18]+i[128|n>>12&63]+i[128|n>>6&63]+i[128|63&n])}return t},isBuffer:function(e){return null!=e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},merge:function o(t,n,i){if(!n)return t;if("object"!=typeof n){if(Array.isArray(t))t.push(n);else{if("object"!=typeof t)return[t,n];(i.plainObjects||i.allowPrototypes||!a.call(Object.prototype,n))&&(t[n]=!0)}return t}if("object"!=typeof t)return[t].concat(n);var e=t;return Array.isArray(t)&&!Array.isArray(n)&&(e=l(t,i)),Array.isArray(t)&&Array.isArray(n)?(n.forEach(function(e,r){a.call(t,r)?t[r]&&"object"==typeof t[r]?t[r]=o(t[r],e,i):t.push(e):t[r]=e}),t):Object.keys(n).reduce(function(e,r){var t=n[r];return a.call(e,r)?e[r]=o(e[r],t,i):e[r]=t,e},e)}}},{}]},{},[2])(2)});