{{define "modals/dnsPresetsModal"}}
<a-modal id="dnsPresets-modal" v-model="dnsPresetsModal.visible" :title="dnsPresetsModal.title" :closable="true"
  :mask-closable="false" :footer="null" :class="themeSwitcher.currentTheme">
  <a-list class="ant-dns-presets-list" bordered :style="{ width: '100%' }">
    <a-list-item v-for="dns in dnsPresetsDatabase" :style="{ padding: '12px 16px' }">
      <a-row justify="space-between" align="middle">
        <a-col :span="12">
          <a-space direction="vertical" size="small">
            <span class="ant-dns-presets-list-name">[[ dns.name ]]</span>
            <a-tag :color="dns.family ? 'purple' : 'green'">[[ dns.family ? '{{ i18n "pages.xray.dns.dnsPresetFamily" }}' : 'DNS' ]]</a-tag>
          </a-space>
        </a-col>
        <a-col :span="12" :style="{ textAlign: 'right' }">
          <a-button type="primary" @click="dnsPresetsModal.install(dns.data)">{{ i18n "install" }}</a-button>
        </a-col>
      </a-row>
    </a-list-item>
  </a-list>
</a-modal>

<style>
  .dark .ant-dns-presets-list {
    border-color: var(--dark-color-stroke)
  }

  .dark .ant-dns-presets-list-name {
    color: var(--dark-color-text-primary);
  }
</style>

<script>
  const dnsPresetsDatabase = [
    {
      name: 'Google DNS',
      family: false,
      data: [
        "*******",
        "*******",
        "2001:4860:4860::8888",
        "2001:4860:4860::8844"
      ]
    },
    {
      name: 'Cloudflare DNS',
      family: false,
      data: [
        "*******",
        "*******",
        "2606:4700:4700::1111",
        "2606:4700:4700::1001"
      ]
    },
    {
      name: 'Adguard DNS',
      family: false,
      data: [
        "************",
        "************",
        "2a10:50c0::ad1:ff",
        "2a10:50c0::ad2:ff"
      ]
    },
    {
      name: 'Adguard Family DNS',
      family: true,
      data: [
        "************",
        "************",
        "2a10:50c0::ad1:ff",
        "2a10:50c0::ad2:ff"
      ]
    },
    {
      name: 'Cloudflare Family DNS',
      family: true,
      data: [
        "*******",
        "*******",
        "2606:4700:4700::1113",
        "2606:4700:4700::1003"
      ]
    }
  ]

  const dnsPresetsModal = {
    title: '',
    visible: false,
    selected: null,
    install(selectedPreset) {
      return ObjectUtil.execute(dnsPresetsModal.selected, selectedPreset);
    },
    show({ title = '', selected = (selectedPreset) => { }, isEdit = false }) {
      this.title = title;
      this.selected = selected;
      this.visible = true;
    },
    close() {
      dnsPresetsModal.visible = false;
    },
  };

  new Vue({
    delimiters: ['[[', ']]'],
    el: '#dnsPresets-modal',
    data: {
      dnsPresetsModal: dnsPresetsModal,
    }
  });
</script>
{{end}}