name: Release 3X-UI for Docker
on:
  workflow_dispatch:
  push:
    tags:
      - "v*.*.*"

jobs:
  build:
    runs-on: ubuntu-22.04

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true
   
    - name: Docker meta
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: |
          hsanaeii/3x-ui
          ghcr.io/mhsanaei/3x-ui
        tags: |
          type=ref,event=branch
          type=ref,event=tag
          type=pep440,pattern={{version}}

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        install: true

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_HUB_USERNAME }}
        password: ${{ secrets.DOCKER_HUB_TOKEN }}

    - name: Login to GHCR
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.repository_owner }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v6
      with:
        context: .
        push: true
        platforms: linux/amd64, linux/arm64/v8, linux/arm/v7, linux/arm/v6, linux/386
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}