{{ define "page/head_start" }}
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="robots" content="noindex,nofollow">
  <link rel="stylesheet" href="{{ .base_path }}assets/ant-design-vue/antd.min.css">
  <link rel="stylesheet" href="{{ .base_path }}assets/css/custom.min.css?{{ .cur_ver }}">
  <style>
    [v-cloak] {
      display: none;
    }
    /* vazirmatn-regular - arabic_latin_latin-ext */
    @font-face {
      font-display: swap;
      font-family: 'Vazirmatn';
      font-style: normal;
      font-weight: 400;
      src: url('{{ .base_path }}assets/Vazirmatn-UI-NL-Regular.woff2') format('woff2');
      unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC, U+0030-0039;
    }
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Vazirmatn', 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
  </style>
  <title>{{ .host }} – {{ i18n .title}}</title>
{{ end }}

{{ define "page/head_end" }}
</head>
{{ end }}

{{ define "page/body_start" }}
<body>
  <div id="message"></div>
{{ end }}

{{ define "page/body_scripts" }}
<script src="{{ .base_path }}assets/vue/vue.min.js?{{ .cur_ver }}"></script>
<script src="{{ .base_path }}assets/moment/moment.min.js"></script>
<script src="{{ .base_path }}assets/ant-design-vue/antd.min.js"></script>
<script src="{{ .base_path }}assets/axios/axios.min.js?{{ .cur_ver }}"></script>
<script src="{{ .base_path }}assets/qs/qs.min.js"></script>
<script src="{{ .base_path }}assets/js/axios-init.js?{{ .cur_ver }}"></script>
<script src="{{ .base_path }}assets/js/util/date-util.js?{{ .cur_ver }}"></script>
<script src="{{ .base_path }}assets/js/util/index.js?{{ .cur_ver }}"></script>
<script>
  const basePath = '{{ .base_path }}';
  axios.defaults.baseURL = basePath;
</script>
{{ end }}
  
{{ define "page/body_end" }}
</body>
</html>
{{ end }}