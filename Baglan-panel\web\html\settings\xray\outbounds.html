{{define "settings/xray/outbounds"}}
<a-space direction="vertical" size="middle">
    <a-row>
        <a-col :xs="12" :sm="12" :lg="12">
            <a-space direction="horizontal" size="small">
                <a-button type="primary" icon="plus" @click="addOutbound()">
                    {{ i18n "pages.xray.outbound.addOutbound" }}
                </a-button>
                <a-button type="primary" icon="cloud" @click="showWarp()">WARP</a-button>
            </a-space>
        </a-col>
        <a-col :xs="12" :sm="12" :lg="12" :style="{ textAlign: 'right' }">
            <a-button-group>
                <a-button icon="sync" @click="refreshOutboundTraffic()" :loading="refreshing"></a-button>
                <a-popconfirm placement="topRight" @confirm="resetOutboundTraffic(-1)"
                    title='{{ i18n "pages.inbounds.resetTrafficContent"}}'
                    :overlay-class-name="themeSwitcher.currentTheme" ok-text='{{ i18n "reset"}}'
                    cancel-text='{{ i18n "cancel"}}'>
                    <a-icon slot="icon" type="question-circle-o"
                        :style="{ color: themeSwitcher.isDarkTheme ? '#008771' : '#008771' }"></a-icon>
                    <a-button icon="retweet"></a-button>
                </a-popconfirm>
            </a-button-group>
        </a-col>
    </a-row>
    <a-table :columns="outboundColumns" bordered :row-key="r => r.key" :data-source="outboundData"
        :scroll="isMobile ? {} : { x: 800 }" :pagination="false" :indent-size="0"
        :locale='{ filterConfirm: `{{ i18n "confirm" }}`, filterReset: `{{ i18n "reset" }}` }'>
        <template slot="action" slot-scope="text, outbound, index">
            <span>[[ index+1 ]]</span>
            <a-dropdown :trigger="['click']">
                <a-icon @click="e => e.preventDefault()" type="more"
                    :style="{ fontSize: '16px', textDecoration: 'bold' }"></a-icon>
                <a-menu slot="overlay" :theme="themeSwitcher.currentTheme">
                    <a-menu-item v-if="index>0" @click="setFirstOutbound(index)">
                        <a-icon type="vertical-align-top"></a-icon>
                        <span>{{ i18n "pages.xray.rules.first"}}</span>
                    </a-menu-item>
                    <a-menu-item @click="editOutbound(index)">
                        <a-icon type="edit"></a-icon>
                        <span>{{ i18n "edit" }}</span>
                    </a-menu-item>
                    <a-menu-item @click="resetOutboundTraffic(index)">
                        <span>
                            <a-icon type="retweet"></a-icon>
                            <span>{{ i18n "pages.inbounds.resetTraffic"}}</span>
                        </span>
                    </a-menu-item>
                    <a-menu-item @click="deleteOutbound(index)">
                        <span :style="{ color: '#FF4D4F' }">
                            <a-icon type="delete"></a-icon>
                            <span>{{ i18n "delete"}}</span>
                        </span>
                    </a-menu-item>
                </a-menu>
            </a-dropdown>
        </template>
        <template slot="address" slot-scope="text, outbound, index">
            <p :style="{ margin: '0 5px' }" v-for="addr in findOutboundAddress(outbound)">[[ addr ]]</p>
        </template>
        <template slot="protocol" slot-scope="text, outbound, index">
            <a-tag :style="{ margin: '0' }" color="purple">[[ outbound.protocol ]]</a-tag>
            <template
                v-if="[Protocols.VMess, Protocols.VLESS, Protocols.Trojan, Protocols.Shadowsocks].includes(outbound.protocol)">
                <a-tag :style="{ margin: '0' }" color="blue">[[ outbound.streamSettings.network ]]</a-tag>
                <a-tag :style="{ margin: '0' }" v-if="outbound.streamSettings.security=='tls'" color="green">tls</a-tag>
                <a-tag :style="{ margin: '0' }" v-if="outbound.streamSettings.security=='reality'"
                    color="green">reality</a-tag>
            </template>
        </template>
        <template slot="traffic" slot-scope="text, outbound, index">
            <a-tag color="green">[[ findOutboundTraffic(outbound) ]]</a-tag>
        </template>
    </a-table>
</a-space>
{{end}}