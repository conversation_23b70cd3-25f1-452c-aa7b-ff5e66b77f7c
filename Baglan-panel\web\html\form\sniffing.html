{{define "form/sniffing"}}
<a-form :colon="false" :label-col="{ md: {span:8} }" :wrapper-col="{ md: {span:14} }">
  <a-form-item>
    <span slot="label">
      {{ i18n "enabled" }}
        <a-tooltip>
            <template slot="title">
                <span>{{ i18n "pages.inbounds.noRecommendKeepDefault" }}</span>
            </template>
            <a-icon type="question-circle"></a-icon>
        </a-tooltip>
    </span>
    <a-switch v-model="inbound.sniffing.enabled"></a-switch>
  </a-form-item>
  <template v-if="inbound.sniffing.enabled">
    <a-form-item :wrapper-col="{span:24}">
      <a-checkbox-group v-model="inbound.sniffing.destOverride">
        <a-checkbox v-for="key,value in SNIFFING_OPTION" :value="key">[[ value ]]</a-checkbox>
      </a-checkbox-group>
    </a-form-item>
    <a-form-item label='Metadata Only'>
      <a-switch v-model="inbound.sniffing.metadataOnly"></a-switch>
    </a-form-item>
    <a-form-item label='Route Only'>
      <a-switch v-model="inbound.sniffing.routeOnly"></a-switch>
    </a-form-item>
  </template>
</a-form>
{{end}}
