"username" = "使用者名稱"
"password" = "密碼"
"login" = "登入"
"confirm" = "確定"
"cancel" = "取消"
"close" = "關閉"
"create" = "建立"
"update" = "更新"
"copy" = "複製"
"copied" = "已複製"
"download" = "下載"
"remark" = "備註"
"enable" = "啟用"
"protocol" = "協議"
"search" = "搜尋"
"filter" = "篩選"
"loading" = "載入中..."
"second" = "秒"
"minute" = "分鐘"
"hour" = "小時"
"day" = "天"
"check" = "檢視"
"indefinite" = "無限期"
"unlimited" = "無限制"
"none" = "無"
"qrCode" = "二維碼"
"info" = "更多資訊"
"edit" = "編輯"
"delete" = "刪除"
"reset" = "重置"
"noData" = "無數據。"
"copySuccess" = "複製成功"
"sure" = "確定"
"encryption" = "加密"
"useIPv4ForHost" = "使用 IPv4 連接主機"
"transmission" = "傳輸"
"host" = "主機"
"path" = "路徑"
"camouflage" = "偽裝"
"status" = "狀態"
"enabled" = "開啟"
"disabled" = "關閉"
"depleted" = "耗盡"
"depletingSoon" = "即將耗盡"
"offline" = "離線"
"online" = "線上"
"domainName" = "域名"
"monitor" = "監聽"
"certificate" = "憑證"
"fail" = "失敗"
"comment" = "評論"
"success" = "成功"
"getVersion" = "獲取版本"
"install" = "安裝"
"clients" = "客戶端"
"usage" = "使用情況"
"twoFactorCode" = "代碼"
"remained" = "剩餘"
"security" = "安全"
"secAlertTitle" = "安全警報"
"secAlertSsl" = "此連線不安全。在啟用 TLS 進行資料保護之前，請勿輸入敏感資訊。"
"secAlertConf" = "某些設定易受攻擊。建議加強安全協議以防止潛在漏洞。"
"secAlertSSL" = "面板缺少安全連線。請安裝 TLS 證書以保護資料安全。"
"secAlertPanelPort" = "面板預設埠存在安全風險。請配置隨機埠或特定埠。"
"secAlertPanelURI" = "面板預設 URI 路徑不安全。請配置複雜的 URI 路徑。"
"secAlertSubURI" = "訂閱預設 URI 路徑不安全。請配置複雜的 URI 路徑。"
"secAlertSubJsonURI" = "訂閱 JSON 預設 URI 路徑不安全。請配置複雜的 URI 路徑。"
"emptyDnsDesc" = "未添加DNS伺服器。"
"emptyFakeDnsDesc" = "未添加Fake DNS伺服器。"
"emptyBalancersDesc" = "未添加負載平衡器。"
"emptyReverseDesc" = "未添加反向代理。"
"somethingWentWrong" = "發生錯誤"

[menu]
"theme" = "主題"
"dark" = "深色"
"ultraDark" = "超深色"
"dashboard" = "系統狀態"
"inbounds" = "入站列表"
"settings" = "面板設定"
"xray" = "Xray 設定"
"logout" = "退出登入"
"link" = "管理"

[pages.login]
"hello" = "你好"
"title" = "歡迎"
"loginAgain" = "登入時效已過，請重新登入"

[pages.login.toasts]
"invalidFormData" = "資料格式錯誤"
"emptyUsername" = "請輸入使用者名稱"
"emptyPassword" = "請輸入密碼"
"wrongUsernameOrPassword" = "用戶名、密碼或雙重驗證碼無效。"  
"successLogin" = "您已成功登入您的帳戶。"

[pages.index]
"title" = "系統狀態"
"cpu" = "CPU"
"logicalProcessors" = "邏輯處理器"
"frequency" = "頻率"
"swap" = "交換空間"
"storage" = "儲存"
"memory" = "記憶體"
"threads" = "執行緒"
"xrayStatus" = "Xray"
"stopXray" = "停止"
"restartXray" = "重啟"
"xraySwitch" = "版本"
"xraySwitchClick" = "選擇你要切換到的版本"
"xraySwitchClickDesk" = "請謹慎選擇，因為較舊版本可能與當前配置不相容"
"xrayStatusUnknown" = "未知"
"xrayStatusRunning" = "運行中"
"xrayStatusStop" = "停止"
"xrayStatusError" = "錯誤"
"xrayErrorPopoverTitle" = "執行Xray時發生錯誤"
"operationHours" = "系統正常執行時間"
"systemLoad" = "系統負載"
"systemLoadDesc" = "過去 1、5 和 15 分鐘的系統平均負載"
"connectionTcpCountDesc" = "系統中所有 TCP 連線數"
"connectionUdpCountDesc" = "系統中所有 UDP 連線數"
"connectionCount" = "連線數"
"ipAddresses" = "IP地址"
"toggleIpVisibility" = "切換IP可見性"
"overallSpeed" = "整體速度"
"upload" = "上傳"
"download" = "下載"
"totalData" = "總數據"
"sent" = "已發送"
"received" = "已接收"
"documentation" = "文件"
"xraySwitchVersionDialog" = "您確定要變更Xray版本嗎？"
"xraySwitchVersionDialogDesc" = "這將會把Xray版本變更為#version#。"
"xraySwitchVersionPopover" = "Xray 更新成功"
"geofileUpdateDialog" = "您確定要更新地理檔案嗎？"
"geofileUpdateDialogDesc" = "這將更新 #filename# 檔案。"
"geofileUpdatePopover" = "地理檔案更新成功"
"dontRefresh" = "安裝中，請勿重新整理此頁面"
"logs" = "日誌"
"config" = "配置"
"backup" = "備份和恢復"
"backupTitle" = "備份和恢復資料庫"
"exportDatabase" = "備份"
"exportDatabaseDesc" = "點擊下載包含當前資料庫備份的 .db 文件到您的設備。"
"importDatabase" = "恢復"
"importDatabaseDesc" = "點擊選擇並上傳設備中的 .db 文件以從備份恢復資料庫。"
"importDatabaseSuccess" = "資料庫匯入成功"
"importDatabaseError" = "匯入資料庫時發生錯誤"
"readDatabaseError" = "讀取資料庫時發生錯誤"
"getDatabaseError" = "檢索資料庫時發生錯誤"
"getConfigError" = "檢索設定檔時發生錯誤"

[pages.inbounds]
"title" = "入站列表"
"totalDownUp" = "總上傳 / 下載"
"totalUsage" = "總用量"
"inboundCount" = "入站數量"
"operate" = "選單"
"enable" = "啟用"
"remark" = "備註"
"protocol" = "協議"
"port" = "埠"
"traffic" = "流量"
"details" = "詳細資訊"
"transportConfig" = "傳輸配置"
"expireDate" = "到期時間"
"resetTraffic" = "重置流量"
"addInbound" = "新增入站"
"generalActions" = "通用操作"
"autoRefresh" = "自動刷新"
"autoRefreshInterval" = "間隔"
"create" = "新增"
"update" = "修改"
"modifyInbound" = "修改入站"
"deleteInbound" = "刪除入站"
"deleteInboundContent" = "確定要刪除入站嗎？"
"deleteClient" = "刪除客戶端"
"deleteClientContent" = "確定要刪除客戶端嗎？"
"resetTrafficContent" = "確定要重置流量嗎？"
"inboundUpdateSuccess" = "入站連接已成功更新。"
"inboundCreateSuccess" = "入站連接已成功建立。"
"copyLink" = "複製連結"
"address" = "地址"
"network" = "網路"
"destinationPort" = "目標埠"
"targetAddress" = "目標地址"
"monitorDesc" = "留空表示監聽所有 IP"
"meansNoLimit" = "= 無限制（單位：GB)"
"totalFlow" = "總流量"
"leaveBlankToNeverExpire" = "留空表示永不過期"
"noRecommendKeepDefault" = "建議保留預設值"
"certificatePath" = "檔案路徑"
"certificateContent" = "檔案內容"
"publicKey" = "公鑰"
"privatekey" = "私鑰"
"clickOnQRcode" = "點選二維碼複製"
"client" = "客戶"
"export" = "匯出連結"
"clone" = "複製"
"cloneInbound" = "複製"
"cloneInboundContent" = "此入站規則除埠（Port）、監聽 IP（Listening IP）和客戶端（Clients）以外的所有配置都將應用於克隆"
"cloneInboundOk" = "建立克隆"
"resetAllTraffic" = "重置所有入站流量"
"resetAllTrafficTitle" = "重置所有入站流量"
"resetAllTrafficContent" = "確定要重置所有入站流量嗎？"
"resetInboundClientTraffics" = "重置客戶端流量"
"resetInboundClientTrafficTitle" = "重置所有客戶端流量"
"resetInboundClientTrafficContent" = "確定要重置此入站客戶端的所有流量嗎？"
"resetAllClientTraffics" = "重置所有客戶端流量"
"resetAllClientTrafficTitle" = "重置所有客戶端流量"
"resetAllClientTrafficContent" = "確定要重置所有客戶端的所有流量嗎？"
"delDepletedClients" = "刪除流量耗盡的客戶端"
"delDepletedClientsTitle" = "刪除流量耗盡的客戶端"
"delDepletedClientsContent" = "確定要刪除所有流量耗盡的客戶端嗎？"
"email" = "電子郵件"
"emailDesc" = "電子郵件必須完全唯一"
"IPLimit" = "IP 限制"
"IPLimitDesc" = "如果數量超過設定值，則禁用入站流量。（0 = 禁用）"
"IPLimitlog" = "IP 日誌"
"IPLimitlogDesc" = "IP 歷史日誌（要啟用被禁用的入站流量，請清除日誌）"
"IPLimitlogclear" = "清除日誌"
"setDefaultCert" = "從面板設定證書"
"telegramDesc" = "請提供Telegram聊天ID。（在機器人中使用'/id'命令）或（@userinfobot"
"subscriptionDesc" = "要找到你的訂閱 URL，請導航到“詳細資訊”。此外，你可以為多個客戶端使用相同的名稱。"
"info" = "資訊"
"same" = "相同"
"inboundData" = "入站資料"
"exportInbound" = "匯出入站規則"
"import"="匯入"
"importInbound" = "匯入入站規則"

[pages.client]
"add" = "新增客戶端"
"edit" = "編輯客戶端"
"submitAdd" = "新增客戶端"
"submitEdit" = "儲存修改"
"clientCount" = "客戶端數量"
"bulk" = "批量建立"
"method" = "方法"
"first" = "置頂"
"last" = "置底"
"prefix" = "字首"
"postfix" = "字尾"
"delayedStart" = "首次使用後開始"
"expireDays" = "期間"
"days" = "天"
"renew" = "自動續訂"
"renewDesc" = "到期後自動續訂。(0 = 禁用)(單位: 天)"

[pages.inbounds.toasts]
"obtain" = "獲取"
"updateSuccess" = "更新成功"
"logCleanSuccess" = "日誌已清除"
"inboundsUpdateSuccess" = "入站連接已成功更新"
"inboundUpdateSuccess" = "入站連接已成功更新"
"inboundCreateSuccess" = "入站連接已成功建立"
"inboundDeleteSuccess" = "入站連接已成功刪除"
"inboundClientAddSuccess" = "已新增入站客戶端"
"inboundClientDeleteSuccess" = "入站客戶端已刪除"
"inboundClientUpdateSuccess" = "入站客戶端已更新"
"delDepletedClientsSuccess" = "所有耗盡客戶端已刪除"
"resetAllClientTrafficSuccess" = "客戶端所有流量已重置"
"resetAllTrafficSuccess" = "所有流量已重置"
"resetInboundClientTrafficSuccess" = "流量已重置"
"trafficGetError" = "取得流量資料時發生錯誤"
"getNewX25519CertError" = "取得X25519憑證時發生錯誤。"

[pages.inbounds.stream.general]
"request" = "請求"
"response" = "響應"
"name" = "名稱"
"value" = "值"

[pages.inbounds.stream.tcp]
"version" = "版本"
"method" = "方法"
"path" = "路徑"
"status" = "狀態"
"statusDescription" = "狀態說明"
"requestHeader" = "請求頭"
"responseHeader" = "響應頭"

[pages.settings]
"title" = "面板設定"
"save" = "儲存"
"infoDesc" = "此處的所有更改都需要儲存並重啟面板才能生效"
"restartPanel" = "重啟面板"
"restartPanelDesc" = "確定要重啟面板嗎？若重啟後無法訪問面板，請前往伺服器檢視面板日誌資訊"
"restartPanelSuccess" = "面板已成功重新啟動"
"actions" = "操作"
"resetDefaultConfig" = "重置為預設配置"
"panelSettings" = "常規"
"securitySettings" = "安全設定"
"TGBotSettings" = "Telegram 機器人配置"
"panelListeningIP" = "面板監聽 IP"
"panelListeningIPDesc" = "預設留空監聽所有 IP"
"panelListeningDomain" = "面板監聽域名"
"panelListeningDomainDesc" = "預設情況下留空以監視所有域名和 IP 地址"
"panelPort" = "面板監聽埠"
"panelPortDesc" = "重啟面板生效"
"publicKeyPath" = "面板證書公鑰檔案路徑"
"publicKeyPathDesc" = "填寫一個 '/' 開頭的絕對路徑"
"privateKeyPath" = "面板證書金鑰檔案路徑"
"privateKeyPathDesc" = "填寫一個 '/' 開頭的絕對路徑"
"panelUrlPath" = "面板 url 根路徑"
"panelUrlPathDesc" = "必須以 '/' 開頭，以 '/' 結尾"
"pageSize" = "分頁大小"
"pageSizeDesc" = "定義入站表的頁面大小。設定 0 表示禁用"
"remarkModel" = "備註模型和分隔符"
"datepicker" = "日期選擇器"
"datepickerPlaceholder" = "選擇日期"
"datepickerDescription" = "選擇器日曆類型指定到期日期"
"sampleRemark" = "備註示例"
"oldUsername" = "原使用者名稱"
"currentPassword" = "原密碼"
"newUsername" = "新使用者名稱"
"newPassword" = "新密碼"
"telegramBotEnable" = "啟用 Telegram 機器人"
"telegramBotEnableDesc" = "啟用 Telegram 機器人功能"
"telegramToken" = "Telegram 機器人令牌（token）"
"telegramTokenDesc" = "從 '@BotFather' 獲取的 Telegram 機器人令牌"
"telegramProxy" = "SOCKS5 Proxy"
"telegramProxyDesc" = "啟用 SOCKS5 代理連線到 Telegram（根據指南調整設定）"
"telegramAPIServer" = "Telegram API Server"
"telegramAPIServerDesc" = "要使用的 Telegram API 伺服器。留空以使用預設伺服器。"
"telegramChatId" = "管理員聊天 ID"
"telegramChatIdDesc" = "Telegram 管理員聊天 ID (多個以逗號分隔)（可通過 @userinfobot 獲取，或在機器人中使用 '/id' 命令獲取）"
"telegramNotifyTime" = "通知時間"
"telegramNotifyTimeDesc" = "設定週期性的 Telegram 機器人通知時間（使用 crontab 時間格式）"
"tgNotifyBackup" = "資料庫備份"
"tgNotifyBackupDesc" = "傳送帶有報告的資料庫備份檔案"
"tgNotifyLogin" = "登入通知"
"tgNotifyLoginDesc" = "當有人試圖登入你的面板時顯示使用者名稱、IP 地址和時間"
"sessionMaxAge" = "會話時長"
"sessionMaxAgeDesc" = "保持登入狀態的時長（單位：分鐘）"
"expireTimeDiff" = "到期通知閾值"
"expireTimeDiffDesc" = "達到此閾值時，將收到有關到期時間的通知（單位：天）"
"trafficDiff" = "流量耗盡閾值"
"trafficDiffDesc" = "達到此閾值時，將收到有關流量耗盡的通知（單位：GB）"
"tgNotifyCpu" = "CPU 負載通知閾值"
"tgNotifyCpuDesc" = "CPU 負載超過此閾值時，將收到通知（單位：%）"
"timeZone" = "時區"
"timeZoneDesc" = "定時任務將按照該時區的時間執行"
"subSettings" = "訂閱設定"
"subEnable" = "啟用訂閱服務"
"subEnableDesc" = "啟用訂閱服務功能"
"subTitle" = "訂閱標題"
"subTitleDesc" = "在VPN客戶端中顯示的標題"
"subListen" = "監聽 IP"
"subListenDesc" = "訂閱服務監聽的 IP 地址（留空表示監聽所有 IP）"
"subPort" = "監聽埠"
"subPortDesc" = "訂閱服務監聽的埠號（必須是未使用的埠）"
"subCertPath" = "公鑰路徑"
"subCertPathDesc" = "訂閱服務使用的公鑰檔案路徑（以 '/' 開頭）"
"subKeyPath" = "私鑰路徑"
"subKeyPathDesc" = "訂閱服務使用的私鑰檔案路徑（以 '/' 開頭）"
"subPath" = "URI 路徑"
"subPathDesc" = "訂閱服務使用的 URI 路徑（以 '/' 開頭，以 '/' 結尾）"
"subDomain" = "監聽域名"
"subDomainDesc" = "訂閱服務監聽的域名（留空表示監聽所有域名和 IP）"
"subUpdates" = "更新間隔"
"subUpdatesDesc" = "客戶端應用中訂閱 URL 的更新間隔（單位：小時）"
"subEncrypt" = "編碼"
"subEncryptDesc" = "訂閱服務返回的內容將採用 Base64 編碼"
"subShowInfo" = "顯示使用資訊"
"subShowInfoDesc" = "客戶端應用中將顯示剩餘流量和日期資訊"
"subURI" = "反向代理 URI"
"subURIDesc" = "用於代理後面的訂閱 URL 的 URI 路徑"
"externalTrafficInformEnable" = "外部交通通知"
"externalTrafficInformEnableDesc" = "每次流量更新時通知外部 API"
"externalTrafficInformURI" = "外部流量通知 URI"
"externalTrafficInformURIDesc" = "流量更新將會傳送到此 URI"
"fragment" = "分片"
"fragmentDesc" = "啟用 TLS hello 資料包分片"
"fragmentSett" = "設定"
"noisesDesc" = "啟用 Noises."
"noisesSett" = "Noises 設定"
"mux" = "多路複用器"
"muxDesc" = "在已建立的資料流內傳輸多個獨立的資料流"
"muxSett" = "複用器設定"
"direct" = "直接連線"
"directDesc" = "直接與特定國家的域或IP範圍建立連線"
"notifications" = "通知"
"certs" = "證書"
"externalTraffic" = "外部流量"
"dateAndTime" = "日期和時間"
"proxyAndServer" = "代理和伺服器"
"intervals" = "間隔"
"information" = "資訊"
"language" = "語言"
"telegramBotLanguage" = "Telegram 機器人語言"

[pages.xray]
"title" = "Xray 配置"
"save" = "儲存"
"restart" = "重新啟動 Xray"
"restartSuccess" = "Xray 已成功重新啟動"
"stopSuccess" = "Xray 已成功停止"
"restartError" = "重新啟動Xray時發生錯誤。"
"stopError" = "停止Xray時發生錯誤。"
"basicTemplate" = "基礎配置"
"advancedTemplate" = "高階配置"
"generalConfigs" = "常規配置"
"generalConfigsDesc" = "這些選項將決定常規配置"
"logConfigs" = "日誌"
"logConfigsDesc" = "日誌可能會影響伺服器的效能，建議僅在需要時啟用"
"blockConfigsDesc" = "這些選項將阻止使用者連線到特定協議和網站"
"basicRouting" = "基本路由"
"blockConnectionsConfigsDesc" = "這些選項將根據特定的請求國家阻止流量。"
"directConnectionsConfigsDesc" = "直接連線確保特定的流量不會通過其他伺服器路由。"
"blockips" = "阻止IP"
"blockdomains" = "阻止域名"
"directips" = "直接IP"
"directdomains" = "直接域名"
"ipv4Routing" = "IPv4 路由"
"ipv4RoutingDesc" = "此選項將僅通過 IPv4 路由到目標域"
"warpRouting" = "WARP 路由"
"warpRoutingDesc" = "注意：在使用這些選項之前，請按照面板 GitHub 上的步驟在你的伺服器上以 socks5 代理模式安裝 WARP。WARP 將通過 Cloudflare 伺服器將流量路由到網站。"
"Template" = "高階 Xray 配置模板"
"TemplateDesc" = "最終的 Xray 配置檔案將基於此模板生成"
"FreedomStrategy" = "Freedom 協議策略"
"FreedomStrategyDesc" = "設定 Freedom 協議中網路的輸出策略"
"RoutingStrategy" = "配置路由域策略"
"RoutingStrategyDesc" = "設定 DNS 解析的整體路由策略"
"Torrent" = "遮蔽 BitTorrent 協議"
"Inbounds" = "入站規則"
"InboundsDesc" = "接受來自特定客戶端的流量"
"Outbounds" = "出站規則"
"Balancers" = "負載均衡"
"OutboundsDesc" = "設定出站流量傳出方式"
"Routings" = "路由規則"
"RoutingsDesc" = "每條規則的優先順序都很重要"
"completeTemplate" = "全部"
"logLevel" = "日誌級別"
"logLevelDesc" = "錯誤日誌的日誌級別，用於指示需要記錄的資訊"
"accessLog" = "訪問日誌"
"accessLogDesc" = "訪問日誌的檔案路徑。特殊值 'none' 禁用訪問日誌"
"errorLog" = "錯誤日誌"
"errorLogDesc" = "錯誤日誌的檔案路徑。特殊值 'none' 禁用錯誤日誌"
"dnsLog" = "DNS 日誌"
"dnsLogDesc" = "是否啟用 DNS 查詢日誌"
"maskAddress" = "隱藏地址"
"maskAddressDesc" = "IP 地址掩碼，啟用時會自動替換日誌中出現的 IP 地址。"
"statistics" = "統計"
"statsInboundUplink" = "入站上傳統計"
"statsInboundUplinkDesc" = "啟用所有入站代理的上行流量統計收集。"
"statsInboundDownlink" = "入站下載統計"
"statsInboundDownlinkDesc" = "啟用所有入站代理的下行流量統計收集。"
"statsOutboundUplink" = "出站上傳統計"
"statsOutboundUplinkDesc" = "啟用所有出站代理的上行流量統計收集。"
"statsOutboundDownlink" = "出站下載統計"
"statsOutboundDownlinkDesc" = "啟用所有出站代理的下行流量統計收集。"

[pages.xray.rules]
"first" = "置頂"
"last" = "置底"
"up" = "向上"
"down" = "向下"
"source" = "來源"
"dest" = "目的地址"
"inbound" = "入站"
"outbound" = "出站"
"balancer" = "負載均衡"
"info" = "資訊"
"add" = "新增規則"
"edit" = "編輯規則"
"useComma" = "逗號分隔的項目"

[pages.xray.outbound]
"addOutbound" = "新增出站"
"addReverse" = "新增反向"
"editOutbound" = "編輯出站"
"editReverse" = "編輯反向"
"tag" = "標籤"
"tagDesc" = "唯一標籤"
"address" = "地址"
"reverse" = "反向"
"domain" = "域名"
"type" = "類型"
"bridge" = "Bridge"
"portal" = "Portal"
"link" = "連結"
"intercon" = "互連"
"settings" = "設定"
"accountInfo" = "帳戶資訊"
"outboundStatus" = "出站狀態"
"sendThrough" = "傳送通過"

[pages.xray.balancer]
"addBalancer" = "新增負載均衡"
"editBalancer" = "編輯負載均衡"
"balancerStrategy" = "策略"
"balancerSelectors" = "選擇器"
"tag" = "標籤"
"tagDesc" = "唯一標籤"
"balancerDesc" = "無法同時使用 balancerTag 和 outboundTag。如果同時使用，則只有 outboundTag 會生效。"

[pages.xray.wireguard]
"secretKey" = "金鑰"
"publicKey" = "公鑰"
"allowedIPs" = "允許的 IP"
"endpoint" = "端點"
"psk" = "共享金鑰"
"domainStrategy" = "域策略"

[pages.xray.dns]
"enable" = "啟用 DNS"
"enableDesc" = "啟用內建 DNS 伺服器"
"tag" = "DNS 入站標籤"
"tagDesc" = "此標籤將在路由規則中可用作入站標籤"
"clientIp" = "客戶端IP"
"clientIpDesc" = "用於在DNS查詢期間通知伺服器指定的IP位置"
"disableCache" = "禁用快取"
"disableCacheDesc" = "禁用DNS快取"
"disableFallback" = "禁用回退"
"disableFallbackDesc" = "禁用回退DNS查詢"
"disableFallbackIfMatch" = "匹配時禁用回退"
"disableFallbackIfMatchDesc" = "當DNS伺服器的匹配域名列表命中時，禁用回退DNS查詢"
"strategy" = "查詢策略"
"strategyDesc" = "解析域名的總體策略"
"add" = "新增伺服器"
"edit" = "編輯伺服器"
"domains" = "域"
"expectIPs" = "預期 IP"
"unexpectIPs" = "意外IP"
"useSystemHosts" = "使用系統Hosts"
"useSystemHostsDesc" = "使用已安裝系統的hosts檔案"
"usePreset" = "使用範本"
"dnsPresetTitle" = "DNS範本"
"dnsPresetFamily" = "家庭"

[pages.xray.fakedns]
"add" = "新增假 DNS"
"edit" = "編輯假 DNS"
"ipPool" = "IP 池子網"
"poolSize" = "池大小"

[pages.settings.security]
"admin" = "管理員憑證"
"twoFactor" = "雙重驗證"  
"twoFactorEnable" = "啟用2FA"  
"twoFactorEnableDesc" = "增加額外的驗證層以提高安全性。"  
"twoFactorModalSetTitle" = "啟用雙重認證"
"twoFactorModalDeleteTitle" = "停用雙重認證"
"twoFactorModalSteps" = "要設定雙重認證，請執行以下步驟："
"twoFactorModalFirstStep" = "1. 在認證應用程式中掃描此QR碼，或複製QR碼附近的令牌並貼到應用程式中"
"twoFactorModalSecondStep" = "2. 輸入應用程式中的驗證碼"
"twoFactorModalRemoveStep" = "輸入應用程式中的驗證碼以移除雙重認證。"
"twoFactorModalChangeCredentialsTitle" = "更改憑證"
"twoFactorModalChangeCredentialsStep" = "輸入應用程式中的代碼以更改管理員憑證。"
"twoFactorModalSetSuccess" = "雙重身份驗證已成功建立"
"twoFactorModalDeleteSuccess" = "雙重身份驗證已成功刪除"
"twoFactorModalError" = "驗證碼錯誤"

[pages.settings.toasts]
"modifySettings" = "參數已更改。"
"getSettings" = "取得參數時發生錯誤"
"modifyUserError" = "變更管理員憑證時發生錯誤。"
"modifyUser" = "您已成功變更管理員憑證。"
"originalUserPassIncorrect" = "原使用者名稱或原密碼錯誤"
"userPassMustBeNotEmpty" = "新使用者名稱和新密碼不能為空"
"getOutboundTrafficError" = "取得出站流量錯誤"
"resetOutboundTrafficError" = "重設出站流量錯誤"

[tgbot]
"keyboardClosed" = "❌ 自定義鍵盤已關閉！"
"noResult" = "❗ 沒有結果！"
"noQuery" = "❌ 未找到查詢！請重新使用命令！"
"wentWrong" = "❌ 出了點問題！"
"noIpRecord" = "❗ 沒有 IP 記錄！"
"noInbounds" = "❗ 沒有找到入站連線！"
"unlimited" = "♾ 無限制"
"add" = "新增"
"month" = "月"
"months" = "月"
"day" = "天"
"days" = "天"
"hours" = "小時"
"unknown" = "未知"
"inbounds" = "入站連線"
"clients" = "客戶端"
"offline" = "🔴 離線"
"online" = "🟢 線上"

[tgbot.commands]
"unknown" = "❗ 未知命令"
"pleaseChoose" = "👇 請選擇：\r\n"
"help" = "🤖 歡迎使用本機器人！它旨在為您提供來自伺服器的特定資料，並允許您進行必要的修改。\r\n\r\n"
"start" = "👋 你好，<i>{{ .Firstname }}</i>。\r\n"
"welcome" = "🤖 歡迎來到 <b>{{ .Hostname }}</b> 管理機器人。\r\n"
"status" = "✅ 機器人正常執行！"
"usage" = "❗ 請輸入要搜尋的文字！"
"getID" = "🆔 您的 ID 為：<code>{{ .ID }}</code>"
"helpAdminCommands" = "要重新啟動 Xray Core：\r\n<code>/restart</code>\r\n\r\n要搜尋客戶電子郵件：\r\n<code>/usage [電子郵件]</code>\r\n\r\n要搜尋入站（帶有客戶統計資料）：\r\n<code>/inbound [備註]</code>\r\n\r\nTelegram聊天ID：\r\n<code>/id</code>"
"helpClientCommands" = "要搜尋統計資料，請使用以下命令：\r\n<code>/usage [電子郵件]</code>\r\n\r\nTelegram聊天ID：\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ 操作成功!"
"restartFailed" = "❗ 操作錯誤。\r\n\r\n<code>錯誤: {{ .Error }}</code>."
"xrayNotRunning" = "❗ Xray Core 未運行。"
"startDesc" = "顯示主選單"
"helpDesc" = "機器人幫助"
"statusDesc" = "檢查機器人狀態"
"idDesc" = "顯示您的 Telegram ID"

[tgbot.messages]
"cpuThreshold" = "🔴 CPU 使用率為 {{ .Percent }}%，超過閾值 {{ .Threshold }}%"
"selectUserFailed" = "❌ 使用者選擇錯誤！"
"userSaved" = "✅ 電報使用者已儲存。"
"loginSuccess" = "✅ 成功登入到面板。\r\n"
"loginFailed" = "❗️ 面板登入失敗。\r\n"
"report" = "🕰 定時報告：{{ .RunTime }}\r\n"
"datetime" = "⏰ 日期時間：{{ .DateTime }}\r\n"
"hostname" = "💻 主機名：{{ .Hostname }}\r\n"
"version" = "🚀 X-UI 版本：{{ .Version }}\r\n"
"xrayVersion" = "📡 Xray 版本: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6：{{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4：{{ .IPv4 }}\r\n"
"ip" = "🌐 IP：{{ .IP }}\r\n"
"ips" = "🔢 IP 地址：\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ 伺服器執行時間：{{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 伺服器負載：{{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 伺服器記憶體：{{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 TCP 連線數：{{ .Count }}\r\n"
"udpCount" = "🔸 UDP 連線數：{{ .Count }}\r\n"
"traffic" = "🚦 流量：{{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ Xray 狀態：{{ .State }}\r\n"
"username" = "👤 使用者名稱：{{ .Username }}\r\n"
"password" = "👤 密碼: {{ .Password }}\r\n"
"time" = "⏰ 時間：{{ .Time }}\r\n"
"inbound" = "📍 入站：{{ .Remark }}\r\n"
"port" = "🔌 埠：{{ .Port }}\r\n"
"expire" = "📅 過期日期：{{ .Time }}\r\n"
"expireIn" = "📅 剩餘時間：{{ .Time }}\r\n"
"active" = "💡 啟用：{{ .Enable }}\r\n"
"enabled" = "🚨 已啟用：{{ .Enable }}\r\n"
"online" = "🌐 連線狀態：{{ .Status }}\r\n"
"email" = "📧 郵箱：{{ .Email }}\r\n"
"upload" = "🔼 上傳↑：{{ .Upload }}\r\n"
"download" = "🔽 下載↓：{{ .Download }}\r\n"
"total" = "📊 總計：{{ .UpDown }} / {{ .Total }}\r\n"
"TGUser" = "👤 電報使用者：{{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 耗盡的 {{ .Type }}：\r\n"
"exhaustedCount" = "🚨 耗盡的 {{ .Type }} 數量：\r\n"
"onlinesCount" = "🌐 線上客戶：{{ .Count }}\r\n"
"disabled" = "🛑 禁用：{{ .Disabled }}\r\n"
"depleteSoon" = "🔜 即將耗盡：{{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 備份時間：{{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 重新整理時間：{{ .Time }}\r\n\r\n"
"yes" = "✅ 是的"
"no" = "❌ 沒有"

"received_id" = "🔑📥 ID 已更新。"
"received_password" = "🔑📥 密碼已更新。"
"received_email" = "📧📥 電子郵件已更新。"
"received_comment" = "💬📥 評論已更新。"
"id_prompt" = "🔑 預設 ID: {{ .ClientId }}\n\n請輸入您的 ID。"
"pass_prompt" = "🔑 預設密碼: {{ .ClientPassword }}\n\n請輸入您的密碼。"
"email_prompt" = "📧 預設電子郵件: {{ .ClientEmail }}\n\n請輸入您的電子郵件。"
"comment_prompt" = "💬 預設評論: {{ .ClientComment }}\n\n請輸入您的評論。"
"inbound_client_data_id" = "🔄 入站: {{ .InboundRemark }}\n\n🔑 ID: {{ .ClientId }}\n📧 電子郵件: {{ .ClientEmail }}\n📊 流量: {{ .ClientTraffic }}\n📅 到期日: {{ .ClientExp }}\n🌐 IP 限制: {{ .IpLimit }}\n💬 備註: {{ .ClientComment }}\n\n你現在可以將客戶加入入站了！"
"inbound_client_data_pass" = "🔄 入站: {{ .InboundRemark }}\n\n🔑 密碼: {{ .ClientPass }}\n📧 電子郵件: {{ .ClientEmail }}\n📊 流量: {{ .ClientTraffic }}\n📅 到期日: {{ .ClientExp }}\n🌐 IP 限制: {{ .IpLimit }}\n💬 備註: {{ .ClientComment }}\n\n你現在可以將客戶加入入站了！"
"cancel" = "❌ 程序已取消！\n\n您可以隨時使用 /start 重新開始。 🔄"
"error_add_client"  = "⚠️ 錯誤:\n\n {{ .error }}"
"using_default_value"  = "好的，我會使用預設值。 😊"
"incorrect_input" ="您的輸入無效。\n短語應連續輸入，不能有空格。\n正確示例: aaaaaa\n錯誤示例: aaa aaa 🚫"
"AreYouSure" = "你確定嗎？🤔"
"SuccessResetTraffic" = "📧 電子郵件: {{ .ClientEmail }}\n🏁 結果: ✅ 成功"
"FailedResetTraffic" = "📧 電子郵件: {{ .ClientEmail }}\n🏁 結果: ❌ 失敗 \n\n🛠️ 錯誤: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 所有客戶的流量重置已完成。"


[tgbot.buttons]
"closeKeyboard" = "❌ 關閉鍵盤"
"cancel" = "❌ 取消"
"cancelReset" = "❌ 取消重置"
"cancelIpLimit" = "❌ 取消 IP 限制"
"confirmResetTraffic" = "✅ 確認重置流量？"
"confirmClearIps" = "✅ 確認清除 IP？"
"confirmRemoveTGUser" = "✅ 確認移除 Telegram 使用者？"
"confirmToggle" = "✅ 確認啟用/禁用使用者？"
"dbBackup" = "獲取資料庫備份"
"serverUsage" = "伺服器使用情況"
"getInbounds" = "獲取入站資訊"
"depleteSoon" = "即將耗盡"
"clientUsage" = "獲取使用情況"
"onlines" = "線上客戶端"
"commands" = "命令"
"refresh" = "🔄 重新整理"
"clearIPs" = "❌ 清除 IP"
"removeTGUser" = "❌ 移除 Telegram 使用者"
"selectTGUser" = "👤 選擇 Telegram 使用者"
"selectOneTGUser" = "👤 選擇一個 Telegram 使用者："
"resetTraffic" = "📈 重置流量"
"resetExpire" = "📅 更改到期日期"
"ipLog" = "🔢 IP 日誌"
"ipLimit" = "🔢 IP 限制"
"setTGUser" = "👤 設定 Telegram 使用者"
"toggle" = "🔘 啟用/禁用"
"custom" = "🔢 風俗"
"confirmNumber" = "✅ 確認: {{ .Num }}"
"confirmNumberAdd" = "✅ 確認新增：{{ .Num }}"
"limitTraffic" = "🚧 流量限制"
"getBanLogs" = "禁止日誌"
"allClients" = "所有客戶"

"addClient" = "新增客戶"
"submitDisable" = "以停用方式送出 ☑️"
"submitEnable" = "以啟用方式送出 ✅"
"use_default" = "🏷️ 使用預設值"
"change_id" = "⚙️🔑 ID"
"change_password" = "⚙️🔑 密碼"
"change_email" = "⚙️📧 電子郵件"
"change_comment" = "⚙️💬 評論"
"ResetAllTraffics" = "重設所有流量"
"SortedTrafficUsageReport" = "排序過的流量使用報告"


[tgbot.answers]
"successfulOperation" = "✅ 成功！"
"errorOperation" = "❗ 操作錯誤。"
"getInboundsFailed" = "❌ 獲取入站資訊失敗。"
"getClientsFailed" = "❌ 獲取客戶失敗。"
"canceled" = "❌ {{ .Email }}：操作已取消。"
"clientRefreshSuccess" = "✅ {{ .Email }}：客戶端重新整理成功。"
"IpRefreshSuccess" = "✅ {{ .Email }}：IP 重新整理成功。"
"TGIdRefreshSuccess" = "✅ {{ .Email }}：客戶端的 Telegram 使用者重新整理成功。"
"resetTrafficSuccess" = "✅ {{ .Email }}：流量已重置成功。"
"setTrafficLimitSuccess" = "✅ {{ .Email }}: 流量限制儲存成功。"
"expireResetSuccess" = "✅ {{ .Email }}：過期天數已重置成功。"
"resetIpSuccess" = "✅ {{ .Email }}：成功儲存 IP 限制數量為 {{ .Count }}。"
"clearIpSuccess" = "✅ {{ .Email }}：IP 已成功清除。"
"getIpLog" = "✅ {{ .Email }}：獲取 IP 日誌。"
"getUserInfo" = "✅ {{ .Email }}：獲取 Telegram 使用者資訊。"
"removedTGUserSuccess" = "✅ {{ .Email }}：Telegram 使用者已成功移除。"
"enableSuccess" = "✅ {{ .Email }}：已成功啟用。"
"disableSuccess" = "✅ {{ .Email }}：已成功禁用。"
"askToAddUserId" = "未找到您的配置！\r\n請向管理員詢問，在您的配置中使用您的 Telegram 使用者 ChatID。\r\n\r\n您的使用者 ChatID：<code>{{ .TgUserID }}</code>"
"chooseClient" = "為入站 {{ .Inbound }} 選擇一個客戶"
"chooseInbound" = "選擇一個入站"
