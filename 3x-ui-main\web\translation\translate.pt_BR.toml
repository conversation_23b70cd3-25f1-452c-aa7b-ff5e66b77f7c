"username" = "Nome de Usuário"
"password" = "Senha"
"login" = "Entrar"
"confirm" = "Confirmar"
"cancel" = "Cancelar"
"close" = "Fechar"
"create" = "Criar"
"update" = "Atualizar"
"copy" = "Copiar"
"copied" = "Copiado"
"download" = "Baixar"
"remark" = "Observação"
"enable" = "Ativado"
"protocol" = "Protocolo"
"search" = "Pesquisar"
"filter" = "Filtrar"
"loading" = "Carregando..."
"second" = "Segundo"
"minute" = "Minuto"
"hour" = "Hora"
"day" = "Dia"
"check" = "Verificar"
"indefinite" = "Indeterminado"
"unlimited" = "Ilimitado"
"none" = "Nada"
"qrCode" = "Código QR"
"info" = "Mais Informações"
"edit" = "Editar"
"delete" = "Excluir"
"reset" = "Redefinir"
"noData" = "Sem dados."
"copySuccess" = "Copiado com Sucesso"
"sure" = "Certo"
"encryption" = "Criptografia"
"useIPv4ForHost" = "Usar IPv4 para o host"
"transmission" = "Transmissão"
"host" = "Servidor"
"path" = "Caminho"
"camouflage" = "Ofuscação"
"status" = "Status"
"enabled" = "Ativado"
"disabled" = "Desativado"
"depleted" = "Encerrado"
"depletingSoon" = "Esgotando"
"offline" = "Offline"
"online" = "Online"
"domainName" = "Nome de Domínio"
"monitor" = "IP de Escuta"
"certificate" = "Certificado Digital"
"fail" = "Falhou"
"comment" = "Comentário"
"success" = "Com Sucesso"
"getVersion" = "Obter Versão"
"install" = "Instalar"
"clients" = "Clientes"
"usage" = "Uso"
"twoFactorCode" = "Código"
"remained" = "Restante"
"security" = "Segurança"
"secAlertTitle" = "Alerta de Segurança"
"secAlertSsl" = "Esta conexão não é segura. Evite inserir informações confidenciais até que o TLS seja ativado para proteção de dados."
"secAlertConf" = "Algumas configurações estão vulneráveis a ataques. Recomenda-se reforçar os protocolos de segurança para evitar possíveis violações."
"secAlertSSL" = "O painel não possui uma conexão segura. Instale o certificado TLS para proteção de dados."
"secAlertPanelPort" = "A porta padrão do painel é vulnerável. Configure uma porta aleatória ou específica."
"secAlertPanelURI" = "O caminho URI padrão do painel não é seguro. Configure um caminho URI complexo."
"secAlertSubURI" = "O caminho URI padrão de inscrição não é seguro. Configure um caminho URI complexo."
"secAlertSubJsonURI" = "O caminho URI JSON de inscrição padrão não é seguro. Configure um caminho URI complexo."
"emptyDnsDesc" = "Nenhum servidor DNS adicionado."
"emptyFakeDnsDesc" = "Nenhum servidor Fake DNS adicionado."
"emptyBalancersDesc" = "Nenhum balanceador adicionado."
"emptyReverseDesc" = "Nenhum proxy reverso adicionado."
"somethingWentWrong" = "Algo deu errado"

[menu]
"theme" = "Tema"
"dark" = "Escuro"
"ultraDark" = "Ultra Escuro"
"dashboard" = "Visão Geral"
"inbounds" = "Inbounds"
"settings" = "Panel Settings"
"xray" = "Xray Configs"
"logout" = "Sair"
"link" = "Gerenciar"

[pages.login]
"hello" = "Olá"
"title" = "Bem-vindo"
"loginAgain" = "Sua sessão expirou, faça login novamente"

[pages.login.toasts]
"invalidFormData" = "O formato dos dados de entrada é inválido."
"emptyUsername" = "Nome de usuário é obrigatório"
"emptyPassword" = "Senha é obrigatória"
"wrongUsernameOrPassword" = "Nome de usuário, senha ou código de dois fatores inválido."  
"successLogin" = "Você entrou na sua conta com sucesso."

[pages.index]
"title" = "Visão Geral"
"cpu" = "CPU" 
"logicalProcessors" = "Processadores lógicos"
"frequency" = "Frequência"
"swap" = "Swap"
"storage" = "Armazenamento"
"memory" = "RAM"
"threads" = "Threads"
"xrayStatus" = "Xray"
"stopXray" = "Parar"
"restartXray" = "Reiniciar"
"xraySwitch" = "Versão"
"xraySwitchClick" = "Escolha a versão para a qual deseja alternar."
"xraySwitchClickDesk" = "Escolha com cuidado, pois versões mais antigas podem não ser compatíveis com as configurações atuais."
"xrayStatusUnknown" = "Desconhecido"
"xrayStatusRunning" = "Em execução"
"xrayStatusStop" = "Parado"
"xrayStatusError" = "Erro"
"xrayErrorPopoverTitle" = "Ocorreu um erro ao executar o Xray"
"operationHours" = "Tempo de Atividade"
"systemLoad" = "Carga do Sistema"
"systemLoadDesc" = "Média de carga do sistema nos últimos 1, 5 e 15 minutos"
"connectionTcpCountDesc" = "Total de conexões TCP no sistema"
"connectionUdpCountDesc" = "Total de conexões UDP no sistema"
"connectionCount" = "Estatísticas de Conexão"
"ipAddresses" = "Endereços IP"
"toggleIpVisibility" = "Alternar visibilidade do IP"
"overallSpeed" = "Velocidade geral"
"upload" = "Upload"
"download" = "Download"
"totalData" = "Dados totais"
"sent" = "Enviado"
"received" = "Recebido"
"documentation" = "Documentação"
"xraySwitchVersionDialog" = "Você realmente deseja alterar a versão do Xray?"
"xraySwitchVersionDialogDesc" = "Isso mudará a versão do Xray para #version#."
"xraySwitchVersionPopover" = "Xray atualizado com sucesso"
"geofileUpdateDialog" = "Você realmente deseja atualizar o geofile?"
"geofileUpdateDialogDesc" = "Isso atualizará o arquivo #filename#."
"geofileUpdatePopover" = "Geofile atualizado com sucesso"
"dontRefresh" = "Instalação em andamento, por favor não atualize a página"
"logs" = "Logs"
"config" = "Configuração"
"backup" = "Backup"
"backupTitle" = "Backup e Restauração do Banco de Dados"
"exportDatabase" = "Backup"
"exportDatabaseDesc" = "Clique para baixar um arquivo .db contendo um backup do seu banco de dados atual para o seu dispositivo."
"importDatabase" = "Restaurar"
"importDatabaseDesc" = "Clique para selecionar e enviar um arquivo .db do seu dispositivo para restaurar seu banco de dados a partir de um backup."
"importDatabaseSuccess" = "O banco de dados foi importado com sucesso"
"importDatabaseError" = "Ocorreu um erro ao importar o banco de dados"
"readDatabaseError" = "Ocorreu um erro ao ler o banco de dados"
"getDatabaseError" = "Ocorreu um erro ao recuperar o banco de dados"
"getConfigError" = "Ocorreu um erro ao recuperar o arquivo de configuração"

[pages.inbounds]
"title" = "Inbounds"
"totalDownUp" = "Total Enviado/Recebido"
"totalUsage" = "Uso Total"
"inboundCount" = "Total de Inbounds"
"operate" = "Menu"
"enable" = "Ativado"
"remark" = "Observação"
"protocol" = "Protocolo"
"port" = "Porta"
"traffic" = "Tráfego"
"details" = "Detalhes"
"transportConfig" = "Transporte"
"expireDate" = "Duração"
"resetTraffic" = "Redefinir Tráfego"
"addInbound" = "Adicionar Inbound"
"generalActions" = "Ações Gerais"
"autoRefresh" = "Atualização automática"
"autoRefreshInterval" = "Intervalo"
"modifyInbound" = "Modificar Inbound"
"deleteInbound" = "Excluir Inbound"
"deleteInboundContent" = "Tem certeza de que deseja excluir o inbound?"
"deleteClient" = "Excluir Cliente"
"deleteClientContent" = "Tem certeza de que deseja excluir o cliente?"
"resetTrafficContent" = "Tem certeza de que deseja redefinir o tráfego?"
"inboundUpdateSuccess" = "A entrada foi atualizada com sucesso."
"inboundCreateSuccess" = "A entrada foi criada com sucesso."
"copyLink" = "Copiar URL"
"address" = "Endereço"
"network" = "Rede"
"destinationPort" = "Porta de Destino"
"targetAddress" = "Endereço de Destino"
"monitorDesc" = "Deixe em branco para ouvir todos os IPs"
"meansNoLimit" = "= Ilimitado. (unidade: GB)"
"totalFlow" = "Fluxo Total"
"leaveBlankToNeverExpire" = "Deixe em branco para nunca expirar"
"noRecommendKeepDefault" = "Recomenda-se manter o padrão"
"certificatePath" = "Caminho"
"certificateContent" = "Conteúdo"
"publicKey" = "Chave Pública"
"privatekey" = "Chave Privada"
"clickOnQRcode" = "Clique no Código QR para Copiar"
"client" = "Cliente"
"export" = "Exportar Todos os URLs"
"clone" = "Clonar"
"cloneInbound" = "Clonar"
"cloneInboundContent" = "Todas as configurações deste inbound, exceto Porta, IP de Escuta e Clientes, serão aplicadas ao clone."
"cloneInboundOk" = "Clonar"
"resetAllTraffic" = "Redefinir Tráfego de Todos os Inbounds"
"resetAllTrafficTitle" = "Redefinir Tráfego de Todos os Inbounds"
"resetAllTrafficContent" = "Tem certeza de que deseja redefinir o tráfego de todos os inbounds?"
"resetInboundClientTraffics" = "Redefinir Tráfego dos Clientes"
"resetInboundClientTrafficTitle" = "Redefinir Tráfego dos Clientes"
"resetInboundClientTrafficContent" = "Tem certeza de que deseja redefinir o tráfego dos clientes deste inbound?"
"resetAllClientTraffics" = "Redefinir Tráfego de Todos os Clientes"
"resetAllClientTrafficTitle" = "Redefinir Tráfego de Todos os Clientes"
"resetAllClientTrafficContent" = "Tem certeza de que deseja redefinir o tráfego de todos os clientes?"
"delDepletedClients" = "Excluir Clientes Esgotados"
"delDepletedClientsTitle" = "Excluir Clientes Esgotados"
"delDepletedClientsContent" = "Tem certeza de que deseja excluir todos os clientes esgotados?"
"email" = "Email"
"emailDesc" = "Por favor, forneça um endereço de e-mail único."
"IPLimit" = "Limite de IP"
"IPLimitDesc" = "Desativa o inbound se o número ultrapassar o valor definido. (0 = desativar)"
"IPLimitlog" = "Log de IP"
"IPLimitlogDesc" = "O histórico de IPs. (para ativar o inbound após a desativação, limpe o log)"
"IPLimitlogclear" = "Limpar o Log"
"setDefaultCert" = "Definir Certificado pelo Painel"
"telegramDesc" = "Por favor, forneça o ID do Chat do Telegram. (use o comando '/id' no bot) ou (@userinfobot)"
"subscriptionDesc" = "Para encontrar seu URL de assinatura, navegue até 'Detalhes'. Além disso, você pode usar o mesmo nome para vários clientes."
"info" = "Informações"
"same" = "Igual"
"inboundData" = "Dados do Inbound"
"exportInbound" = "Exportar Inbound"
"import" = "Importar"
"importInbound" = "Importar um Inbound"

[pages.client]
"add" = "Adicionar Cliente"
"edit" = "Editar Cliente"
"submitAdd" = "Adicionar Cliente"
"submitEdit" = "Salvar Alterações"
"clientCount" = "Número de Clientes"
"bulk" = "Adicionar Vários"
"method" = "Método"
"first" = "Primeiro"
"last" = "Último"
"prefix" = "Prefixo"
"postfix" = "Sufixo"
"delayedStart" = "Iniciar Após Primeiro Uso"
"expireDays" = "Duração"
"days" = "Dia(s)"
"renew" = "Renovação Automática"
"renewDesc" = "Renovação automática após expiração. (0 = desativado)(unidade: dia)"

[pages.inbounds.toasts]
"obtain" = "Obter"
"updateSuccess" = "A atualização foi bem-sucedida"
"logCleanSuccess" = "O log foi limpo"
"inboundsUpdateSuccess" = "Entradas atualizadas com sucesso"
"inboundUpdateSuccess" = "Entrada atualizada com sucesso"
"inboundCreateSuccess" = "Entrada criada com sucesso"
"inboundDeleteSuccess" = "Entrada excluída com sucesso"
"inboundClientAddSuccess" = "Cliente(s) de entrada adicionado(s)"
"inboundClientDeleteSuccess" = "Cliente de entrada excluído"
"inboundClientUpdateSuccess" = "Cliente de entrada atualizado"
"delDepletedClientsSuccess" = "Todos os clientes esgotados foram excluídos"
"resetAllClientTrafficSuccess" = "Todo o tráfego do cliente foi reiniciado"
"resetAllTrafficSuccess" = "Todo o tráfego foi reiniciado"
"resetInboundClientTrafficSuccess" = "O tráfego foi reiniciado"
"trafficGetError" = "Erro ao obter tráfegos"
"getNewX25519CertError" = "Erro ao obter o certificado X25519."

[pages.inbounds.stream.general]
"request" = "Requisição"
"response" = "Resposta"
"name" = "Nome"
"value" = "Valor"

[pages.inbounds.stream.tcp]
"version" = "Versão"
"method" = "Método"
"path" = "Caminho"
"status" = "Status"
"statusDescription" = "Descrição do Status"
"requestHeader" = "Cabeçalho da Requisição"
"responseHeader" = "Cabeçalho da Resposta"

[pages.settings]
"title" = "Configurações do Painel"
"save" = "Salvar"
"infoDesc" = "Toda alteração feita aqui precisa ser salva. Reinicie o painel para aplicar as alterações."
"restartPanel" = "Reiniciar Painel"
"restartPanelDesc" = "Tem certeza de que deseja reiniciar o painel? Se não conseguir acessar o painel após reiniciar, consulte os logs do painel no servidor."
"restartPanelSuccess" = "O painel foi reiniciado com sucesso"
"actions" = "Ações"
"resetDefaultConfig" = "Redefinir para Padrão"
"panelSettings" = "Geral"
"securitySettings" = "Autenticação"
"TGBotSettings" = "Bot do Telegram"
"panelListeningIP" = "IP de Escuta"
"panelListeningIPDesc" = "O endereço IP para o painel web. (deixe em branco para escutar em todos os IPs)"
"panelListeningDomain" = "Domínio de Escuta"
"panelListeningDomainDesc" = "O nome de domínio para o painel web. (deixe em branco para escutar em todos os domínios e IPs)"
"panelPort" = "Porta de Escuta"
"panelPortDesc" = "O número da porta para o painel web. (deve ser uma porta não usada)"
"publicKeyPath" = "Caminho da Chave Pública"
"publicKeyPathDesc" = "O caminho do arquivo de chave pública para o painel web. (começa com ‘/‘)"
"privateKeyPath" = "Caminho da Chave Privada"
"privateKeyPathDesc" = "O caminho do arquivo de chave privada para o painel web. (começa com ‘/‘)"
"panelUrlPath" = "Caminho URI"
"panelUrlPathDesc" = "O caminho URI para o painel web. (começa com ‘/‘ e termina com ‘/‘)"
"pageSize" = "Tamanho da Paginação"
"pageSizeDesc" = "Definir o tamanho da página para a tabela de entradas. (0 = desativado)"
"remarkModel" = "Modelo de Observação & Caractere de Separação"
"datepicker" = "Tipo de Calendário"
"datepickerPlaceholder" = "Selecionar data"
"datepickerDescription" = "Tarefas agendadas serão executadas com base neste calendário."
"sampleRemark" = "Exemplo de Observação"
"oldUsername" = "Nome de Usuário Atual"
"currentPassword" = "Senha Atual"
"newUsername" = "Novo Nome de Usuário"
"newPassword" = "Nova Senha"
"telegramBotEnable" = "Ativar Bot do Telegram"
"telegramBotEnableDesc" = "Ativa o bot do Telegram."
"telegramToken" = "Token do Telegram"
"telegramTokenDesc" = "O token do bot do Telegram obtido de '@BotFather'."
"telegramProxy" = "Proxy SOCKS"
"telegramProxyDesc" = "Ativa o proxy SOCKS5 para conectar ao Telegram. (ajuste as configurações conforme o guia)"
"telegramAPIServer" = "API Server do Telegram"
"telegramAPIServerDesc" = "O servidor API do Telegram a ser usado. Deixe em branco para usar o servidor padrão."
"telegramChatId" = "ID de Chat do Administrador"
"telegramChatIdDesc" = "O(s) ID(s) de Chat do Administrador no Telegram. (separado por vírgulas)(obtenha aqui @userinfobot) ou (use o comando '/id' no bot)"
"telegramNotifyTime" = "Hora da Notificação"
"telegramNotifyTimeDesc" = "O horário de notificação do bot do Telegram configurado para relatórios periódicos. (use o formato de tempo do crontab)"
"tgNotifyBackup" = "Backup do Banco de Dados"
"tgNotifyBackupDesc" = "Enviar arquivo de backup do banco de dados junto com o relatório."
"tgNotifyLogin" = "Notificação de Login"
"tgNotifyLoginDesc" = "Receba notificações sobre o nome de usuário, endereço IP e horário sempre que alguém tentar fazer login no seu painel web."
"sessionMaxAge" = "Duração da Sessão"
"sessionMaxAgeDesc" = "A duração pela qual você pode permanecer logado. (unidade: minuto)"
"expireTimeDiff" = "Notificação de Expiração"
"expireTimeDiffDesc" = "Receba notificações sobre a data de expiração ao atingir esse limite. (unidade: dia)"
"trafficDiff" = "Notificação de Limite de Tráfego"
"trafficDiffDesc" = "Receba notificações sobre o limite de tráfego ao atingir esse limite. (unidade: GB)"
"tgNotifyCpu" = "Notificação de Carga da CPU"
"tgNotifyCpuDesc" = "Receba notificações se a carga da CPU ultrapassar esse limite. (unidade: %)"
"timeZone" = "Fuso Horário"
"timeZoneDesc" = "As tarefas agendadas serão executadas com base nesse fuso horário."
"subSettings" = "Assinatura"
"subEnable" = "Ativar Serviço de Assinatura"
"subEnableDesc" = "Ativa o serviço de assinatura."
"subTitle" = "Título da Assinatura"
"subTitleDesc" = "Título exibido no cliente VPN"
"subListen" = "IP de Escuta"
"subListenDesc" = "O endereço IP para o serviço de assinatura. (deixe em branco para escutar em todos os IPs)"
"subPort" = "Porta de Escuta"
"subPortDesc" = "O número da porta para o serviço de assinatura. (deve ser uma porta não usada)"
"subCertPath" = "Caminho da Chave Pública"
"subCertPathDesc" = "O caminho do arquivo de chave pública para o serviço de assinatura. (começa com ‘/‘)"
"subKeyPath" = "Caminho da Chave Privada"
"subKeyPathDesc" = "O caminho do arquivo de chave privada para o serviço de assinatura. (começa com ‘/‘)"
"subPath" = "Caminho URI"
"subPathDesc" = "O caminho URI para o serviço de assinatura. (começa com ‘/‘ e termina com ‘/‘)"
"subDomain" = "Domínio de Escuta"
"subDomainDesc" = "O nome de domínio para o serviço de assinatura. (deixe em branco para escutar em todos os domínios e IPs)"
"subUpdates" = "Intervalos de Atualização"
"subUpdatesDesc" = "Os intervalos de atualização da URL de assinatura nos aplicativos de cliente. (unidade: hora)"
"subEncrypt" = "Codificar"
"subEncryptDesc" = "O conteúdo retornado pelo serviço de assinatura será codificado em Base64."
"subShowInfo" = "Mostrar Informações de Uso"
"subShowInfoDesc" = "O tráfego restante e a data serão exibidos nos aplicativos de cliente."
"subURI" = "URI de Proxy Reverso"
"subURIDesc" = "O caminho URI da URL de assinatura para uso por trás de proxies."
"externalTrafficInformEnable" = "Informações de tráfego externo"
"externalTrafficInformEnableDesc" = "Informar a API externa sobre cada atualização de tráfego."
"externalTrafficInformURI" = "URI de informação de tráfego externo"
"externalTrafficInformURIDesc" = "As atualizações de tráfego são enviadas para este URI."
"fragment" = "Fragmentação"
"fragmentDesc" = "Ativa a fragmentação para o pacote TLS hello."
"fragmentSett" = "Configurações de Fragmentação"
"noisesDesc" = "Ativar Noises."
"noisesSett" = "Configurações de Noises"
"mux" = "Mux"
"muxDesc" = "Transmitir múltiplos fluxos de dados independentes dentro de um fluxo de dados estabelecido."
"muxSett" = "Configurações de Mux"
"direct" = "Conexão Direta"
"directDesc" = "Estabelece conexões diretamente com domínios ou intervalos de IP de um país específico."
"notifications" = "Notificações"
"certs" = "Certificados"
"externalTraffic" = "Tráfego Externo"
"dateAndTime" = "Data e Hora"
"proxyAndServer" = "Proxy e Servidor"
"intervals" = "Intervalos"
"information" = "Informação"
"language" = "Idioma"
"telegramBotLanguage" = "Idioma do Bot do Telegram"

[pages.xray]
"title" = "Configurações Xray"
"save" = "Salvar"
"restart" = "Reiniciar Xray"
"restartSuccess" = "Xray foi reiniciado com sucesso"
"stopSuccess" = "Xray foi interrompido com sucesso"
"restartError" = "Ocorreu um erro ao reiniciar o Xray."
"stopError" = "Ocorreu um erro ao parar o Xray."
"basicTemplate" = "Básico"
"advancedTemplate" = "Avançado"
"generalConfigs" = "Geral"
"generalConfigsDesc" = "Essas opções determinam ajustes gerais."
"logConfigs" = "Log"
"logConfigsDesc" = "Os logs podem afetar a eficiência do servidor. É recomendável habilitá-los com sabedoria apenas se necessário."
"blockConfigsDesc" = "Essas opções bloqueiam tráfego com base em protocolos e sites específicos solicitados."
"basicRouting" = "Roteamento Básico"
"blockConnectionsConfigsDesc" = "Essas opções bloquearão o tráfego com base no país solicitado."
"directConnectionsConfigsDesc" = "Uma conexão direta garante que o tráfego específico não seja roteado por outro servidor."
"blockips" = "Bloquear IPs"
"blockdomains" = "Bloquear Domínios"
"directips" = "IPs Diretos"
"directdomains" = "Domínios Diretos"
"ipv4Routing" = "Roteamento IPv4"
"ipv4RoutingDesc" = "Essas opções roteam o tráfego para um destino específico via IPv4."
"warpRouting" = "Roteamento WARP"
"warpRoutingDesc" = "Essas opções roteam o tráfego para um destino específico via WARP."
"Template" = "Modelo de Configuração Avançada do Xray"
"TemplateDesc" = "O arquivo final de configuração do Xray será gerado com base neste modelo."
"FreedomStrategy" = "Estratégia do Protocolo Freedom"
"FreedomStrategyDesc" = "Definir a estratégia de saída para a rede no Protocolo Freedom."
"RoutingStrategy" = "Estratégia Geral de Roteamento"
"RoutingStrategyDesc" = "Definir a estratégia geral de roteamento de tráfego para resolver todas as solicitações."
"Torrent" = "Bloquear Protocolo BitTorrent"
"Inbounds" = "Inbounds"
"InboundsDesc" = "Aceitar clientes específicos."
"Outbounds" = "Outbounds"
"Balancers" = "Balanceadores"
"OutboundsDesc" = "Definir o caminho de saída do tráfego."
"Routings" = "Regras de Roteamento"
"RoutingsDesc" = "A prioridade de cada regra é importante!"
"completeTemplate" = "Todos"
"logLevel" = "Nível de Log"
"logLevelDesc" = "O nível de log para erros, indicando a informação que precisa ser registrada."
"accessLog" = "Log de Acesso"
"accessLogDesc" = "O caminho do arquivo para o log de acesso. O valor especial 'none' desativa os logs de acesso."
"errorLog" = "Log de Erros"
"errorLogDesc" = "O caminho do arquivo para o log de erros. O valor especial 'none' desativa os logs de erro."
"dnsLog" = "Log DNS"
"dnsLogDesc" = "Se ativar logs de consulta DNS"
"maskAddress" = "Mascarar Endereço"
"maskAddressDesc" = "Máscara de endereço IP, quando ativado, substitui automaticamente o endereço IP que aparece no log."
"statistics" = "Estatísticas"
"statsInboundUplink" = "Estatísticas de Upload de Entrada"
"statsInboundUplinkDesc" = "Habilita a coleta de estatísticas para o tráfego de upload de todos os proxies de entrada."
"statsInboundDownlink" = "Estatísticas de Download de Entrada"
"statsInboundDownlinkDesc" = "Habilita a coleta de estatísticas para o tráfego de download de todos os proxies de entrada."
"statsOutboundUplink" = "Estatísticas de Upload de Saída"
"statsOutboundUplinkDesc" = "Habilita a coleta de estatísticas para o tráfego de upload de todos os proxies de saída."
"statsOutboundDownlink" = "Estatísticas de Download de Saída"
"statsOutboundDownlinkDesc" = "Habilita a coleta de estatísticas para o tráfego de download de todos os proxies de saída."

[pages.xray.rules]
"first" = "Primeiro"
"last" = "Último"
"up" = "Cima"
"down" = "Baixo"
"source" = "Fonte"
"dest" = "Destino"
"inbound" = "Entrada"
"outbound" = "Saída"
"balancer" = "Balanceador"
"info" = "Info"
"add" = "Adicionar Regra"
"edit" = "Editar Regra"
"useComma" = "Itens separados por vírgula"

[pages.xray.outbound]
"addOutbound" = "Adicionar Saída"
"addReverse" = "Adicionar Reverso"
"editOutbound" = "Editar Saída"
"editReverse" = "Editar Reverso"
"tag" = "Tag"
"tagDesc" = "Tag Única"
"address" = "Endereço"
"reverse" = "Reverso"
"domain" = "Domínio"
"type" = "Tipo"
"bridge" = "Ponte"
"portal" = "Portal"
"link" = "Link"
"intercon" = "Interconexão"
"settings" = "Configurações"
"accountInfo" = "Informações da Conta"
"outboundStatus" = "Status de Saída"
"sendThrough" = "Enviar Através de"

[pages.xray.balancer]
"addBalancer" = "Adicionar Balanceador"
"editBalancer" = "Editar Balanceador"
"balancerStrategy" = "Estratégia"
"balancerSelectors" = "Seletores"
"tag" = "Tag"
"tagDesc" = "Tag Única"
"balancerDesc" = "Não é possível usar balancerTag e outboundTag ao mesmo tempo. Se usados simultaneamente, apenas outboundTag funcionará."

[pages.xray.wireguard]
"secretKey" = "Chave Secreta"
"publicKey" = "Chave Pública"
"allowedIPs" = "IPs Permitidos"
"endpoint" = "Ponto Final"
"psk" = "Chave Pré-Compartilhada"
"domainStrategy" = "Estratégia de Domínio"

[pages.xray.dns]
"enable" = "Ativar DNS"
"enableDesc" = "Ativar o servidor DNS integrado"
"tag" = "Tag de Entrada DNS"
"tagDesc" = "Esta tag estará disponível como uma tag de Entrada nas regras de roteamento."
"clientIp" = "IP do Cliente"
"clientIpDesc" = "Usado para notificar o servidor sobre a localização IP especificada durante consultas DNS"
"disableCache" = "Desativar cache"
"disableCacheDesc" = "Desativa o cache de DNS"
"disableFallback" = "Desativar Fallback"
"disableFallbackDesc" = "Desativa consultas DNS de fallback"
"disableFallbackIfMatch" = "Desativar Fallback Se Corresponder"
"disableFallbackIfMatchDesc" = "Desativa consultas DNS de fallback quando a lista de domínios correspondentes do servidor DNS é atingida"
"strategy" = "Estratégia de Consulta"
"strategyDesc" = "Estratégia geral para resolver nomes de domínio"
"add" = "Adicionar Servidor"
"edit" = "Editar Servidor"
"domains" = "Domínios"
"expectIPs" = "IPs Esperadas"
"unexpectIPs" = "IPs inesperados"
"useSystemHosts" = "Usar Hosts do sistema"
"useSystemHostsDesc" = "Usar o arquivo hosts de um sistema instalado"
"usePreset" = "Usar modelo"
"dnsPresetTitle" = "Modelos DNS"
"dnsPresetFamily" = "Familiar"

[pages.xray.fakedns]
"add" = "Adicionar Fake DNS"
"edit" = "Editar Fake DNS"
"ipPool" = "Sub-rede do Pool de IP"
"poolSize" = "Tamanho do Pool"

[pages.settings.security]
"admin" = "Credenciais de administrador"
"twoFactor" = "Autenticação de dois fatores"  
"twoFactorEnable" = "Ativar 2FA"  
"twoFactorEnableDesc" = "Adiciona uma camada extra de autenticação para mais segurança."  
"twoFactorModalSetTitle" = "Ativar autenticação de dois fatores"
"twoFactorModalDeleteTitle" = "Desativar autenticação de dois fatores"
"twoFactorModalSteps" = "Para configurar a autenticação de dois fatores, siga alguns passos:"
"twoFactorModalFirstStep" = "1. Escaneie este QR code no aplicativo de autenticação ou copie o token próximo ao QR code e cole no aplicativo"
"twoFactorModalSecondStep" = "2. Digite o código do aplicativo"
"twoFactorModalRemoveStep" = "Digite o código do aplicativo para remover a autenticação de dois fatores."
"twoFactorModalChangeCredentialsTitle" = "Alterar credenciais"
"twoFactorModalChangeCredentialsStep" = "Insira o código do aplicativo para alterar as credenciais do administrador."
"twoFactorModalSetSuccess" = "A autenticação de dois fatores foi estabelecida com sucesso"
"twoFactorModalDeleteSuccess" = "A autenticação de dois fatores foi excluída com sucesso"
"twoFactorModalError" = "Código incorreto"

[pages.settings.toasts]
"modifySettings" = "Os parâmetros foram alterados."
"getSettings" = "Ocorreu um erro ao recuperar os parâmetros."
"modifyUserError" = "Ocorreu um erro ao alterar as credenciais do administrador."
"modifyUser" = "Você alterou com sucesso as credenciais do administrador."
"originalUserPassIncorrect" = "O nome de usuário ou senha atual é inválido"
"userPassMustBeNotEmpty" = "O novo nome de usuário e senha não podem estar vazios"
"getOutboundTrafficError" = "Erro ao obter tráfego de saída"
"resetOutboundTrafficError" = "Erro ao redefinir tráfego de saída"

[tgbot]
"keyboardClosed" = "❌ Teclado personalizado fechado!"
"noResult" = "❗ Nenhum resultado!"
"noQuery" = "❌ Consulta não encontrada! Por favor, use o comando novamente!"
"wentWrong" = "❌ Algo deu errado!"
"noIpRecord" = "❗ Nenhum registro de IP!"
"noInbounds" = "❗ Nenhuma entrada encontrada!"
"unlimited" = "♾ Ilimitado (Reiniciar)"
"add" = "Adicionar"
"month" = "Mês"
"months" = "Meses"
"day" = "Dia"
"days" = "Dias"
"hours" = "Horas"
"unknown" = "Desconhecido"
"inbounds" = "Entradas"
"clients" = "Clientes"
"offline" = "🔴 Offline"
"online" = "🟢 Online"

[tgbot.commands]
"unknown" = "❗ Comando desconhecido."
"pleaseChoose" = "👇 Escolha:\r\n"
"help" = "🤖 Bem-vindo a este bot! Ele foi projetado para oferecer dados específicos do painel da web e permite que você faça as modificações necessárias.\r\n\r\n"
"start" = "👋 Olá <i>{{ .Firstname }}</i>.\r\n"
"welcome" = "🤖 Bem-vindo ao bot de gerenciamento do <b>{{ .Hostname }}</b>.\r\n"
"status" = "✅ Bot está OK!"
"usage" = "❗ Por favor, forneça um texto para pesquisar!"
"getID" = "🆔 Seu ID: <code>{{ .ID }}</code>"
"helpAdminCommands" = "Para reiniciar o Xray Core:\r\n<code>/restart</code>\r\n\r\nPara pesquisar por um email de cliente:\r\n<code>/usage [Email]</code>\r\n\r\nPara pesquisar por inbounds (com estatísticas do cliente):\r\n<code>/inbound [Remark]</code>\r\n\r\nTelegram Chat ID:\r\n<code>/id</code>"
"helpClientCommands" = "Para pesquisar por estatísticas, use o seguinte comando:\r\n\r\n<code>/usage [Email]</code>\r\n\r\nTelegram Chat ID:\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ Operação bem-sucedida!"
"restartFailed" = "❗ Erro na operação.\r\n\r\n<code>Erro: {{ .Error }}</code>."
"xrayNotRunning" = "❗ Xray Core não está em execução."
"startDesc" = "Mostrar menu principal"
"helpDesc" = "Ajuda do bot"
"statusDesc" = "Verificar status do bot"
"idDesc" = "Mostrar seu ID do Telegram"

[tgbot.messages]
"cpuThreshold" = "🔴 A carga da CPU {{ .Percent }}% excede o limite de {{ .Threshold }}%"
"selectUserFailed" = "❌ Erro na seleção do usuário!"
"userSaved" = "✅ Usuário do Telegram salvo."
"loginSuccess" = "✅ Conectado ao painel com sucesso.\r\n"
"loginFailed" = "❗️Tentativa de login no painel falhou.\r\n"
"report" = "🕰 Relatórios agendados: {{ .RunTime }}\r\n"
"datetime" = "⏰ Data&Hora: {{ .DateTime }}\r\n"
"hostname" = "💻 Host: {{ .Hostname }}\r\n"
"version" = "🚀 Versão 3X-UI: {{ .Version }}\r\n"
"xrayVersion" = "📡 Versão Xray: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6: {{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4: {{ .IPv4 }}\r\n"
"ip" = "🌐 IP: {{ .IP }}\r\n"
"ips" = "🔢 IPs:\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ Tempo de atividade: {{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 Carga do sistema: {{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 RAM: {{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 TCP: {{ .Count }}\r\n"
"udpCount" = "🔸 UDP: {{ .Count }}\r\n"
"traffic" = "🚦 Tráfego: {{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ Status: {{ .State }}\r\n"
"username" = "👤 Nome de usuário: {{ .Username }}\r\n"
"password" = "👤 Senha: {{ .Password }}\r\n"
"time" = "⏰ Hora: {{ .Time }}\r\n"
"inbound" = "📍 Inbound: {{ .Remark }}\r\n"
"port" = "🔌 Porta: {{ .Port }}\r\n"
"expire" = "📅 Data de expiração: {{ .Time }}\r\n"
"expireIn" = "📅 Expira em: {{ .Time }}\r\n"
"active" = "💡 Ativo: {{ .Enable }}\r\n"
"enabled" = "🚨 Ativado: {{ .Enable }}\r\n"
"online" = "🌐 Status da conexão: {{ .Status }}\r\n"
"email" = "📧 Email: {{ .Email }}\r\n"
"upload" = "🔼 Upload: ↑{{ .Upload }}\r\n"
"download" = "🔽 Download: ↓{{ .Download }}\r\n"
"total" = "📊 Total: ↑↓{{ .UpDown }} / {{ .Total }}\r\n"
"TGUser" = "👤 Usuário do Telegram: {{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 {{ .Type }} esgotado:\r\n"
"exhaustedCount" = "🚨 Contagem de {{ .Type }} esgotado:\r\n"
"onlinesCount" = "🌐 Clientes online: {{ .Count }}\r\n"
"disabled" = "🛑 Desativado: {{ .Disabled }}\r\n"
"depleteSoon" = "🔜 Esgotar em breve: {{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 Hora do backup: {{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 Atualizado em: {{ .Time }}\r\n\r\n"
"yes" = "✅ Sim"
"no" = "❌ Não"

"received_id" = "🔑📥 ID atualizado."
"received_password" = "🔑📥 Senha atualizada."
"received_email" = "📧📥 E-mail atualizado."
"received_comment" = "💬📥 Comentário atualizado."
"id_prompt" = "🔑 ID Padrão: {{ .ClientId }}\n\nDigite seu ID."
"pass_prompt" = "🔑 Senha Padrão: {{ .ClientPassword }}\n\nDigite sua senha."
"email_prompt" = "📧 E-mail Padrão: {{ .ClientEmail }}\n\nDigite seu e-mail."
"comment_prompt" = "💬 Comentário Padrão: {{ .ClientComment }}\n\nDigite seu comentário."
"inbound_client_data_id" = "🔄 Entrada: {{ .InboundRemark }}\n\n🔑 ID: {{ .ClientId }}\n📧 Email: {{ .ClientEmail }}\n📊 Tráfego: {{ .ClientTraffic }}\n📅 Data de expiração: {{ .ClientExp }}\n🌐 Limite de IP: {{ .IpLimit }}\n💬 Comentário: {{ .ClientComment }}\n\nAgora você pode adicionar o cliente à entrada!"
"inbound_client_data_pass" = "🔄 Entrada: {{ .InboundRemark }}\n\n🔑 Senha: {{ .ClientPass }}\n📧 Email: {{ .ClientEmail }}\n📊 Tráfego: {{ .ClientTraffic }}\n📅 Data de expiração: {{ .ClientExp }}\n🌐 Limite de IP: {{ .IpLimit }}\n💬 Comentário: {{ .ClientComment }}\n\nAgora você pode adicionar o cliente à entrada!"
"cancel" = "❌ Processo Cancelado! \n\nVocê pode iniciar novamente a qualquer momento com /start. 🔄"
"error_add_client"  = "⚠️ Erro:\n\n {{ .error }}"
"using_default_value"  = "Tudo bem, vou manter o valor padrão. 😊"
"incorrect_input" ="Sua entrada não é válida.\nAs frases devem ser contínuas, sem espaços.\nExemplo correto: aaaaaa\nExemplo incorreto: aaa aaa 🚫"
"AreYouSure" = "Você tem certeza? 🤔"
"SuccessResetTraffic" = "📧 Email: {{ .ClientEmail }}\n🏁 Resultado: ✅ Sucesso"
"FailedResetTraffic" = "📧 Email: {{ .ClientEmail }}\n🏁 Resultado: ❌ Falhou \n\n🛠️ Erro: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 Processo de redefinição de tráfego concluído para todos os clientes."


[tgbot.buttons]
"closeKeyboard" = "❌ Fechar teclado"
"cancel" = "❌ Cancelar"
"cancelReset" = "❌ Cancelar redefinição"
"cancelIpLimit" = "❌ Cancelar limite de IP"
"confirmResetTraffic" = "✅ Confirmar redefinição de tráfego?"
"confirmClearIps" = "✅ Confirmar limpar IPs?"
"confirmRemoveTGUser" = "✅ Confirmar remover usuário do Telegram?"
"confirmToggle" = "✅ Confirmar ativar/desativar usuário?"
"dbBackup" = "Obter backup do DB"
"serverUsage" = "Uso do servidor"
"getInbounds" = "Obter Inbounds"
"depleteSoon" = "Esgotar em breve"
"clientUsage" = "Obter uso"
"onlines" = "Clientes online"
"commands" = "Comandos"
"refresh" = "🔄 Atualizar"
"clearIPs" = "❌ Limpar IPs"
"removeTGUser" = "❌ Remover usuário do Telegram"
"selectTGUser" = "👤 Selecionar usuário do Telegram"
"selectOneTGUser" = "👤 Selecione um usuário do Telegram:"
"resetTraffic" = "📈 Redefinir tráfego"
"resetExpire" = "📅 Alterar data de expiração"
"ipLog" = "🔢 Log de IP"
"ipLimit" = "🔢 Limite de IP"
"setTGUser" = "👤 Definir usuário do Telegram"
"toggle" = "🔘 Ativar / Desativar"
"custom" = "🔢 Personalizado"
"confirmNumber" = "✅ Confirmar: {{ .Num }}"
"confirmNumberAdd" = "✅ Confirmar adicionar: {{ .Num }}"
"limitTraffic" = "🚧 Limite de tráfego"
"getBanLogs" = "Obter logs de banimento"
"allClients" = "Todos os clientes"

"addClient" = "Adicionar Cliente"
"submitDisable" = "Enviar como Desativado ☑️"
"submitEnable" = "Enviar como Ativado ✅"
"use_default" = "🏷️ Usar padrão"
"change_id" = "⚙️🔑 ID"
"change_password" = "⚙️🔑 Senha"
"change_email" = "⚙️📧 E-mail"
"change_comment" = "⚙️💬 Comentário"
"ResetAllTraffics" = "Redefinir Todo o Tráfego"
"SortedTrafficUsageReport" = "Relatório de Uso de Tráfego Ordenado"


[tgbot.answers]
"successfulOperation" = "✅ Operação bem-sucedida!"
"errorOperation" = "❗ Erro na operação."
"getInboundsFailed" = "❌ Falha ao obter inbounds."
"getClientsFailed" = "❌ Falha ao obter clientes."
"canceled" = "❌ {{ .Email }}: Operação cancelada."
"clientRefreshSuccess" = "✅ {{ .Email }}: Cliente atualizado com sucesso."
"IpRefreshSuccess" = "✅ {{ .Email }}: IPs atualizados com sucesso."
"TGIdRefreshSuccess" = "✅ {{ .Email }}: Usuário do Telegram do cliente atualizado com sucesso."
"resetTrafficSuccess" = "✅ {{ .Email }}: Tráfego redefinido com sucesso."
"setTrafficLimitSuccess" = "✅ {{ .Email }}: Limite de tráfego salvo com sucesso."
"expireResetSuccess" = "✅ {{ .Email }}: Dias de expiração redefinidos com sucesso."
"resetIpSuccess" = "✅ {{ .Email }}: Limite de IP {{ .Count }} salvo com sucesso."
"clearIpSuccess" = "✅ {{ .Email }}: IPs limpos com sucesso."
"getIpLog" = "✅ {{ .Email }}: Obter log de IP."
"getUserInfo" = "✅ {{ .Email }}: Obter informações do usuário do Telegram."
"removedTGUserSuccess" = "✅ {{ .Email }}: Usuário do Telegram removido com sucesso."
"enableSuccess" = "✅ {{ .Email }}: Ativado com sucesso."
"disableSuccess" = "✅ {{ .Email }}: Desativado com sucesso."
"askToAddUserId" = "Sua configuração não foi encontrada!\r\nPeça ao seu administrador para usar seu Telegram ChatID em suas configurações.\r\n\r\nSeu ChatID: <code>{{ .TgUserID }}</code>"
"chooseClient" = "Escolha um cliente para Inbound {{ .Inbound }}"
"chooseInbound" = "Escolha um Inbound"
