{{define "component/aClientTable"}}
<template slot="actions" slot-scope="text, client, index">
  <a-tooltip>
    <template slot="title">{{ i18n "qrCode" }}</template>
    <a-icon :style="{ fontSize: '24px' }" class="normal-icon" type="qrcode" v-if="record.hasLink()" @click="showQrcode(record.id,client);"></a-icon>
  </a-tooltip>
  <a-tooltip>
    <template slot="title">{{ i18n "pages.client.edit" }}</template>
    <a-icon :style="{ fontSize: '24px' }" class="normal-icon" type="edit" @click="openEditClient(record.id,client);"></a-icon>
  </a-tooltip>
  <a-tooltip>
    <template slot="title">{{ i18n "info" }}</template>
    <a-icon :style="{ fontSize: '24px' }" class="normal-icon" type="info-circle" @click="showInfo(record.id,client);"></a-icon>
  </a-tooltip>
  <a-tooltip>
    <template slot="title">{{ i18n "pages.inbounds.resetTraffic" }}</template>
    <a-popconfirm @confirm="resetClientTraffic(client,record.id,false)" title='{{ i18n "pages.inbounds.resetTrafficContent"}}' :overlay-class-name="themeSwitcher.currentTheme" ok-text='{{ i18n "reset"}}' cancel-text='{{ i18n "cancel"}}'>
      <a-icon slot="icon" type="question-circle-o" :style="{ color: 'var(--color-primary-100)'}"></a-icon>
      <a-icon :style="{ fontSize: '24px', cursor: 'pointer' }" class="normal-icon" type="retweet" v-if="client.email.length > 0"></a-icon>
    </a-popconfirm>
  </a-tooltip>
  <a-tooltip>
    <template slot="title">
      <span :style="{ color: '#FF4D4F' }"> {{ i18n "delete"}}</span>
    </template>
    <a-popconfirm @confirm="delClient(record.id,client,false)" title='{{ i18n "pages.inbounds.deleteClientContent"}}' :overlay-class-name="themeSwitcher.currentTheme" ok-text='{{ i18n "delete"}}' ok-type="danger" cancel-text='{{ i18n "cancel"}}'>
      <a-icon slot="icon" type="question-circle-o" :style="{ color: '#e04141' }"></a-icon>
      <a-icon :style="{ fontSize: '24px', cursor: 'pointer' }" class="delete-icon" type="delete" v-if="isRemovable(record.id)"></a-icon>
    </a-popconfirm>
  </a-tooltip>
</template>
<template slot="enable" slot-scope="text, client, index">
  <a-switch v-model="client.enable" @change="switchEnableClient(record.id,client)"></a-switch>
</template>
<template slot="online" slot-scope="text, client, index">
  <template v-if="client.enable && isClientOnline(client.email)">
    <a-tag color="green">{{ i18n "online" }}</a-tag>
  </template>
  <template v-else>
    <a-tag>{{ i18n "offline" }}</a-tag>
  </template>
</template>
<template slot="client" slot-scope="text, client">
  <a-space direction="horizontal" :size="2">
    <a-tooltip>
      <template slot="title">
        <template v-if="!isClientEnabled(record, client.email)">{{ i18n "depleted" }}</template>
        <template v-else-if="!client.enable">{{ i18n "disabled" }}</template>
        <template v-else-if="client.enable && isClientOnline(client.email)">{{ i18n "online" }}</template>
      </template>
      <a-badge :class="isClientOnline(client.email)? 'online-animation' : ''" :color="client.enable ? statsExpColor(record, client.email) : themeSwitcher.isDarkTheme ? '#2c3950' : '#bcbcbc'"></a-badge>
    </a-tooltip>
    <a-space direction="vertical" :size="2">
      <span class="client-email">[[ client.email ]]</span>
      <template v-if="client.comment && client.comment.trim()">
        <a-tooltip v-if="client.comment.length > 50" :overlay-class-name="themeSwitcher.currentTheme">
          <template slot="title">
            [[ client.comment ]]
          </template>
          <span class="client-comment">[[ client.comment.substring(0, 47) + '...' ]]</span>
        </a-tooltip>
        <span v-else class="client-comment">[[ client.comment ]]</span>
      </template>
    </a-space>
  </a-space>
</template>
<template slot="traffic" slot-scope="text, client">
  <a-popover :overlay-class-name="themeSwitcher.currentTheme">
    <template slot="content" v-if="client.email">
      <table cellpadding="2" width="100%">
        <tr>
          <td>↑[[ SizeFormatter.sizeFormat(getUpStats(record, client.email)) ]]</td>
          <td>↓[[ SizeFormatter.sizeFormat(getDownStats(record, client.email)) ]]</td>
        </tr>
        <tr v-if="client.totalGB > 0">
          <td>{{ i18n "remained" }}</td>
          <td>[[ SizeFormatter.sizeFormat(getRemStats(record, client.email)) ]]</td>
        </tr>
      </table>
    </template>
    <table>
      <tr class="tr-table-box">
        <td class="tr-table-rt"> [[ SizeFormatter.sizeFormat(getSumStats(record, client.email)) ]] </td>
        <td class="tr-table-bar" v-if="!client.enable">
          <a-progress :stroke-color="themeSwitcher.isDarkTheme ? 'rgb(72 84 105)' : '#bcbcbc'" :show-info="false" :percent="statsProgress(record, client.email)" />
        </td>
        <td class="tr-table-bar" v-else-if="client.totalGB > 0">
          <a-progress :stroke-color="clientStatsColor(record, client.email)" :show-info="false" :status="isClientEnabled(record, client.email)? 'exception' : ''" :percent="statsProgress(record, client.email)" />
        </td>
        <td v-else class="infinite-bar tr-table-bar">
          <a-progress :show-info="false" :percent="100"></a-progress>
        </td>
        <td class="tr-table-lt">
          <template v-if="client.totalGB > 0">[[ client._totalGB + "GB" ]]</template>
          <span v-else class="tr-infinity-ch">&infin;</span>
        </td>
      </tr>
    </table>
  </a-popover>
</template>
<template slot="expiryTime" slot-scope="text, client, index">
  <template v-if="client.expiryTime !=0 && client.reset >0">
    <a-popover :overlay-class-name="themeSwitcher.currentTheme">
      <template slot="content">
        <span v-if="client.expiryTime < 0">{{ i18n "pages.client.delayedStart" }}
        </span>
        <span v-else>
          <template v-if="app.datepicker === 'gregorian'">
            [[ DateUtil.formatMillis(client._expiryTime) ]]
          </template>
          <template v-else>
            [[ DateUtil.convertToJalalian(moment(client._expiryTime)) ]]
          </template>
        </span>
      </template>
      <table>
        <tr class="tr-table-box">
          <td class="tr-table-rt"> [[ remainedDays(client.expiryTime) ]] </td>
          <td class="infinite-bar tr-table-bar">
            <a-progress :show-info="false" :status="isClientEnabled(record, client.email)? 'exception' : ''" :percent="expireProgress(client.expiryTime, client.reset)" />
          </td>
          <td class="tr-table-lt">[[ client.reset + "d" ]]</td>
        </tr>
      </table>
    </a-popover>
  </template>
  <template v-else>
    <a-popover v-if="client.expiryTime != 0" :overlay-class-name="themeSwitcher.currentTheme">
      <template slot="content">
        <span v-if="client.expiryTime < 0">{{ i18n "pages.client.delayedStart" }}
        </span>
        <span v-else>
          <template v-if="app.datepicker === 'gregorian'">
            [[ DateUtil.formatMillis(client._expiryTime) ]]
          </template>
          <template v-else>
            [[ DateUtil.convertToJalalian(moment(client._expiryTime)) ]]
          </template>
        </span>
      </template>
      <a-tag :style="{ minWidth: '50px', border: 'none' }" :color="ColorUtils.userExpiryColor(app.expireDiff, client, themeSwitcher.isDarkTheme)"> [[ remainedDays(client.expiryTime) ]] </a-tag>
    </a-popover>
    <a-tag v-else :color="ColorUtils.userExpiryColor(app.expireDiff, client, themeSwitcher.isDarkTheme)" :style="{ border: 'none' }" class="infinite-tag">
      <svg height="10px" width="14px" viewBox="0 0 640 512" fill="currentColor">
        <path d="M484.4 96C407 96 349.2 164.1 320 208.5C290.8 164.1 233 96 155.6 96C69.75 96 0 167.8 0 256s69.75 160 155.6 160C233.1 416 290.8 347.9 320 303.5C349.2 347.9 407 416 484.4 416C570.3 416 640 344.2 640 256S570.3 96 484.4 96zM155.6 368C96.25 368 48 317.8 48 256s48.25-112 107.6-112c67.75 0 120.5 82.25 137.1 112C276 285.8 223.4 368 155.6 368zM484.4 368c-67.75 0-120.5-82.25-137.1-112C364 226.2 416.6 144 484.4 144C543.8 144 592 194.2 592 256S543.8 368 484.4 368z" fill="currentColor"></path>
      </svg>
    </a-tag>
  </template>
</template>
<template slot="actionMenu" slot-scope="text, client, index">
  <a-dropdown :trigger="['click']">
    <a-icon @click="e => e.preventDefault()" type="ellipsis" :style="{ fontSize: '20px' }"></a-icon>
    <a-menu slot="overlay" :theme="themeSwitcher.currentTheme">
      <a-menu-item v-if="record.hasLink()" @click="showQrcode(record.id,client);">
        <a-icon :style="{ fontSize: '14px' }" type="qrcode"></a-icon>
        {{ i18n "qrCode" }}
      </a-menu-item>
      <a-menu-item @click="openEditClient(record.id,client);">
        <a-icon :style="{ fontSize: '14px' }" type="edit"></a-icon>
        {{ i18n "pages.client.edit" }}
      </a-menu-item>
      <a-menu-item @click="showInfo(record.id,client);">
        <a-icon :style="{ fontSize: '14px' }" type="info-circle"></a-icon>
        {{ i18n "info" }}
      </a-menu-item>
      <a-menu-item @click="resetClientTraffic(client,record.id)" v-if="client.email.length > 0">
        <a-icon :style="{ fontSize: '14px' }" type="retweet"></a-icon>
        {{ i18n "pages.inbounds.resetTraffic" }}
      </a-menu-item>
      <a-menu-item v-if="isRemovable(record.id)" @click="delClient(record.id,client)">
        <a-icon :style="{ fontSize: '14px' }" type="delete"></a-icon>
        <span :style="{ color: '#FF4D4F' }"> {{ i18n "delete"}}</span>
      </a-menu-item>
      <a-menu-item>
        <a-switch v-model="client.enable" size="small" @change="switchEnableClient(record.id,client)"></a-switch>
        {{ i18n "enable"}}
      </a-menu-item>
    </a-menu>
  </a-dropdown>
</template>
<template slot="info" slot-scope="text, client, index">
  <a-popover placement="bottomRight" :overlay-class-name="themeSwitcher.currentTheme" trigger="click">
    <template slot="content">
      <table>
        <tr>
          <td colspan="3" :style="{ textAlign: 'center' }">{{ i18n "pages.inbounds.traffic" }}</td>
        </tr>
        <tr>
          <td width="80px" :style="{ margin: '0', textAlign: 'right', fontSize: '1em' }"> [[ SizeFormatter.sizeFormat(getUpStats(record, client.email) + getDownStats(record, client.email)) ]] </td>
          <td width="120px" v-if="!client.enable">
            <a-progress :stroke-color="themeSwitcher.isDarkTheme ? 'rgb(72 84 105)' : '#bcbcbc'" :show-info="false" :percent="statsProgress(record, client.email)" />
          </td>
          <td width="120px" v-else-if="client.totalGB > 0">
            <a-popover :overlay-class-name="themeSwitcher.currentTheme">
              <template slot="content" v-if="client.email">
                <table cellpadding="2" width="100%">
                  <tr>
                    <td>↑[[ SizeFormatter.sizeFormat(getUpStats(record, client.email)) ]]</td>
                    <td>↓[[ SizeFormatter.sizeFormat(getDownStats(record, client.email)) ]]</td>
                  </tr>
                  <tr>
                    <td>{{ i18n "remained" }}</td>
                    <td>[[ SizeFormatter.sizeFormat(getRemStats(record, client.email)) ]]</td>
                  </tr>
                </table>
              </template>
              <a-progress :stroke-color="clientStatsColor(record, client.email)" :show-info="false" :status="isClientEnabled(record, client.email)? 'exception' : ''" :percent="statsProgress(record, client.email)" />
            </a-popover>
          </td>
          <td width="120px" v-else class="infinite-bar">
            <a-progress :stroke-color="themeSwitcher.isDarkTheme ? '#2c1e32':'#F2EAF1'" :show-info="false" :percent="100"></a-progress>
          </td>
          <td width="80px">
            <template v-if="client.totalGB > 0">[[ client._totalGB + "GB" ]]</template>
            <span v-else class="tr-infinity-ch">&infin;</span>
          </td>
        </tr>
        <tr>
          <td colspan="3" :style="{ textAlign: 'center' }">
            <a-divider :style="{ margin: '0', borderCollapse: 'separate' }"></a-divider>
            {{ i18n "pages.inbounds.expireDate" }}
          </td>
        </tr>
        <tr>
          <template v-if="client.expiryTime !=0 && client.reset >0">
            <td width="80px" :style="{ margin: '0', textAlign: 'right', fontSize: '1em' }"> [[ remainedDays(client.expiryTime) ]] </td>
            <td width="120px" class="infinite-bar">
              <a-popover :overlay-class-name="themeSwitcher.currentTheme">
                <template slot="content">
                  <span v-if="client.expiryTime < 0">{{ i18n "pages.client.delayedStart" }}
                  </span>
                  <span v-else>
                    <template v-if="app.datepicker === 'gregorian'">
                      [[ DateUtil.formatMillis(client._expiryTime) ]]
                    </template>
                    <template v-else>
                      [[ DateUtil.convertToJalalian(moment(client._expiryTime)) ]]
                    </template>
                  </span>
                </template>
                <a-progress :show-info="false" :status="isClientEnabled(record, client.email)? 'exception' : ''" :percent="expireProgress(client.expiryTime, client.reset)" />
              </a-popover>
            </td>
            <td width="60px">[[ client.reset + "d" ]]</td>
          </template>
          <template v-else>
            <td colspan="3" :style="{ textAlign: 'center' }">
              <a-popover v-if="client.expiryTime != 0" :overlay-class-name="themeSwitcher.currentTheme">
                <template slot="content">
                  <span v-if="client.expiryTime < 0">{{ i18n "pages.client.delayedStart" }}
                  </span>
                  <span v-else>
                    <template v-if="app.datepicker === 'gregorian'">
                      [[ DateUtil.formatMillis(client._expiryTime) ]]
                    </template>
                    <template v-else>
                      [[ DateUtil.convertToJalalian(moment(client._expiryTime)) ]]
                    </template>
                  </span>
                </template>
                <a-tag :style="{ minWidth: '50px', border: 'none' }" :color="ColorUtils.userExpiryColor(app.expireDiff, client, themeSwitcher.isDarkTheme)"> [[ remainedDays(client.expiryTime) ]] </a-tag>
              </a-popover>
              <a-tag v-else :color="client.enable ? 'purple' : themeSwitcher.isDarkTheme ? '#2c3950' : '#bcbcbc'" class="infinite-tag">
                <svg height="10px" width="14px" viewBox="0 0 640 512" fill="currentColor">
                  <path d="M484.4 96C407 96 349.2 164.1 320 208.5C290.8 164.1 233 96 155.6 96C69.75 96 0 167.8 0 256s69.75 160 155.6 160C233.1 416 290.8 347.9 320 303.5C349.2 347.9 407 416 484.4 416C570.3 416 640 344.2 640 256S570.3 96 484.4 96zM155.6 368C96.25 368 48 317.8 48 256s48.25-112 107.6-112c67.75 0 120.5 82.25 137.1 112C276 285.8 223.4 368 155.6 368zM484.4 368c-67.75 0-120.5-82.25-137.1-112C364 226.2 416.6 144 484.4 144C543.8 144 592 194.2 592 256S543.8 368 484.4 368z" fill="currentColor"></path>
                </svg>
              </a-tag>
          </template>
          </td>
        </tr>
      </table>
    </template>
    <a-badge>
      <a-icon v-if="!client.enable" slot="count" type="pause-circle" theme="filled" :style="{ color: themeSwitcher.isDarkTheme ? '#2c3950' : '#bcbcbc' }"></a-icon>
      <a-button shape="round" size="small" :style="{ fontSize: '14px', padding: '0 10px' }">
        <a-icon type="solution"></a-icon>
      </a-button>
    </a-badge>
  </a-popover>
</template>
{{end}}
