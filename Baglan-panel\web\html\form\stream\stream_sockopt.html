{{define "form/streamSockopt"}}
<a-divider :style="{ margin: '5px 0 0' }"></a-divider>
<a-form :colon="false" :label-col="{ md: {span:8} }" :wrapper-col="{ md: {span:14} }">
    <a-form-item label="Sockopt">
        <a-switch v-model="inbound.stream.sockoptSwitch"></a-switch>
    </a-form-item>
    <template v-if="inbound.stream.sockoptSwitch">
        <a-form-item label="Route Mark">
            <a-input-number v-model.number="inbound.stream.sockopt.mark" :min="0"></a-input-number>
        </a-form-item>
        <a-form-item label="TCP Keep Alive Interval">
            <a-input-number v-model.number="inbound.stream.sockopt.tcpKeepAliveInterval" :min="0"></a-input-number>
        </a-form-item>
        <a-form-item label="TCP Keep Alive Idle">
            <a-input-number v-model.number="inbound.stream.sockopt.tcpKeepAliveIdle" :min="0"></a-input-number>
        </a-form-item>
        <a-form-item label="TCP Max Seg">
            <a-input-number v-model.number="inbound.stream.sockopt.tcpMaxSeg" :min="0"></a-input-number>
        </a-form-item>
        <a-form-item label="TCP User Timeout">
            <a-input-number v-model.number="inbound.stream.sockopt.tcpUserTimeout" :min="0"></a-input-number>
        </a-form-item>
        <a-form-item label="TCP Window Clamp">
            <a-input-number v-model.number="inbound.stream.sockopt.tcpWindowClamp" :min="0"></a-input-number>
        </a-form-item>
        <a-form-item label="Proxy Protocol">
            <a-switch v-model="inbound.stream.sockopt.acceptProxyProtocol"></a-switch>
        </a-form-item>
        <a-form-item label="TCP Fast Open">
            <a-switch v-model.trim="inbound.stream.sockopt.tcpFastOpen"></a-switch>
        </a-form-item>
        <a-form-item label="Multipath TCP">
            <a-switch v-model.trim="inbound.stream.sockopt.tcpMptcp"></a-switch>
        </a-form-item>
        <a-form-item label="Penetrate">
            <a-switch v-model.trim="inbound.stream.sockopt.penetrate"></a-switch>
        </a-form-item>
        <a-form-item label="V6 Only">
            <a-switch v-model.trim="inbound.stream.sockopt.V6Only"></a-switch>
        </a-form-item>
        <a-form-item label='Domain Strategy'>
            <a-select v-model="inbound.stream.sockopt.domainStrategy" :style="{ width: '50%' }" :dropdown-class-name="themeSwitcher.currentTheme">
              <a-select-option v-for="key in DOMAIN_STRATEGY_OPTION" :value="key">[[ key ]]</a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label='TCP Congestion'>
            <a-select v-model="inbound.stream.sockopt.tcpcongestion" :style="{ width: '50%' }" :dropdown-class-name="themeSwitcher.currentTheme">
              <a-select-option v-for="key in TCP_CONGESTION_OPTION" :value="key">[[ key ]]</a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="TProxy">
            <a-select v-model="inbound.stream.sockopt.tproxy" :style="{ width: '50%' }" :dropdown-class-name="themeSwitcher.currentTheme">
                <a-select-option value="off">Off</a-select-option>
                <a-select-option value="redirect">Redirect</a-select-option>
                <a-select-option value="tproxy">TProxy</a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="Dialer Proxy">
            <a-input v-model="inbound.stream.sockopt.dialerProxy"></a-input>
        </a-form-item>
        <a-form-item label="Interface Name">
            <a-input v-model="inbound.stream.sockopt.interfaceName"></a-input>
        </a-form-item>
    </template>
</a-form>
{{end}}
