{{define "modals/textModal"}}
<a-modal id="text-modal" v-model="txtModal.visible" :title="txtModal.title" :closable="true"
    :class="themeSwitcher.currentTheme">
    <a-input :style="{ overflowY: 'auto' }" type="textarea" v-model="txtModal.content"
        :autosize="{ minRows: 10, maxRows: 20}"></a-input>
    <template slot="footer">
        <a-button v-if="!ObjectUtil.isEmpty(txtModal.fileName)" icon="download"
            @click="FileManager.downloadTextFile(txtModal.content, txtModal.fileName)">
            <span>[[ txtModal.fileName ]]</span>
        </a-button>
        <a-button type="primary" icon="copy" @click="txtModal.copy(txtModal.content)">
            <span>{{ i18n "copy" }}</span>
        </a-button>
    </template>
</a-modal>

<script>
    const txtModal = {
        title: '',
        content: '',
        fileName: '',
        qrcode: null,
        visible: false,
        show: function (title = '', content = '', fileName = '') {
            this.title = title;
            this.content = content;
            this.fileName = fileName;
            this.visible = true;
        },
        copy: function (content = '') {
            ClipboardManager
                .copyText(content)
                .then(() => {
                    app.$message.success('{{ i18n "copied" }}')
                    this.close();
                })
        },
        close: function () {
            this.visible = false;
        },
    };

    const textModalApp = new Vue({
        delimiters: ['[[', ']]'],
        el: '#text-modal',
        data: {
            txtModal: txtModal,
        },
    });
</script>
{{end}}