"username" = "用户名"
"password" = "密码"
"login" = "登录"
"confirm" = "确定"
"cancel" = "取消"
"close" = "关闭"
"create" = "创建"
"update" = "更新"
"copy" = "复制"
"copied" = "已复制"
"download" = "下载"
"remark" = "备注"
"enable" = "启用"
"protocol" = "协议"
"search" = "搜索"
"filter" = "筛选"
"loading" = "加载中..."
"second" = "秒"
"minute" = "分钟"
"hour" = "小时"
"day" = "天"
"check" = "查看"
"indefinite" = "无限期"
"unlimited" = "无限制"
"none" = "无"
"qrCode" = "二维码"
"info" = "更多信息"
"edit" = "编辑"
"delete" = "删除"
"reset" = "重置"
"noData" = "无数据。"
"copySuccess" = "复制成功"
"sure" = "确定"
"encryption" = "加密"
"useIPv4ForHost" = "使用 IPv4 连接主机"
"transmission" = "传输"
"host" = "主机"
"path" = "路径"
"camouflage" = "伪装"
"status" = "状态"
"enabled" = "开启"
"disabled" = "关闭"
"depleted" = "耗尽"
"depletingSoon" = "即将耗尽"
"offline" = "离线"
"online" = "在线"
"domainName" = "域名"
"monitor" = "监听"
"certificate" = "数字证书"
"fail" = "失败"
"comment" = "评论"
"success" = "成功"
"getVersion" = "获取版本"
"install" = "安装"
"clients" = "客户端"
"usage" = "使用情况"
"twoFactorCode" = "代码"
"remained" = "剩余"
"security" = "安全"
"secAlertTitle" = "安全警报"
"secAlertSsl" = "此连接不安全。在激活 TLS 进行数据保护之前，请勿输入敏感信息。"
"secAlertConf" = "某些设置易受攻击。建议加强安全协议以防止潜在漏洞。"
"secAlertSSL" = "面板缺少安全连接。请安装 TLS 证书以保护数据安全。"
"secAlertPanelPort" = "面板默认端口存在安全风险。请配置随机端口或特定端口。"
"secAlertPanelURI" = "面板默认 URI 路径不安全。请配置复杂的 URI 路径。"
"secAlertSubURI" = "订阅默认 URI 路径不安全。请配置复杂的 URI 路径。"
"secAlertSubJsonURI" = "订阅 JSON 默认 URI 路径不安全。请配置复杂的 URI 路径。"
"emptyDnsDesc" = "未添加DNS服务器。"
"emptyFakeDnsDesc" = "未添加Fake DNS服务器。"
"emptyBalancersDesc" = "未添加负载均衡器。"
"emptyReverseDesc" = "未添加反向代理。"
"somethingWentWrong" = "出了点问题"

[menu]
"theme" = "主题"
"dark" = "暗色"
"ultraDark" = "超暗色"
"dashboard" = "系统状态"
"inbounds" = "入站列表"
"settings" = "面板设置"
"xray" = "Xray 设置"
"logout" = "退出登录"
"link" = "管理"

[pages.login]
"hello" = "你好"
"title" = "欢迎"
"loginAgain" = "登录时效已过，请重新登录"

[pages.login.toasts]
"invalidFormData" = "数据格式错误"
"emptyUsername" = "请输入用户名"
"emptyPassword" = "请输入密码"
"wrongUsernameOrPassword" = "用户名、密码或双重验证码无效。"  
"successLogin" = "您已成功登录您的账户。"

[pages.index]
"title" = "系统状态"
"cpu" = "CPU"
"logicalProcessors" = "逻辑处理器"
"frequency" = "频率"
"swap" = "交换分区"
"storage" = "存储"
"memory" = "内存"
"threads" = "线程"
"xrayStatus" = "Xray"
"stopXray" = "停止"
"restartXray" = "重启"
"xraySwitch" = "版本"
"xraySwitchClick" = "选择你要切换到的版本"
"xraySwitchClickDesk" = "请谨慎选择，因为较旧版本可能与当前配置不兼容"
"xrayStatusUnknown" = "未知"
"xrayStatusRunning" = "运行中"
"xrayStatusStop" = "停止"
"xrayStatusError" = "错误"
"xrayErrorPopoverTitle" = "运行Xray时发生错误"
"operationHours" = "系统正常运行时间"
"systemLoad" = "系统负载"
"systemLoadDesc" = "过去 1、5 和 15 分钟的系统平均负载"
"connectionTcpCountDesc" = "系统中所有 TCP 连接数"
"connectionUdpCountDesc" = "系统中所有 UDP 连接数"
"connectionCount" = "连接数"
"ipAddresses" = "IP地址"
"toggleIpVisibility" = "切换IP可见性"
"overallSpeed" = "整体速度"
"upload" = "上传"
"download" = "下载"
"totalData" = "总数据"
"sent" = "已发送"
"received" = "已接收"
"documentation" = "文档"
"xraySwitchVersionDialog" = "您确定要更改Xray版本吗？"
"xraySwitchVersionDialogDesc" = "这将把Xray版本更改为#version#。"
"xraySwitchVersionPopover" = "Xray 更新成功"
"geofileUpdateDialog" = "您确定要更新地理文件吗？"
"geofileUpdateDialogDesc" = "这将更新 #filename# 文件。"
"geofileUpdatePopover" = "地理文件更新成功"
"dontRefresh" = "安装中，请勿刷新此页面"
"logs" = "日志"
"config" = "配置"
"backup" = "备份"
"backupTitle" = "备份和恢复数据库"
"exportDatabase" = "备份"
"exportDatabaseDesc" = "点击下载包含当前数据库备份的 .db 文件到您的设备。"
"importDatabase" = "恢复"
"importDatabaseDesc" = "点击选择并上传设备中的 .db 文件以从备份恢复数据库。"
"importDatabaseSuccess" = "数据库导入成功"
"importDatabaseError" = "导入数据库时出错"
"readDatabaseError" = "读取数据库时出错"
"getDatabaseError" = "检索数据库时出错"
"getConfigError" = "检索配置文件时出错"

[pages.inbounds]
"title" = "入站列表"
"totalDownUp" = "总上传 / 下载"
"totalUsage" = "总用量"
"inboundCount" = "入站数量"
"operate" = "菜单"
"enable" = "启用"
"remark" = "备注"
"protocol" = "协议"
"port" = "端口"
"traffic" = "流量"
"details" = "详细信息"
"transportConfig" = "传输配置"
"expireDate" = "到期时间"
"resetTraffic" = "重置流量"
"addInbound" = "添加入站"
"generalActions" = "通用操作"
"autoRefresh" = "自动刷新"
"autoRefreshInterval" = "间隔"
"modifyInbound" = "修改入站"
"deleteInbound" = "删除入站"
"deleteInboundContent" = "确定要删除入站吗？"
"deleteClient" = "删除客户端"
"deleteClientContent" = "确定要删除客户端吗？"
"resetTrafficContent" = "确定要重置流量吗？"
"inboundUpdateSuccess" = "入站连接已成功更新。"
"inboundCreateSuccess" = "入站连接已成功创建。"
"copyLink" = "复制链接"
"address" = "地址"
"network" = "网络"
"destinationPort" = "目标端口"
"targetAddress" = "目标地址"
"monitorDesc" = "留空表示监听所有 IP"
"meansNoLimit" = "= 无限制（单位：GB)"
"totalFlow" = "总流量"
"leaveBlankToNeverExpire" = "留空表示永不过期"
"noRecommendKeepDefault" = "建议保留默认值"
"certificatePath" = "文件路径"
"certificateContent" = "文件内容"
"publicKey" = "公钥"
"privatekey" = "私钥"
"clickOnQRcode" = "点击二维码复制"
"client" = "客户"
"export" = "导出链接"
"clone" = "克隆"
"cloneInbound" = "克隆"
"cloneInboundContent" = "此入站规则除端口（Port）、监听 IP（Listening IP）和客户端（Clients）以外的所有配置都将应用于克隆"
"cloneInboundOk" = "创建克隆"
"resetAllTraffic" = "重置所有入站流量"
"resetAllTrafficTitle" = "重置所有入站流量"
"resetAllTrafficContent" = "确定要重置所有入站流量吗？"
"resetInboundClientTraffics" = "重置客户端流量"
"resetInboundClientTrafficTitle" = "重置所有客户端流量"
"resetInboundClientTrafficContent" = "确定要重置此入站客户端的所有流量吗？"
"resetAllClientTraffics" = "重置所有客户端流量"
"resetAllClientTrafficTitle" = "重置所有客户端流量"
"resetAllClientTrafficContent" = "确定要重置所有客户端的所有流量吗？"
"delDepletedClients" = "删除流量耗尽的客户端"
"delDepletedClientsTitle" = "删除流量耗尽的客户端"
"delDepletedClientsContent" = "确定要删除所有流量耗尽的客户端吗？"
"email" = "电子邮件"
"emailDesc" = "电子邮件必须完全唯一"
"IPLimit" = "IP 限制"
"IPLimitDesc" = "如果数量超过设置值，则禁用入站流量。（0 = 禁用）"
"IPLimitlog" = "IP 日志"
"IPLimitlogDesc" = "IP 历史日志（要启用被禁用的入站流量，请清除日志）"
"IPLimitlogclear" = "清除日志"
"setDefaultCert" = "从面板设置证书"
"telegramDesc" = "请提供Telegram聊天ID。（在机器人中使用'/id'命令）或（@userinfobot"
"subscriptionDesc" = "要找到你的订阅 URL，请导航到“详细信息”。此外，你可以为多个客户端使用相同的名称。"
"info" = "信息"
"same" = "相同"
"inboundData" = "入站数据"
"exportInbound" = "导出入站规则"
"import"="导入"
"importInbound" = "导入入站规则"

[pages.client]
"add" = "添加客户端"
"edit" = "编辑客户端"
"submitAdd" = "添加客户端"
"submitEdit" = "保存修改"
"clientCount" = "客户端数量"
"bulk" = "批量创建"
"method" = "方法"
"first" = "置顶"
"last" = "置底"
"prefix" = "前缀"
"postfix" = "后缀"
"delayedStart" = "首次使用后开始"
"expireDays" = "期间"
"days" = "天"
"renew" = "自动续订"
"renewDesc" = "到期后自动续订。(0 = 禁用)(单位: 天)"

[pages.inbounds.toasts]
"obtain" = "获取"
"updateSuccess" = "更新成功"
"logCleanSuccess" = "日志已清除"
"inboundsUpdateSuccess" = "入站连接已成功更新"
"inboundUpdateSuccess" = "入站连接已成功更新"
"inboundCreateSuccess" = "入站连接已成功创建"
"inboundDeleteSuccess" = "入站连接已成功删除"
"inboundClientAddSuccess" = "已添加入站客户端"
"inboundClientDeleteSuccess" = "入站客户端已删除"
"inboundClientUpdateSuccess" = "入站客户端已更新"
"delDepletedClientsSuccess" = "所有耗尽客户端已删除"
"resetAllClientTrafficSuccess" = "客户端所有流量已重置"
"resetAllTrafficSuccess" = "所有流量已重置"
"resetInboundClientTrafficSuccess" = "流量已重置"
"trafficGetError" = "获取流量数据时出错"
"getNewX25519CertError" = "获取X25519证书时出错。"

[pages.inbounds.stream.general]
"request" = "请求"
"response" = "响应"
"name" = "名称"
"value" = "值"

[pages.inbounds.stream.tcp]
"version" = "版本"
"method" = "方法"
"path" = "路径"
"status" = "状态"
"statusDescription" = "状态说明"
"requestHeader" = "请求头"
"responseHeader" = "响应头"

[pages.settings]
"title" = "面板设置"
"save" = "保存"
"infoDesc" = "此处的所有更改都需要保存并重启面板才能生效"
"restartPanel" = "重启面板"
"restartPanelDesc" = "确定要重启面板吗？若重启后无法访问面板，请前往服务器查看面板日志信息"
"restartPanelSuccess" = "面板已成功重启"
"actions" = "操作"
"resetDefaultConfig" = "重置为默认配置"
"panelSettings" = "常规"
"securitySettings" = "安全设定"
"TGBotSettings" = "Telegram 机器人配置"
"panelListeningIP" = "面板监听 IP"
"panelListeningIPDesc" = "默认留空监听所有 IP"
"panelListeningDomain" = "面板监听域名"
"panelListeningDomainDesc" = "默认情况下留空以监视所有域名和 IP 地址"
"panelPort" = "面板监听端口"
"panelPortDesc" = "重启面板生效"
"publicKeyPath" = "面板证书公钥文件路径"
"publicKeyPathDesc" = "填写一个 '/' 开头的绝对路径"
"privateKeyPath" = "面板证书密钥文件路径"
"privateKeyPathDesc" = "填写一个 '/' 开头的绝对路径"
"panelUrlPath" = "面板 url 根路径"
"panelUrlPathDesc" = "必须以 '/' 开头，以 '/' 结尾"
"pageSize" = "分页大小"
"pageSizeDesc" = "定义入站表的页面大小。设置 0 表示禁用"
"remarkModel" = "备注模型和分隔符"
"datepicker" = "日期选择器"
"datepickerPlaceholder" = "选择日期"
"datepickerDescription" = "选择器日历类型指定到期日期"
"sampleRemark" = "备注示例"
"oldUsername" = "原用户名"
"currentPassword" = "原密码"
"newUsername" = "新用户名"
"newPassword" = "新密码"
"telegramBotEnable" = "启用 Telegram 机器人"
"telegramBotEnableDesc" = "启用 Telegram 机器人功能"
"telegramToken" = "Telegram 机器人令牌（token）"
"telegramTokenDesc" = "从 '@BotFather' 获取的 Telegram 机器人令牌"
"telegramProxy" = "SOCKS5 Proxy"
"telegramProxyDesc" = "启用 SOCKS5 代理连接到 Telegram（根据指南调整设置）"
"telegramAPIServer" = "Telegram API Server"
"telegramAPIServerDesc" = "要使用的 Telegram API 服务器。留空以使用默认服务器。"
"telegramChatId" = "管理员聊天 ID"
"telegramChatIdDesc" = "Telegram 管理员聊天 ID (多个以逗号分隔)（可通过 @userinfobot 获取，或在机器人中使用 '/id' 命令获取）"
"telegramNotifyTime" = "通知时间"
"telegramNotifyTimeDesc" = "设置周期性的 Telegram 机器人通知时间（使用 crontab 时间格式）"
"tgNotifyBackup" = "数据库备份"
"tgNotifyBackupDesc" = "发送带有报告的数据库备份文件"
"tgNotifyLogin" = "登录通知"
"tgNotifyLoginDesc" = "当有人试图登录你的面板时显示用户名、IP 地址和时间"
"sessionMaxAge" = "会话时长"
"sessionMaxAgeDesc" = "保持登录状态的时长（单位：分钟）"
"expireTimeDiff" = "到期通知阈值"
"expireTimeDiffDesc" = "达到此阈值时，将收到有关到期时间的通知（单位：天）"
"trafficDiff" = "流量耗尽阈值"
"trafficDiffDesc" = "达到此阈值时，将收到有关流量耗尽的通知（单位：GB）"
"tgNotifyCpu" = "CPU 负载通知阈值"
"tgNotifyCpuDesc" = "CPU 负载超过此阈值时，将收到通知（单位：%）"
"timeZone" = "时区"
"timeZoneDesc" = "定时任务将按照该时区的时间运行"
"subSettings" = "订阅设置"
"subEnable" = "启用订阅服务"
"subEnableDesc" = "启用订阅服务功能"
"subTitle" = "订阅标题"
"subTitleDesc" = "在VPN客户端中显示的标题"
"subListen" = "监听 IP"
"subListenDesc" = "订阅服务监听的 IP 地址（留空表示监听所有 IP）"
"subPort" = "监听端口"
"subPortDesc" = "订阅服务监听的端口号（必须是未使用的端口）"
"subCertPath" = "公钥路径"
"subCertPathDesc" = "订阅服务使用的公钥文件路径（以 '/' 开头）"
"subKeyPath" = "私钥路径"
"subKeyPathDesc" = "订阅服务使用的私钥文件路径（以 '/' 开头）"
"subPath" = "URI 路径"
"subPathDesc" = "订阅服务使用的 URI 路径（以 '/' 开头，以 '/' 结尾）"
"subDomain" = "监听域名"
"subDomainDesc" = "订阅服务监听的域名（留空表示监听所有域名和 IP）"
"subUpdates" = "更新间隔"
"subUpdatesDesc" = "客户端应用中订阅 URL 的更新间隔（单位：小时）"
"subEncrypt" = "编码"
"subEncryptDesc" = "订阅服务返回的内容将采用 Base64 编码"
"subShowInfo" = "显示使用信息"
"subShowInfoDesc" = "客户端应用中将显示剩余流量和日期信息"
"subURI" = "反向代理 URI"
"subURIDesc" = "用于代理后面的订阅 URL 的 URI 路径"
"externalTrafficInformEnable" = "外部交通通知"
"externalTrafficInformEnableDesc" = "每次流量更新时通知外部 API"
"externalTrafficInformURI" = "外部流量通知 URI"
"externalTrafficInformURIDesc" = "流量更新将发送到此 URI"
"fragment" = "分片"
"fragmentDesc" = "启用 TLS hello 数据包分片"
"fragmentSett" = "设置"
"noisesDesc" = "启用 Noises."
"noisesSett" = "Noises 设置"
"mux" = "多路复用器"
"muxDesc" = "在已建立的数据流内传输多个独立的数据流"
"muxSett" = "复用器设置"
"direct" = "直接连接"
"directDesc" = "直接与特定国家的域或IP范围建立连接"
"notifications" = "通知"
"certs" = "证书"
"externalTraffic" = "外部流量"
"dateAndTime" = "日期和时间"
"proxyAndServer" = "代理和服务器"
"intervals" = "间隔"
"information" = "信息"
"language" = "语言"
"telegramBotLanguage" = "Telegram 机器人语言"

[pages.xray]
"title" = "Xray 配置"
"save" = "保存"
"restart" = "重新启动 Xray"
"restartSuccess" = "Xray 已成功重新启动"
"stopSuccess" = "Xray 已成功停止"
"restartError" = "重启Xray时发生错误。"
"stopError" = "停止Xray时发生错误。"
"basicTemplate" = "基础配置"
"advancedTemplate" = "高级配置"
"generalConfigs" = "常规配置"
"generalConfigsDesc" = "这些选项将决定常规配置"
"logConfigs" = "日志"
"logConfigsDesc" = "日志可能会影响服务器的性能，建议仅在需要时启用"
"blockConfigsDesc" = "这些选项将阻止用户连接到特定协议和网站"
"basicRouting" = "基本路由"
"blockConnectionsConfigsDesc" = "这些选项将根据特定的请求国家阻止流量。"
"directConnectionsConfigsDesc" = "直接连接确保特定的流量不会通过其他服务器路由。"
"blockips" = "阻止IP"
"blockdomains" = "阻止域名"
"directips" = "直接IP"
"directdomains" = "直接域名"
"ipv4Routing" = "IPv4 路由"
"ipv4RoutingDesc" = "此选项将仅通过 IPv4 路由到目标域"
"warpRouting" = "WARP 路由"
"warpRoutingDesc" = "注意：在使用这些选项之前，请按照面板 GitHub 上的步骤在你的服务器上以 socks5 代理模式安装 WARP。WARP 将通过 Cloudflare 服务器将流量路由到网站。"
"Template" = "高级 Xray 配置模板"
"TemplateDesc" = "最终的 Xray 配置文件将基于此模板生成"
"FreedomStrategy" = "Freedom 协议策略"
"FreedomStrategyDesc" = "设置 Freedom 协议中网络的输出策略"
"RoutingStrategy" = "配置路由域策略"
"RoutingStrategyDesc" = "设置 DNS 解析的整体路由策略"
"Torrent" = "屏蔽 BitTorrent 协议"
"Inbounds" = "入站规则"
"InboundsDesc" = "接受来自特定客户端的流量"
"Outbounds" = "出站规则"
"Balancers" = "负载均衡"
"OutboundsDesc" = "设置出站流量传出方式"
"Routings" = "路由规则"
"RoutingsDesc" = "每条规则的优先级都很重要"
"completeTemplate" = "全部"
"logLevel" = "日志级别"
"logLevelDesc" = "错误日志的日志级别，用于指示需要记录的信息"
"accessLog" = "访问日志"
"accessLogDesc" = "访问日志的文件路径。特殊值 'none' 禁用访问日志"
"errorLog" = "错误日志"
"errorLogDesc" = "错误日志的文件路径。特殊值 'none' 禁用错误日志"
"dnsLog" = "DNS 日志"
"dnsLogDesc" = "是否启用 DNS 查询日志"
"maskAddress" = "隐藏地址"
"maskAddressDesc" = "IP 地址掩码，启用时会自动替换日志中出现的 IP 地址。"
"statistics" = "统计"
"statsInboundUplink" = "入站上传统计"
"statsInboundUplinkDesc" = "启用所有入站代理的上行流量统计收集。"
"statsInboundDownlink" = "入站下载统计"
"statsInboundDownlinkDesc" = "启用所有入站代理的下行流量统计收集。"
"statsOutboundUplink" = "出站上传统计"
"statsOutboundUplinkDesc" = "启用所有出站代理的上行流量统计收集。"
"statsOutboundDownlink" = "出站下载统计"
"statsOutboundDownlinkDesc" = "启用所有出站代理的下行流量统计收集。"

[pages.xray.rules]
"first" = "置顶"
"last" = "置底"
"up" = "向上"
"down" = "向下"
"source" = "来源"
"dest" = "目的地址"
"inbound" = "入站"
"outbound" = "出站"
"balancer" = "负载均衡"
"info" = "信息"
"add" = "添加规则"
"edit" = "编辑规则"
"useComma" = "逗号分隔的项目"

[pages.xray.outbound]
"addOutbound" = "添加出站"
"addReverse" = "添加反向"
"editOutbound" = "编辑出站"
"editReverse" = "编辑反向"
"tag" = "标签"
"tagDesc" = "唯一标签"
"address" = "地址"
"reverse" = "反向"
"domain" = "域名"
"type" = "类型"
"bridge" = "Bridge"
"portal" = "Portal"
"link" = "链接"
"intercon" = "互连"
"settings" = "设置"
"accountInfo" = "帐户信息"
"outboundStatus" = "出站状态"
"sendThrough" = "发送通过"

[pages.xray.balancer]
"addBalancer" = "添加负载均衡"
"editBalancer" = "编辑负载均衡"
"balancerStrategy" = "策略"
"balancerSelectors" = "选择器"
"tag" = "标签"
"tagDesc" = "唯一标签"
"balancerDesc" = "无法同时使用 balancerTag 和 outboundTag。如果同时使用，则只有 outboundTag 会生效。"

[pages.xray.wireguard]
"secretKey" = "密钥"
"publicKey" = "公钥"
"allowedIPs" = "允许的 IP"
"endpoint" = "端点"
"psk" = "共享密钥"
"domainStrategy" = "域策略"

[pages.xray.dns]
"enable" = "启用 DNS"
"enableDesc" = "启用内置 DNS 服务器"
"tag" = "DNS 入站标签"
"tagDesc" = "此标签将在路由规则中可用作入站标签"
"clientIp" = "客户端IP"
"clientIpDesc" = "用于在DNS查询期间通知服务器指定的IP位置"
"disableCache" = "禁用缓存"
"disableCacheDesc" = "禁用DNS缓存"
"disableFallback" = "禁用回退"
"disableFallbackDesc" = "禁用回退DNS查询"
"disableFallbackIfMatch" = "匹配时禁用回退"
"disableFallbackIfMatchDesc" = "当DNS服务器的匹配域名列表命中时，禁用回退DNS查询"
"strategy" = "查询策略"
"strategyDesc" = "解析域名的总体策略"
"add" = "添加服务器"
"edit" = "编辑服务器"
"domains" = "域"
"expectIPs" = "预期 IP"
"unexpectIPs" = "意外IP"
"useSystemHosts" = "使用系统Hosts"
"useSystemHostsDesc" = "使用已安装系统的hosts文件"
"usePreset" = "使用模板"
"dnsPresetTitle" = "DNS模板"
"dnsPresetFamily" = "家庭"

[pages.xray.fakedns]
"add" = "添加假 DNS"
"edit" = "编辑假 DNS"
"ipPool" = "IP 池子网"
"poolSize" = "池大小"

[pages.settings.security]
"admin" = "管理员凭据"
"twoFactor" = "双重验证"  
"twoFactorEnable" = "启用2FA"  
"twoFactorEnableDesc" = "增加额外的验证层以提高安全性。"  
"twoFactorModalSetTitle" = "启用双重认证"
"twoFactorModalDeleteTitle" = "停用双重认证"
"twoFactorModalSteps" = "要设定双重认证，请执行以下步骤："
"twoFactorModalFirstStep" = "1. 在认证应用程序中扫描此QR码，或复制QR码附近的令牌并粘贴到应用程序中"
"twoFactorModalSecondStep" = "2. 输入应用程序中的验证码"
"twoFactorModalRemoveStep" = "输入应用程序中的验证码以移除双重认证。"
"twoFactorModalChangeCredentialsTitle" = "更改凭据"
"twoFactorModalChangeCredentialsStep" = "输入应用程序中的代码以更改管理员凭据。"
"twoFactorModalSetSuccess" = "双因素认证已成功建立"
"twoFactorModalDeleteSuccess" = "双因素认证已成功删除"
"twoFactorModalError" = "验证码错误"

[pages.settings.toasts]
"modifySettings" = "参数已更改。"
"getSettings" = "获取参数时发生错误"
"modifyUserError" = "更改管理员凭据时发生错误。"
"modifyUser" = "您已成功更改管理员凭据。"
"originalUserPassIncorrect" = "原用户名或原密码错误"
"userPassMustBeNotEmpty" = "新用户名和新密码不能为空"
"getOutboundTrafficError" = "获取出站流量错误"
"resetOutboundTrafficError" = "重置出站流量错误"

[tgbot]
"keyboardClosed" = "❌ 自定义键盘已关闭！"
"noResult" = "❗ 没有结果！"
"noQuery" = "❌ 未找到查询！请重新使用命令！"
"wentWrong" = "❌ 出了点问题！"
"noIpRecord" = "❗ 没有 IP 记录！"
"noInbounds" = "❗ 没有找到入站连接！"
"unlimited" = "♾ 无限制"
"add" = "添加"
"month" = "月"
"months" = "月"
"day" = "天"
"days" = "天"
"hours" = "小时"
"unknown" = "未知"
"inbounds" = "入站连接"
"clients" = "客户端"
"offline" = "🔴 离线"
"online" = "🟢 在线"

[tgbot.commands]
"unknown" = "❗ 未知命令"
"pleaseChoose" = "👇 请选择：\r\n"
"help" = "🤖 欢迎使用本机器人！它旨在为您提供来自服务器的特定数据，并允许您进行必要的修改。\r\n\r\n"
"start" = "👋 你好，<i>{{ .Firstname }}</i>。\r\n"
"welcome" = "🤖 欢迎来到 <b>{{ .Hostname }}</b> 管理机器人。\r\n"
"status" = "✅ 机器人正常运行！"
"usage" = "❗ 请输入要搜索的文本！"
"getID" = "🆔 您的 ID 为：<code>{{ .ID }}</code>"
"helpAdminCommands" = "要重新启动 Xray Core：\r\n<code>/restart</code>\r\n\r\n要搜索客户电子邮件：\r\n<code>/usage [电子邮件]</code>\r\n\r\n要搜索入站（带有客户统计数据）：\r\n<code>/inbound [备注]</code>\r\n\r\nTelegram聊天ID：\r\n<code>/id</code>"
"helpClientCommands" = "要搜索统计数据，请使用以下命令：\r\n<code>/usage [电子邮件]</code>\r\n\r\nTelegram聊天ID：\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ 操作成功!"
"restartFailed" = "❗ 操作错误。\r\n\r\n<code>错误: {{ .Error }}</code>."
"xrayNotRunning" = "❗ Xray Core 未运行。"
"startDesc" = "显示主菜单"
"helpDesc" = "机器人帮助"
"statusDesc" = "检查机器人状态"
"idDesc" = "显示您的 Telegram ID"

[tgbot.messages]
"cpuThreshold" = "🔴 CPU 使用率为 {{ .Percent }}%，超过阈值 {{ .Threshold }}%"
"selectUserFailed" = "❌ 用户选择错误！"
"userSaved" = "✅ 电报用户已保存。"
"loginSuccess" = "✅ 成功登录到面板。\r\n"
"loginFailed" = "❗️ 面板登录失败。\r\n"
"report" = "🕰 定时报告：{{ .RunTime }}\r\n"
"datetime" = "⏰ 日期时间：{{ .DateTime }}\r\n"
"hostname" = "💻 主机名：{{ .Hostname }}\r\n"
"version" = "🚀 X-UI 版本：{{ .Version }}\r\n"
"xrayVersion" = "📡 Xray 版本: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6：{{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4：{{ .IPv4 }}\r\n"
"ip" = "🌐 IP：{{ .IP }}\r\n"
"ips" = "🔢 IP 地址：\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ 服务器运行时间：{{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 服务器负载：{{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 服务器内存：{{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 TCP 连接数：{{ .Count }}\r\n"
"udpCount" = "🔸 UDP 连接数：{{ .Count }}\r\n"
"traffic" = "🚦 流量：{{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ Xray 状态：{{ .State }}\r\n"
"username" = "👤 用户名：{{ .Username }}\r\n"
"password" = "👤 密码: {{ .Password }}\r\n"
"time" = "⏰ 时间：{{ .Time }}\r\n"
"inbound" = "📍 入站：{{ .Remark }}\r\n"
"port" = "🔌 端口：{{ .Port }}\r\n"
"expire" = "📅 过期日期：{{ .Time }}\r\n"
"expireIn" = "📅 剩余时间：{{ .Time }}\r\n"
"active" = "💡 激活：{{ .Enable }}\r\n"
"enabled" = "🚨 已启用：{{ .Enable }}\r\n"
"online" = "🌐 连接状态：{{ .Status }}\r\n"
"email" = "📧 邮箱：{{ .Email }}\r\n"
"upload" = "🔼 上传↑：{{ .Upload }}\r\n"
"download" = "🔽 下载↓：{{ .Download }}\r\n"
"total" = "📊 总计：{{ .UpDown }} / {{ .Total }}\r\n"
"TGUser" = "👤 电报用户：{{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 耗尽的 {{ .Type }}：\r\n"
"exhaustedCount" = "🚨 耗尽的 {{ .Type }} 数量：\r\n"
"onlinesCount" = "🌐 在线客户：{{ .Count }}\r\n"
"disabled" = "🛑 禁用：{{ .Disabled }}\r\n"
"depleteSoon" = "🔜 即将耗尽：{{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 备份时间：{{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 刷新时间：{{ .Time }}\r\n\r\n"
"yes" = "✅ 是的"
"no" = "❌ 没有"

"received_id" = "🔑📥 ID 已更新。"
"received_password" = "🔑📥 密码已更新。"
"received_email" = "📧📥 邮箱已更新。"
"received_comment" = "💬📥 评论已更新。"
"id_prompt" = "🔑 默认 ID: {{ .ClientId }}\n\n请输入您的 ID。"
"pass_prompt" = "🔑 默认密码: {{ .ClientPassword }}\n\n请输入您的密码。"
"email_prompt" = "📧 默认邮箱: {{ .ClientEmail }}\n\n请输入您的邮箱。"
"comment_prompt" = "💬 默认评论: {{ .ClientComment }}\n\n请输入您的评论。"
"inbound_client_data_id" = "🔄 入站: {{ .InboundRemark }}\n\n🔑 ID: {{ .ClientId }}\n📧 邮箱: {{ .ClientEmail }}\n📊 流量: {{ .ClientTraffic }}\n📅 到期日期: {{ .ClientExp }}\n🌐 IP 限制: {{ .IpLimit }}\n💬 备注: {{ .ClientComment }}\n\n你现在可以将客户添加到入站了！"
"inbound_client_data_pass" = "🔄 入站: {{ .InboundRemark }}\n\n🔑 密码: {{ .ClientPass }}\n📧 邮箱: {{ .ClientEmail }}\n📊 流量: {{ .ClientTraffic }}\n📅 到期日期: {{ .ClientExp }}\n🌐 IP 限制: {{ .IpLimit }}\n💬 备注: {{ .ClientComment }}\n\n你现在可以将客户添加到入站了！"
"cancel" = "❌ 进程已取消！\n\n您可以随时使用 /start 重新开始。 🔄"
"error_add_client"  = "⚠️ 错误:\n\n {{ .error }}"
"using_default_value"  = "好的，我会使用默认值。 😊"
"incorrect_input" ="您的输入无效。\n短语应连续输入，不能有空格。\n正确示例: aaaaaa\n错误示例: aaa aaa 🚫"
"AreYouSure" = "你确定吗？🤔"
"SuccessResetTraffic" = "📧 邮箱: {{ .ClientEmail }}\n🏁 结果: ✅ 成功"
"FailedResetTraffic" = "📧 邮箱: {{ .ClientEmail }}\n🏁 结果: ❌ 失败 \n\n🛠️ 错误: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 所有客户的流量重置已完成。"


[tgbot.buttons]
"closeKeyboard" = "❌ 关闭键盘"
"cancel" = "❌ 取消"
"cancelReset" = "❌ 取消重置"
"cancelIpLimit" = "❌ 取消 IP 限制"
"confirmResetTraffic" = "✅ 确认重置流量？"
"confirmClearIps" = "✅ 确认清除 IP？"
"confirmRemoveTGUser" = "✅ 确认移除 Telegram 用户？"
"confirmToggle" = "✅ 确认启用/禁用用户？"
"dbBackup" = "获取数据库备份"
"serverUsage" = "服务器使用情况"
"getInbounds" = "获取入站信息"
"depleteSoon" = "即将耗尽"
"clientUsage" = "获取使用情况"
"onlines" = "在线客户端"
"commands" = "命令"
"refresh" = "🔄 刷新"
"clearIPs" = "❌ 清除 IP"
"removeTGUser" = "❌ 移除 Telegram 用户"
"selectTGUser" = "👤 选择 Telegram 用户"
"selectOneTGUser" = "👤 选择一个 Telegram 用户："
"resetTraffic" = "📈 重置流量"
"resetExpire" = "📅 更改到期日期"
"ipLog" = "🔢 IP 日志"
"ipLimit" = "🔢 IP 限制"
"setTGUser" = "👤 设置 Telegram 用户"
"toggle" = "🔘 启用/禁用"
"custom" = "🔢 风俗"
"confirmNumber" = "✅ 确认: {{ .Num }}"
"confirmNumberAdd" = "✅ 确认添加：{{ .Num }}"
"limitTraffic" = "🚧 流量限制"
"getBanLogs" = "禁止日志"
"allClients" = "所有客户"

"addClient" = "添加客户"
"submitDisable" = "提交为禁用 ☑️"
"submitEnable" = "提交为启用 ✅"
"use_default" = "🏷️ 使用默认"
"change_id" = "⚙️🔑 ID"
"change_password" = "⚙️🔑 密码"
"change_email" = "⚙️📧 邮箱"
"change_comment" = "⚙️💬 评论"
"ResetAllTraffics" = "重置所有流量"
"SortedTrafficUsageReport" = "排序的流量使用报告"


[tgbot.answers]
"successfulOperation" = "✅ 成功！"
"errorOperation" = "❗ 操作错误。"
"getInboundsFailed" = "❌ 获取入站信息失败。"
"getClientsFailed" = "❌ 获取客户失败。"
"canceled" = "❌ {{ .Email }}：操作已取消。"
"clientRefreshSuccess" = "✅ {{ .Email }}：客户端刷新成功。"
"IpRefreshSuccess" = "✅ {{ .Email }}：IP 刷新成功。"
"TGIdRefreshSuccess" = "✅ {{ .Email }}：客户端的 Telegram 用户刷新成功。"
"resetTrafficSuccess" = "✅ {{ .Email }}：流量已重置成功。"
"setTrafficLimitSuccess" = "✅ {{ .Email }}: 流量限制保存成功。"
"expireResetSuccess" = "✅ {{ .Email }}：过期天数已重置成功。"
"resetIpSuccess" = "✅ {{ .Email }}：成功保存 IP 限制数量为 {{ .Count }}。"
"clearIpSuccess" = "✅ {{ .Email }}：IP 已成功清除。"
"getIpLog" = "✅ {{ .Email }}：获取 IP 日志。"
"getUserInfo" = "✅ {{ .Email }}：获取 Telegram 用户信息。"
"removedTGUserSuccess" = "✅ {{ .Email }}：Telegram 用户已成功移除。"
"enableSuccess" = "✅ {{ .Email }}：已成功启用。"
"disableSuccess" = "✅ {{ .Email }}：已成功禁用。"
"askToAddUserId" = "未找到您的配置！\r\n请向管理员询问，在您的配置中使用您的 Telegram 用户 ChatID。\r\n\r\n您的用户 ChatID：<code>{{ .TgUserID }}</code>"
"chooseClient" = "为入站 {{ .Inbound }} 选择一个客户"
"chooseInbound" = "选择一个入站"
