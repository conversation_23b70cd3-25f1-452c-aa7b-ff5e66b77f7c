"username" = "Kullanıcı Adı"
"password" = "Şifre"
"login" = "<PERSON><PERSON>ş Yap"
"confirm" = "Onayla"
"cancel" = "İptal"
"close" = "Kapat"
"create" = "Oluştur"
"update" = "Güncelle"
"copy" = "Kopyala"
"copied" = "Kopyalandı"
"download" = "İndir"
"remark" = "Açıklama"
"enable" = "Etkin"
"protocol" = "Protokol"
"search" = "Ara"
"filter" = "Filtrele"
"loading" = "Yükleniyor..."
"second" = "Saniye"
"minute" = "Dakika"
"hour" = "Saat"
"day" = "Gün"
"check" = "Kontrol Et"
"indefinite" = "Belirsiz"
"unlimited" = "Sınırsız"
"none" = "Hiçbiri"
"qrCode" = "QR Kod"
"info" = "Daha Fazla Bilgi"
"edit" = "Düzenle"
"delete" = "Sil"
"reset" = "Sıfırla"
"noData" = "Veri yok."
"copySuccess" = "Başarıyla Kopyalandı"
"sure" = "Emin misiniz"
"encryption" = "Şifreleme"
"useIPv4ForHost" = "Ana bilgisayar için IPv4 kullan"
"transmission" = "İletim"
"host" = "Sunucu"
"path" = "Yol"
"camouflage" = "Kandırma"
"status" = "Durum"
"enabled" = "Etkin"
"disabled" = "Devre Dışı"
"depleted" = "Bitti"
"depletingSoon" = "Bitmek Üzere"
"offline" = "Çevrimdışı"
"online" = "Çevrimiçi"
"domainName" = "Alan Adı"
"monitor" = "Dinleme IP"
"certificate" = "Dijital Sertifika"
"fail" = "Başarısız"
"comment" = "Yorum"
"success" = "Başarılı"
"getVersion" = "Sürümü Al"
"install" = "Yükle"
"clients" = "Müşteriler"
"usage" = "Kullanım"
"twoFactorCode" = "Kod"
"remained" = "Kalan"
"security" = "Güvenlik"
"secAlertTitle" = "Güvenlik Uyarısı"
"secAlertSsl" = "Bu bağlantı güvenli değil. Verilerin korunması için TLS etkinleştirilene kadar hassas bilgiler girmekten kaçının."
"secAlertConf" = "Bazı ayarlar saldırılara açıktır. Olası ihlalleri önlemek için güvenlik protokollerini güçlendirmeniz önerilir."
"secAlertSSL" = "Panelde güvenli bağlantı yok. Verilerin korunması için TLS sertifikası yükleyin."
"secAlertPanelPort" = "Panel varsayılan portu savunmasız. Rastgele veya belirli bir port yapılandırın."
"secAlertPanelURI" = "Panel varsayılan URI yolu güvensiz. Karmaşık bir URI yolu yapılandırın."
"secAlertSubURI" = "Abonelik varsayılan URI yolu güvensiz. Karmaşık bir URI yolu yapılandırın."
"secAlertSubJsonURI" = "Abonelik JSON varsayılan URI yolu güvensiz. Karmaşık bir URI yolu yapılandırın."
"emptyDnsDesc" = "Eklenmiş DNS sunucusu yok."
"emptyFakeDnsDesc" = "Eklenmiş Fake DNS sunucusu yok."
"emptyBalancersDesc" = "Eklenmiş dengeleyici yok."
"emptyReverseDesc" = "Eklenmiş ters proxy yok."
"somethingWentWrong" = "Bir şeyler yanlış gitti"

[menu]
"theme" = "Tema"
"dark" = "Koyu"
"ultraDark" = "Ultra Koyu"
"dashboard" = "Genel Bakış"
"inbounds" = "Gelenler"
"settings" = "Panel Ayarları"
"xray" = "Xray Yapılandırmaları"
"logout" = "Çıkış Yap"
"link" = "Yönet"

[pages.login]
"hello" = "Merhaba"
"title" = "Hoş Geldiniz"
"loginAgain" = "Oturum süreniz doldu, lütfen tekrar giriş yapın"

[pages.login.toasts]
"invalidFormData" = "Girdi verisi formatı geçersiz."
"emptyUsername" = "Kullanıcı adı gerekli"
"emptyPassword" = "Şifre gerekli"
"wrongUsernameOrPassword" = "Geçersiz kullanıcı adı, şifre veya iki adımlı doğrulama kodu."  
"successLogin" = "Hesabınıza başarıyla giriş yaptınız."

[pages.index]
"title" = "Genel Bakış"
"cpu" = "İşlemci"
"logicalProcessors" = "Mantıksal işlemciler"
"frequency" = "Frekans"
"swap" = "Takas"
"storage" = "Depolama"
"memory" = "RAM"
"threads" = "İş parçacıkları"
"xrayStatus" = "Xray"
"stopXray" = "Durdur"
"restartXray" = "Yeniden Başlat"
"xraySwitch" = "Sürüm"
"xraySwitchClick" = "Geçiş yapmak istediğiniz sürümü seçin."
"xraySwitchClickDesk" = "Dikkatli seçin, eski sürümler mevcut yapılandırmalarla uyumlu olmayabilir."
"xrayStatusUnknown" = "Bilinmiyor"
"xrayStatusRunning" = "Çalışıyor"
"xrayStatusStop" = "Durduruldu"
"xrayStatusError" = "Hata"
"xrayErrorPopoverTitle" = "Xray çalıştırılırken bir hata oluştu"
"operationHours" = "Çalışma Süresi"
"systemLoad" = "Sistem Yükü"
"systemLoadDesc" = "Geçmiş 1, 5 ve 15 dakika için sistem yük ortalaması"
"connectionTcpCountDesc" = "Sistem genelinde toplam TCP bağlantıları"
"connectionUdpCountDesc" = "Sistem genelinde toplam UDP bağlantıları"
"connectionCount" = "Bağlantı İstatistikleri"
"ipAddresses" = "IP adresleri"
"toggleIpVisibility" = "IP görünürlüğünü değiştir"
"overallSpeed" = "Genel hız"
"upload" = "Yükleme"
"download" = "İndirme"
"totalData" = "Toplam veri"
"sent" = "Gönderilen"
"received" = "Alınan"
"documentation" = "Dokümantasyon"
"xraySwitchVersionDialog" = "Xray sürümünü gerçekten değiştirmek istiyor musunuz?"
"xraySwitchVersionDialogDesc" = "Bu işlem Xray sürümünü #version# olarak değiştirecektir."
"xraySwitchVersionPopover" = "Xray başarıyla güncellendi"
"geofileUpdateDialog" = "Geofile'ı gerçekten güncellemek istiyor musunuz?"
"geofileUpdateDialogDesc" = "Bu işlem #filename# dosyasını güncelleyecektir."
"geofileUpdatePopover" = "Geofile başarıyla güncellendi"
"dontRefresh" = "Kurulum devam ediyor, lütfen bu sayfayı yenilemeyin"
"logs" = "Günlükler"
"config" = "Yapılandırma"
"backup" = "Yedek"
"backupTitle" = "Veritabanı Yedekleme & Geri Yükleme"
"exportDatabase" = "Yedekle"
"exportDatabaseDesc" = "Mevcut veritabanınızın yedeğini içeren bir .db dosyasını cihazınıza indirmek için tıklayın."
"importDatabase" = "Geri Yükle"
"importDatabaseDesc" = "Cihazınızdan bir .db dosyası seçip yükleyerek veritabanınızı yedekten geri yüklemek için tıklayın."
"importDatabaseSuccess" = "Veritabanı başarıyla içe aktarıldı"
"importDatabaseError" = "Veritabanı içe aktarılırken bir hata oluştu"
"readDatabaseError" = "Veritabanı okunurken bir hata oluştu"
"getDatabaseError" = "Veritabanı alınırken bir hata oluştu"
"getConfigError" = "Yapılandırma dosyası alınırken bir hata oluştu"

[pages.inbounds]
"title" = "Gelenler"
"totalDownUp" = "Toplam Gönderilen/Alınan"
"totalUsage" = "Toplam Kullanım"
"inboundCount" = "Toplam Gelen"
"operate" = "Menü"
"enable" = "Etkin"
"remark" = "Açıklama"
"protocol" = "Protokol"
"port" = "Port"
"traffic" = "Trafik"
"details" = "Detaylar"
"transportConfig" = "Taşıma"
"expireDate" = "Süre"
"resetTraffic" = "Trafiği Sıfırla"
"addInbound" = "Gelen Ekle"
"generalActions" = "Genel Eylemler"
"autoRefresh" = "Otomatik yenileme"
"autoRefreshInterval" = "Aralık"
"modifyInbound" = "Geleni Düzenle"
"deleteInbound" = "Geleni Sil"
"deleteInboundContent" = "Geleni silmek istediğinizden emin misiniz?"
"deleteClient" = "Müşteriyi Sil"
"deleteClientContent" = "Müşteriyi silmek istediğinizden emin misiniz?"
"resetTrafficContent" = "Trafiği sıfırlamak istediğinizden emin misiniz?"
"inboundUpdateSuccess" = "Gelen bağlantı başarıyla güncellendi."
"inboundCreateSuccess" = "Gelen bağlantı başarıyla oluşturuldu."
"copyLink" = "URL'yi Kopyala"
"address" = "Adres"
"network" = "Ağ"
"destinationPort" = "Hedef Port"
"targetAddress" = "Hedef Adres"
"monitorDesc" = "Tüm IP'leri dinlemek için boş bırakın"
"meansNoLimit" = "= Sınırsız. (birim: GB)"
"totalFlow" = "Toplam Akış"
"leaveBlankToNeverExpire" = "Hiçbir zaman sona ermemesi için boş bırakın"
"noRecommendKeepDefault" = "Varsayılanı korumanız önerilir"
"certificatePath" = "Dosya Yolu"
"certificateContent" = "Dosya İçeriği"
"publicKey" = "Genel Anahtar"
"privatekey" = "Özel Anahtar"
"clickOnQRcode" = "Kopyalamak için QR Kodu Tıklayın"
"client" = "Müşteri"
"export" = "Tüm URL'leri Dışa Aktar"
"clone" = "Klonla"
"cloneInbound" = "Klonla"
"cloneInboundContent" = "Bu gelenin tüm ayarları, Port, Dinleme IP ve Müşteriler hariç, klona uygulanacaktır."
"cloneInboundOk" = "Klonla"
"resetAllTraffic" = "Tüm Gelen Trafiğini Sıfırla"
"resetAllTrafficTitle" = "Tüm Gelen Trafiğini Sıfırla"
"resetAllTrafficContent" = "Tüm gelenlerin trafiğini sıfırlamak istediğinizden emin misiniz?"
"resetInboundClientTraffics" = "Müşteri Trafiklerini Sıfırla"
"resetInboundClientTrafficTitle" = "Müşteri Trafiklerini Sıfırla"
"resetInboundClientTrafficContent" = "Bu gelenin müşterilerinin trafiğini sıfırlamak istediğinizden emin misiniz?"
"resetAllClientTraffics" = "Tüm Müşteri Trafiklerini Sıfırla"
"resetAllClientTrafficTitle" = "Tüm Müşteri Trafiklerini Sıfırla"
"resetAllClientTrafficContent" = "Tüm müşterilerin trafiğini sıfırlamak istediğinizden emin misiniz?"
"delDepletedClients" = "Bitmiş Müşterileri Sil"
"delDepletedClientsTitle" = "Bitmiş Müşterileri Sil"
"delDepletedClientsContent" = "Tüm bitmiş müşterileri silmek istediğinizden emin misiniz?"
"email" = "E-posta"
"emailDesc" = "Lütfen benzersiz bir e-posta adresi sağlayın."
"IPLimit" = "IP Limiti"
"IPLimitDesc" = "Sayının aşılması durumunda gelen devre dışı bırakılır. (0 = devre dışı)"
"IPLimitlog" = "IP Günlüğü"
"IPLimitlogDesc" = "IP geçmiş günlüğü. (devre dışı bırakıldıktan sonra gelini etkinleştirmek için günlüğü temizleyin)"
"IPLimitlogclear" = "Günlüğü Temizle"
"setDefaultCert" = "Panelden Sertifikayı Ayarla"
"telegramDesc" = "Lütfen Telegram Sohbet Kimliği sağlayın. (botta '/id' komutunu kullanın) veya (@userinfobot)"
"subscriptionDesc" = "Abonelik URL'inizi bulmak için 'Detaylar'a gidin. Ayrıca, aynı adı birden fazla müşteri için kullanabilirsiniz."
"info" = "Bilgi"
"same" = "Aynı"
"inboundData" = "Gelenin Verileri"
"exportInbound" = "Geleni Dışa Aktar"
"import" = "İçe Aktar"
"importInbound" = "Bir Gelen İçe Aktar"

[pages.client]
"add" = "Müşteri Ekle"
"edit" = "Müşteriyi Düzenle"
"submitAdd" = "Müşteri Ekle"
"submitEdit" = "Değişiklikleri Kaydet"
"clientCount" = "Müşteri Sayısı"
"bulk" = "Toplu Ekle"
"method" = "Yöntem"
"first" = "İlk"
"last" = "Son"
"prefix" = "Önek"
"postfix" = "Sonek"
"delayedStart" = "İlk Kullanımdan Sonra Başlat"
"expireDays" = "Süre"
"days" = "Gün"
"renew" = "Otomatik Yenile"
"renewDesc" = "Süresi dolduktan sonra otomatik yenileme. (0 = devre dışı)(birim: gün)"

[pages.inbounds.toasts]
"obtain" = "Elde Et"
"updateSuccess" = "Güncelleme başarılı oldu"
"logCleanSuccess" = "Günlük temizlendi"
"inboundsUpdateSuccess" = "Gelen bağlantılar başarıyla güncellendi"
"inboundUpdateSuccess" = "Gelen bağlantı başarıyla güncellendi"
"inboundCreateSuccess" = "Gelen bağlantı başarıyla oluşturuldu"
"inboundDeleteSuccess" = "Gelen bağlantı başarıyla silindi"
"inboundClientAddSuccess" = "Gelen bağlantı istemci(leri) eklendi"
"inboundClientDeleteSuccess" = "Gelen bağlantı istemcisi silindi"
"inboundClientUpdateSuccess" = "Gelen bağlantı istemcisi güncellendi"
"delDepletedClientsSuccess" = "Tüm tükenmiş istemciler silindi"
"resetAllClientTrafficSuccess" = "İstemcinin tüm trafiği sıfırlandı"
"resetAllTrafficSuccess" = "Tüm trafik sıfırlandı"
"resetInboundClientTrafficSuccess" = "Trafik sıfırlandı"
"trafficGetError" = "Trafik bilgisi alınırken hata oluştu"
"getNewX25519CertError" = "X25519 sertifikası alınırken hata oluştu."

[pages.inbounds.stream.general]
"request" = "İstek"
"response" = "Yanıt"
"name" = "Ad"
"value" = "Değer"

[pages.inbounds.stream.tcp]
"version" = "Sürüm"
"method" = "Yöntem"
"path" = "Yol"
"status" = "Durum"
"statusDescription" = "Durum Açıklaması"
"requestHeader" = "İstek Başlığı"
"responseHeader" = "Yanıt Başlığı"

[pages.settings]
"title" = "Panel Ayarları"
"save" = "Kaydet"
"infoDesc" = "Burada yapılan her değişikliğin kaydedilmesi gerekir. Değişikliklerin uygulanması için paneli yeniden başlatın."
"restartPanel" = "Paneli Yeniden Başlat"
"restartPanelDesc" = "Paneli yeniden başlatmak istediğinizden emin misiniz? Yeniden başlattıktan sonra panele erişemezseniz, sunucudaki panel günlük bilgilerini görüntüleyin."
"restartPanelSuccess" = "Panel başarıyla yeniden başlatıldı"
"actions" = "Eylemler"
"resetDefaultConfig" = "Varsayılana Sıfırla"
"panelSettings" = "Genel"
"securitySettings" = "Kimlik Doğrulama"
"TGBotSettings" = "Telegram Bot"
"panelListeningIP" = "Dinleme IP"
"panelListeningIPDesc" = "Web paneli için IP adresi. (tüm IP'leri dinlemek için boş bırakın)"
"panelListeningDomain" = "Dinleme Alan Adı"
"panelListeningDomainDesc" = "Web paneli için alan adı. (tüm alan adlarını ve IP'leri dinlemek için boş bırakın)"
"panelPort" = "Dinleme Portu"
"panelPortDesc" = "Web paneli için port numarası. (kullanılmayan bir port olmalıdır)"
"publicKeyPath" = "Genel Anahtar Yolu"
"publicKeyPathDesc" = "Web paneli için genel anahtar dosya yolu. ('/' ile başlar)"
"privateKeyPath" = "Özel Anahtar Yolu"
"privateKeyPathDesc" = "Web paneli için özel anahtar dosya yolu. ('/' ile başlar)"
"panelUrlPath" = "URI Yolu"
"panelUrlPathDesc" = "Web paneli için URI yolu. ('/' ile başlar ve '/' ile biter)"
"pageSize" = "Sayfa Boyutu"
"pageSizeDesc" = "Gelenler tablosu için sayfa boyutunu belirleyin. (0 = devre dışı)"
"remarkModel" = "Açıklama Modeli & Ayırma Karakteri"
"datepicker" = "Takvim Türü"
"datepickerPlaceholder" = "Tarih Seçin"
"datepickerDescription" = "Planlanmış görevler bu takvime göre çalışacaktır."
"sampleRemark" = "Örnek Açıklama"
"oldUsername" = "Mevcut Kullanıcı Adı"
"currentPassword" = "Mevcut Şifre"
"newUsername" = "Yeni Kullanıcı Adı"
"newPassword" = "Yeni Şifre"
"telegramBotEnable" = "Telegram Botunu Etkinleştir"
"telegramBotEnableDesc" = "Telegram botunu etkinleştirir."
"telegramToken" = "Telegram Token"
"telegramTokenDesc" = "'@BotFather'dan alınan Telegram bot token."
"telegramProxy" = "SOCKS Proxy"
"telegramProxyDesc" = "Telegram'a bağlanmak için SOCKS5 proxy'sini etkinleştirir. (ayarları kılavuzda belirtilen şekilde ayarlayın)"
"telegramAPIServer" = "Telegram API Server"
"telegramAPIServerDesc" = "Kullanılacak Telegram API sunucusu. Varsayılan sunucuyu kullanmak için boş bırakın."
"telegramChatId" = "Yönetici Sohbet Kimliği"
"telegramChatIdDesc" = "Telegram Yönetici Sohbet Kimliği(leri). (virgülle ayrılmış)(buradan alın @userinfobot) veya (botta '/id' komutunu kullanın)"
"telegramNotifyTime" = "Bildirim Zamanı"
"telegramNotifyTimeDesc" = "Periyodik raporlar için ayarlanan Telegram bot bildirim zamanı. (crontab zaman formatını kullanın)"
"tgNotifyBackup" = "Veritabanı Yedeği"
"tgNotifyBackupDesc" = "Bir rapor ile birlikte veritabanı yedek dosyasını gönder."
"tgNotifyLogin" = "Giriş Bildirimi"
"tgNotifyLoginDesc" = "Birisi web panelinize giriş yapmaya çalıştığında kullanıcı adı, IP adresi ve zaman hakkında bildirim alın."
"sessionMaxAge" = "Oturum Süresi"
"sessionMaxAgeDesc" = "Giriş yaptıktan sonra oturum süresi. (birim: dakika)"
"expireTimeDiff" = "Son Kullanma Tarihi Bildirimi"
"expireTimeDiffDesc" = "Bu eşik seviyesine ulaşıldığında son kullanma tarihi hakkında bildirim alın. (birim: gün)"
"trafficDiff" = "Trafik Sınırı Bildirimi"
"trafficDiffDesc" = "Bu eşik seviyesine ulaşıldığında trafik sınırı hakkında bildirim alın. (birim: GB)"
"tgNotifyCpu" = "CPU Yükü Bildirimi"
"tgNotifyCpuDesc" = "CPU yükü bu eşik seviyesini aşarsa bildirim alın. (birim: %)"
"timeZone" = "Saat Dilimi"
"timeZoneDesc" = "Planlanmış görevler bu saat dilimine göre çalışacaktır."
"subSettings" = "Abonelik"
"subEnable" = "Abonelik Hizmetini Etkinleştir"
"subEnableDesc" = "Abonelik hizmetini etkinleştirir."
"subTitle" = "Abonelik Başlığı"
"subTitleDesc" = "VPN istemcisinde gösterilen başlık"
"subListen" = "Dinleme IP"
"subListenDesc" = "Abonelik hizmeti için IP adresi. (tüm IP'leri dinlemek için boş bırakın)"
"subPort" = "Dinleme Portu"
"subPortDesc" = "Abonelik hizmeti için port numarası. (kullanılmayan bir port olmalıdır)"
"subCertPath" = "Genel Anahtar Yolu"
"subCertPathDesc" = "Abonelik hizmeti için genel anahtar dosya yolu. ('/' ile başlar)"
"subKeyPath" = "Özel Anahtar Yolu"
"subKeyPathDesc" = "Abonelik hizmeti için özel anahtar dosya yolu. ('/' ile başlar)"
"subPath" = "URI Yolu"
"subPathDesc" = "Abonelik hizmeti için URI yolu. ('/' ile başlar ve '/' ile biter)"
"subDomain" = "Dinleme Alan Adı"
"subDomainDesc" = "Abonelik hizmeti için alan adı. (tüm alan adlarını ve IP'leri dinlemek için boş bırakın)"
"subUpdates" = "Güncelleme Aralıkları"
"subUpdatesDesc" = "Müşteri uygulamalarındaki abonelik URL'sinin güncelleme aralıkları. (birim: saat)"
"subEncrypt" = "Şifrele"
"subEncryptDesc" = "Abonelik hizmetinin döndürülen içeriği Base64 ile şifrelenir."
"subShowInfo" = "Kullanım Bilgisini Göster"
"subShowInfoDesc" = "Kalan trafik ve tarih müşteri uygulamalarında görüntülenir."
"subURI" = "Ters Proxy URI"
"subURIDesc" = "Proxy arkasında kullanılacak abonelik URL'sinin URI yolu."
"externalTrafficInformEnable" = "Harici Trafik Bilgisi"
"externalTrafficInformEnableDesc" = "Her trafik güncellemesinde harici API'yi bilgilendirin."
"externalTrafficInformURI" = "Harici Trafik Bilgisi URI'si"
"externalTrafficInformURIDesc" = "Trafik güncellemeleri bu URI'ye gönderildi."
"fragment" = "Parçalama"
"fragmentDesc" = "TLS merhaba paketinin parçalanmasını etkinleştir."
"fragmentSett" = "Parçalama Ayarları"
"noisesDesc" = "Noises'i Etkinleştir."
"noisesSett" = "Noises Ayarları"
"mux" = "Mux"
"muxDesc" = "Kurulmuş bir veri akışında birden çok bağımsız veri akışını iletir."
"muxSett" = "Mux Ayarları"
"direct" = "Doğrudan Bağlantı"
"directDesc" = "Belirli bir ülkenin alan adları veya IP aralıkları ile doğrudan bağlantı kurar."
"notifications" = "Bildirimler"
"certs" = "Sertifikalar"
"externalTraffic" = "Harici Trafik"
"dateAndTime" = "Tarih ve Saat"
"proxyAndServer" = "Proxy ve Sunucu"
"intervals" = "Aralıklar"
"information" = "Bilgi"
"language" = "Dil"
"telegramBotLanguage" = "Telegram Bot Dili"

[pages.xray]
"title" = "Xray Yapılandırmaları"
"save" = "Kaydet"
"restart" = "Xray'i Yeniden Başlat"
"restartSuccess" = "Xray başarıyla yeniden başlatıldı"
"stopSuccess" = "Xray başarıyla durduruldu"
"restartError" = "Xray yeniden başlatılırken bir hata oluştu."
"stopError" = "Xray durdurulurken bir hata oluştu."
"basicTemplate" = "Temeller"
"advancedTemplate" = "Gelişmiş"
"generalConfigs" = "Genel"
"generalConfigsDesc" = "Bu seçenekler genel ayarlamaları belirler."
"logConfigs" = "Günlük"
"logConfigsDesc" = "Günlükler sunucunuzun verimliliğini etkileyebilir. Yalnızca ihtiyaç durumunda akıllıca etkinleştirmeniz önerilir"
"blockConfigsDesc" = "Bu seçenekler belirli istek protokolleri ve web siteleri temelinde trafiği engeller."
"basicRouting" = "Temel Yönlendirme"
"blockConnectionsConfigsDesc" = "Bu seçenekler belirli bir istenen ülkeye göre trafiği engelleyecektir."
"directConnectionsConfigsDesc" = "Doğrudan bağlantı, belirli bir trafiğin başka bir sunucu üzerinden yönlendirilmediğini sağlar."
"blockips" = "IP'leri Engelle"
"blockdomains" = "Alan Adlarını Engelle"
"directips" = "Doğrudan IP'ler"
"directdomains" = "Doğrudan Alan Adları"
"ipv4Routing" = "IPv4 Yönlendirme"
"ipv4RoutingDesc" = "Bu seçenekler belirli bir varış yerine IPv4 üzerinden trafiği yönlendirir."
"warpRouting" = "WARP Yönlendirme"
"warpRoutingDesc" = "Bu seçenekler belirli bir varış yerine WARP üzerinden trafiği yönlendirir."
"Template" = "Gelişmiş Xray Yapılandırma Şablonu"
"TemplateDesc" = "Nihai Xray yapılandırma dosyası bu şablona göre oluşturulacaktır."
"FreedomStrategy" = "Freedom Protokol Stratejisi"
"FreedomStrategyDesc" = "Freedom Protokolünde ağın çıkış stratejisini ayarlayın."
"RoutingStrategy" = "Genel Yönlendirme Stratejisi"
"RoutingStrategyDesc" = "Tüm istekleri çözmek için genel trafik yönlendirme stratejisini ayarlayın."
"Torrent" = "BitTorrent Protokolünü Engelle"
"Inbounds" = "Gelenler"
"InboundsDesc" = "Belirli müşterileri kabul eder."
"Outbounds" = "Gidenler"
"Balancers" = "Dengeler"
"OutboundsDesc" = "Giden trafiğin yolunu ayarlayın."
"Routings" = "Yönlendirme Kuralları"
"RoutingsDesc" = "Her kuralın önceliği önemlidir!"
"completeTemplate" = "Tümü"
"logLevel" = "Günlük Seviyesi"
"logLevelDesc" = "Hata günlükleri için günlük seviyesi, kaydedilmesi gereken bilgileri belirtir."
"accessLog" = "Erişim Günlüğü"
"accessLogDesc" = "Erişim günlüğü için dosya yolu. 'none' özel değeri erişim günlüklerini devre dışı bırakır"
"errorLog" = "Hata Günlüğü"
"errorLogDesc" = "Hata günlüğü için dosya yolu. 'none' özel değeri hata günlüklerini devre dışı bırakır"
"dnsLog" = "DNS Günlüğü"
"dnsLogDesc" = "DNS sorgu günlüklerini etkinleştirin"
"maskAddress" = "Adres Maskesi"
"maskAddressDesc" = "IP adresi maskesi, etkinleştirildiğinde, günlükte görünen IP adresini otomatik olarak değiştirecektir."
"statistics" = "İstatistikler"
"statsInboundUplink" = "Gelen Yükleme İstatistikleri"
"statsInboundUplinkDesc" = "Tüm gelen proxy'lerin yükleme trafiği için istatistik toplamayı etkinleştirir."
"statsInboundDownlink" = "Gelen İndirme İstatistikleri"
"statsInboundDownlinkDesc" = "Tüm gelen proxy'lerin indirme trafiği için istatistik toplamayı etkinleştirir."
"statsOutboundUplink" = "Giden Yükleme İstatistikleri"
"statsOutboundUplinkDesc" = "Tüm giden proxy'lerin yükleme trafiği için istatistik toplamayı etkinleştirir."
"statsOutboundDownlink" = "Giden İndirme İstatistikleri"
"statsOutboundDownlinkDesc" = "Tüm giden proxy'lerin indirme trafiği için istatistik toplamayı etkinleştirir."

[pages.xray.rules]
"first" = "İlk"
"last" = "Son"
"up" = "Yukarı"
"down" = "Aşağı"
"source" = "Kaynak"
"dest" = "Hedef"
"inbound" = "Gelen"
"outbound" = "Giden"
"balancer" = "Dengeler"
"info" = "Bilgi"
"add" = "Kural Ekle"
"edit" = "Kuralı Düzenle"
"useComma" = "Virgülle ayrılmış öğeler"

[pages.xray.outbound]
"addOutbound" = "Giden Ekle"
"addReverse" = "Ters Ekle"
"editOutbound" = "Gideni Düzenle"
"editReverse" = "Tersi Düzenle"
"tag" = "Etiket"
"tagDesc" = "Benzersiz Etiket"
"address" = "Adres"
"reverse" = "Ters"
"domain" = "Alan Adı"
"type" = "Tür"
"bridge" = "Köprü"
"portal" = "Portal"
"link" = "Bağlantı"
"intercon" = "Bağlantı"
"settings" = "Ayarlar"
"accountInfo" = "Hesap Bilgileri"
"outboundStatus" = "Giden Durumu"
"sendThrough" = "Üzerinden Gönder"

[pages.xray.balancer]
"addBalancer" = "Dengeleyici Ekle"
"editBalancer" = "Dengeleyiciyi Düzenle"
"balancerStrategy" = "Strateji"
"balancerSelectors" = "Seçiciler"
"tag" = "Etiket"
"tagDesc" = "Benzersiz Etiket"
"balancerDesc" = "Dengeleyici Etiketi ve Giden Etiketi aynı anda kullanılamaz. Aynı anda kullanıldığında yalnızca giden etiketi çalışır."

[pages.xray.wireguard]
"secretKey" = "Gizli Anahtar"
"publicKey" = "Genel Anahtar"
"allowedIPs" = "İzin Verilen IP'ler"
"endpoint" = "Uç Nokta"
"psk" = "Ön Paylaşılan Anahtar"
"domainStrategy" = "Alan Adı Stratejisi"

[pages.xray.dns]
"enable" = "DNS'yi Etkinleştir"
"enableDesc" = "Dahili DNS sunucusunu etkinleştir"
"tag" = "DNS Gelen Etiketi"
"tagDesc" = "Bu etiket, yönlendirme kurallarında Gelen etiketi olarak kullanılabilir."
"clientIp" = "İstemci IP"
"clientIpDesc" = "DNS sorguları sırasında belirtilen IP konumunu sunucuya bildirmek için kullanılır"
"disableCache" = "Önbelleği devre dışı bırak"
"disableCacheDesc" = "DNS önbelleğini devre dışı bırakır"
"disableFallback" = "Yedeklemeyi devre dışı bırak"
"disableFallbackDesc" = "Yedek DNS sorgularını devre dışı bırakır"
"disableFallbackIfMatch" = "Eşleşirse Yedeklemeyi Devre Dışı Bırak"
"disableFallbackIfMatchDesc" = "DNS sunucusunun eşleşen alan adı listesi vurulduğunda yedek DNS sorgularını devre dışı bırakır"
"strategy" = "Sorgu Stratejisi"
"strategyDesc" = "Alan adlarını çözmek için genel strateji"
"add" = "Sunucu Ekle"
"edit" = "Sunucuyu Düzenle"
"domains" = "Alan Adları"
"expectIPs" = "Beklenen IP'ler"
"unexpectIPs" = "Beklenmeyen IP'ler"
"useSystemHosts" = "Sistem Hosts'larını Kullan"
"useSystemHostsDesc" = "Yüklü bir sistemden hosts dosyasını kullan"
"usePreset" = "Şablon kullan"
"dnsPresetTitle" = "DNS Şablonları"
"dnsPresetFamily" = "Aile"

[pages.xray.fakedns]
"add" = "Sahte DNS Ekle"
"edit" = "Sahte DNS'i Düzenle"
"ipPool" = "IP Havuzu Alt Ağı"
"poolSize" = "Havuz Boyutu"

[pages.settings.security]
"admin" = "Yönetici kimlik bilgileri"
"twoFactor" = "İki adımlı doğrulama"  
"twoFactorEnable" = "2FA'yı Etkinleştir"  
"twoFactorEnableDesc" = "Daha fazla güvenlik için ek bir doğrulama katmanı ekler."  
"twoFactorModalSetTitle" = "İki adımlı doğrulamayı etkinleştir"
"twoFactorModalDeleteTitle" = "İki adımlı doğrulamayı devre dışı bırak"
"twoFactorModalSteps" = "İki adımlı doğrulamayı ayarlamak için şu adımları izleyin:"
"twoFactorModalFirstStep" = "1. Bu QR kodunu doğrulama uygulamasında tarayın veya QR kodunun yanındaki token'ı kopyalayıp uygulamaya yapıştırın"
"twoFactorModalSecondStep" = "2. Uygulamadaki kodu girin"
"twoFactorModalRemoveStep" = "İki adımlı doğrulamayı kaldırmak için uygulamadaki kodu girin."
"twoFactorModalChangeCredentialsTitle" = "Kimlik bilgilerini değiştir"
"twoFactorModalChangeCredentialsStep" = "Yönetici kimlik bilgilerini değiştirmek için uygulamadaki kodu girin."
"twoFactorModalSetSuccess" = "İki faktörlü kimlik doğrulama başarıyla kuruldu"
"twoFactorModalDeleteSuccess" = "İki faktörlü kimlik doğrulama başarıyla silindi"
"twoFactorModalError" = "Yanlış kod"

[pages.settings.toasts]
"modifySettings" = "Parametreler değiştirildi."
"getSettings" = "Parametreler alınırken bir hata oluştu."
"modifyUserError" = "Yönetici kimlik bilgileri değiştirilirken bir hata oluştu."
"modifyUser" = "Yönetici kimlik bilgilerini başarıyla değiştirdiniz."
"originalUserPassIncorrect" = "Mevcut kullanıcı adı veya şifre geçersiz"
"userPassMustBeNotEmpty" = "Yeni kullanıcı adı ve şifre boş olamaz"
"getOutboundTrafficError" = "Giden trafik alınırken hata"
"resetOutboundTrafficError" = "Giden trafik sıfırlanırken hata"

[tgbot]
"keyboardClosed" = "❌ Özel klavye kapalı!"
"noResult" = "❗ Sonuç yok!"
"noQuery" = "❌ Sorgu bulunamadı! Lütfen komutu tekrar kullanın!"
"wentWrong" = "❌ Bir şeyler yanlış gitti!"
"noIpRecord" = "❗ IP Kaydı yok!"
"noInbounds" = "❗ Gelen bulunamadı!"
"unlimited" = "♾ Sınırsız(Sıfırla)"
"add" = "Ekle"
"month" = "Ay"
"months" = "Aylar"
"day" = "Gün"
"days" = "Günler"
"hours" = "Saatler"
"unknown" = "Bilinmiyor"
"inbounds" = "Gelenler"
"clients" = "Müşteriler"
"offline" = "🔴 Çevrimdışı"
"online" = "🟢 Çevrimiçi"

[tgbot.commands]
"unknown" = "❗ Bilinmeyen komut."
"pleaseChoose" = "👇 Lütfen seçin:\r\n"
"help" = "🤖 Bu bota hoş geldiniz! Web panelinden belirli verileri sunmak ve gerektiğinde değişiklik yapmanıza olanak tanımak için tasarlanmıştır.\r\n\r\n"
"start" = "👋 Merhaba <i>{{ .Firstname }}</i>.\r\n"
"welcome" = "🤖 <b>{{ .Hostname }}</b> yönetim botuna hoş geldiniz.\r\n"
"status" = "✅ Bot çalışıyor!"
"usage" = "❗ Lütfen aramak için bir metin sağlayın!"
"getID" = "🆔 Kimliğiniz: <code>{{ .ID }}</code>"
"helpAdminCommands" = "Xray Core'u yeniden başlatmak için:\r\n<code>/restart</code>\r\n\r\nBir müşteri e-postasını aramak için:\r\n<code>/usage [E-posta]</code>\r\n\r\nGelenleri aramak için (müşteri istatistikleri ile):\r\n<code>/inbound [Açıklama]</code>\r\n\r\nTelegram Sohbet Kimliği:\r\n<code>/id</code>"
"helpClientCommands" = "İstatistikleri aramak için şu komutu kullanın:\r\n\r\n<code>/usage [E-posta]</code>\r\n\r\nTelegram Sohbet Kimliği:\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ İşlem başarılı!"
"restartFailed" = "❗ İşlem hatası.\r\n\r\n<code>Hata: {{ .Error }}</code>."
"xrayNotRunning" = "❗ Xray Core çalışmıyor."
"startDesc" = "Ana menüyü göster"
"helpDesc" = "Bot yardımı"
"statusDesc" = "Bot durumunu kontrol et"
"idDesc" = "Telegram ID'nizi göster"

[tgbot.messages]
"cpuThreshold" = "🔴 CPU Yükü {{ .Percent }}% eşiği {{ .Threshold }}%'yi aşıyor"
"selectUserFailed" = "❌ Kullanıcı seçiminde hata!"
"userSaved" = "✅ Telegram Kullanıcısı kaydedildi."
"loginSuccess" = "✅ Panele başarıyla giriş yapıldı.\r\n"
"loginFailed" = "❗️Panele giriş denemesi başarısız oldu.\r\n"
"report" = "🕰 Planlanmış Raporlar: {{ .RunTime }}\r\n"
"datetime" = "⏰ Tarih&Zaman: {{ .DateTime }}\r\n"
"hostname" = "💻 Sunucu: {{ .Hostname }}\r\n"
"version" = "🚀 3X-UI Sürümü: {{ .Version }}\r\n"
"xrayVersion" = "📡 Xray Sürümü: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6: {{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4: {{ .IPv4 }}\r\n"
"ip" = "🌐 IP: {{ .IP }}\r\n"
"ips" = "🔢 IP'ler:\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ Çalışma Süresi: {{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 Sistem Yükü: {{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 RAM: {{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 TCP: {{ .Count }}\r\n"
"udpCount" = "🔸 UDP: {{ .Count }}\r\n"
"traffic" = "🚦 Trafik: {{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ Durum: {{ .State }}\r\n"
"username" = "👤 Kullanıcı Adı: {{ .Username }}\r\n"
"password" = "👤 Şifre: {{ .Password }}\r\n"
"time" = "⏰ Zaman: {{ .Time }}\r\n"
"inbound" = "📍 Gelen: {{ .Remark }}\r\n"
"port" = "🔌 Port: {{ .Port }}\r\n"
"expire" = "📅 Son Kullanma Tarihi: {{ .Time }}\r\n"
"expireIn" = "📅 Sona Erecek: {{ .Time }}\r\n"
"active" = "💡 Aktif: {{ .Enable }}\r\n"
"enabled" = "🚨 Etkin: {{ .Enable }}\r\n"
"online" = "🌐 Bağlantı durumu: {{ .Status }}\r\n"
"email" = "📧 E-posta: {{ .Email }}\r\n"
"upload" = "🔼 Yükleme: ↑{{ .Upload }}\r\n"
"download" = "🔽 İndirme: ↓{{ .Download }}\r\n"
"total" = "📊 Toplam: ↑↓{{ .UpDown }} / {{ .Total }}\r\n"
"TGUser" = "👤 Telegram Kullanıcısı: {{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 Tükenmiş {{ .Type }}:\r\n"
"exhaustedCount" = "🚨 Tükenmiş {{ .Type }} sayısı:\r\n"
"onlinesCount" = "🌐 Çevrimiçi Müşteriler: {{ .Count }}\r\n"
"disabled" = "🛑 Devre Dışı: {{ .Disabled }}\r\n"
"depleteSoon" = "🔜 Yakında Tükenecek: {{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 Yedekleme Zamanı: {{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 Yenilendi: {{ .Time }}\r\n\r\n"
"yes" = "✅ Evet"
"no" = "❌ Hayır"

"received_id" = "🔑📥 Kimlik güncellendi."
"received_password" = "🔑📥 Şifre güncellendi."
"received_email" = "📧📥 E-posta güncellendi."
"received_comment" = "💬📥 Yorum güncellendi."
"id_prompt" = "🔑 Varsayılan Kimlik: {{ .ClientId }}\n\nKimliğinizi girin."
"pass_prompt" = "🔑 Varsayılan Şifre: {{ .ClientPassword }}\n\nŞifrenizi girin."
"email_prompt" = "📧 Varsayılan E-posta: {{ .ClientEmail }}\n\nE-postanızı girin."
"comment_prompt" = "💬 Varsayılan Yorum: {{ .ClientComment }}\n\nYorumunuzu girin."
"inbound_client_data_id" = "🔄 Giriş: {{ .InboundRemark }}\n\n🔑 Kimlik: {{ .ClientId }}\n📧 E-posta: {{ .ClientEmail }}\n📊 Trafik: {{ .ClientTraffic }}\n📅 Bitiş Tarihi: {{ .ClientExp }}\n🌐 IP Sınırı: {{ .IpLimit }}\n💬 Yorum: {{ .ClientComment }}\n\nArtık bu müşteriyi girişe ekleyebilirsin!"
"inbound_client_data_pass" = "🔄 Giriş: {{ .InboundRemark }}\n\n🔑 Şifre: {{ .ClientPass }}\n📧 E-posta: {{ .ClientEmail }}\n📊 Trafik: {{ .ClientTraffic }}\n📅 Bitiş Tarihi: {{ .ClientExp }}\n🌐 IP Sınırı: {{ .IpLimit }}\n💬 Yorum: {{ .ClientComment }}\n\nArtık bu müşteriyi girişe ekleyebilirsin!"
"cancel" = "❌ İşlem iptal edildi! \n\nİstediğiniz zaman /start ile yeniden başlayabilirsiniz. 🔄"
"error_add_client"  = "⚠️ Hata:\n\n {{ .error }}"
"using_default_value"  = "Tamam, varsayılan değeri kullanacağım. 😊"
"incorrect_input" ="Girdiğiniz değer geçerli değil.\nKelime öbekleri boşluk olmadan devam etmelidir.\nDoğru örnek: aaaaaa\nYanlış örnek: aaa aaa 🚫"
"AreYouSure" = "Emin misin? 🤔"
"SuccessResetTraffic" = "📧 E-posta: {{ .ClientEmail }}\n🏁 Sonuç: ✅ Başarılı"
"FailedResetTraffic" = "📧 E-posta: {{ .ClientEmail }}\n🏁 Sonuç: ❌ Başarısız \n\n🛠️ Hata: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 Tüm müşteriler için trafik sıfırlama işlemi tamamlandı."


[tgbot.buttons]
"closeKeyboard" = "❌ Klavyeyi Kapat"
"cancel" = "❌ İptal"
"cancelReset" = "❌ Sıfırlamayı İptal Et"
"cancelIpLimit" = "❌ IP Limitini İptal Et"
"confirmResetTraffic" = "✅ Trafiği Sıfırlamayı Onayla?"
"confirmClearIps" = "✅ IP'leri Temizlemeyi Onayla?"
"confirmRemoveTGUser" = "✅ Telegram Kullanıcısını Kaldırmayı Onayla?"
"confirmToggle" = "✅ Kullanıcıyı Etkinleştirme/Devre Dışı Bırakmayı Onayla?"
"dbBackup" = "Veritabanı Yedeği Al"
"serverUsage" = "Sunucu Kullanımı"
"getInbounds" = "Gelenleri Al"
"depleteSoon" = "Yakında Tükenecek"
"clientUsage" = "Kullanımı Al"
"onlines" = "Çevrimiçi Müşteriler"
"commands" = "Komutlar"
"refresh" = "🔄 Yenile"
"clearIPs" = "❌ IP'leri Temizle"
"removeTGUser" = "❌ Telegram Kullanıcısını Kaldır"
"selectTGUser" = "👤 Telegram Kullanıcısını Seç"
"selectOneTGUser" = "👤 Bir Telegram Kullanıcısını Seçin:"
"resetTraffic" = "📈 Trafiği Sıfırla"
"resetExpire" = "📅 Son Kullanma Tarihini Değiştir"
"ipLog" = "🔢 IP Günlüğü"
"ipLimit" = "🔢 IP Limiti"
"setTGUser" = "👤 Telegram Kullanıcısını Ayarla"
"toggle" = "🔘 Etkinleştir / Devre Dışı Bırak"
"custom" = "🔢 Özel"
"confirmNumber" = "✅ Onayla: {{ .Num }}"
"confirmNumberAdd" = "✅ Ekleme onayı: {{ .Num }}"
"limitTraffic" = "🚧 Trafik Sınırı"
"getBanLogs" = "Yasak Günlüklerini Al"
"allClients" = "Tüm Müşteriler"

"addClient" = "Müşteri Ekle"
"submitDisable" = "Devre Dışı Olarak Gönder ☑️"
"submitEnable" = "Etkin Olarak Gönder ✅"
"use_default" = "🏷️ Varsayılanı Kullan"
"change_id" = "⚙️🔑 Kimlik"
"change_password" = "⚙️🔑 Şifre"
"change_email" = "⚙️📧 E-posta"
"change_comment" = "⚙️💬 Yorum"
"ResetAllTraffics" = "Tüm Trafikleri Sıfırla"
"SortedTrafficUsageReport" = "Sıralı Trafik Kullanım Raporu"


[tgbot.answers]
"successfulOperation" = "✅ İşlem başarılı!"
"errorOperation" = "❗ İşlemde hata."
"getInboundsFailed" = "❌ Gelenler alınamadı."
"getClientsFailed" = "❌ Müşteriler alınamadı."
"canceled" = "❌ {{ .Email }}: İşlem iptal edildi."
"clientRefreshSuccess" = "✅ {{ .Email }}: Müşteri başarıyla yenilendi."
"IpRefreshSuccess" = "✅ {{ .Email }}: IP'ler başarıyla yenilendi."
"TGIdRefreshSuccess" = "✅ {{ .Email }}: Müşterinin Telegram Kullanıcısı başarıyla yenilendi."
"resetTrafficSuccess" = "✅ {{ .Email }}: Trafik başarıyla sıfırlandı."
"setTrafficLimitSuccess" = "✅ {{ .Email }}: Trafik limiti başarıyla kaydedildi."
"expireResetSuccess" = "✅ {{ .Email }}: Son kullanma günleri başarıyla sıfırlandı."
"resetIpSuccess" = "✅ {{ .Email }}: IP limiti {{ .Count }} başarıyla kaydedildi."
"clearIpSuccess" = "✅ {{ .Email }}: IP'ler başarıyla temizlendi."
"getIpLog" = "✅ {{ .Email }}: IP Günlüğü alındı."
"getUserInfo" = "✅ {{ .Email }}: Telegram Kullanıcı Bilgisi alındı."
"removedTGUserSuccess" = "✅ {{ .Email }}: Telegram Kullanıcısı başarıyla kaldırıldı."
"enableSuccess" = "✅ {{ .Email }}: Başarıyla etkinleştirildi."
"disableSuccess" = "✅ {{ .Email }}: Başarıyla devre dışı bırakıldı."
"askToAddUserId" = "Yapılandırmanız bulunamadı!\r\nLütfen yöneticinizden yapılandırmalarınıza Telegram ChatID'nizi eklemesini isteyin.\r\n\r\nKullanıcı ChatID'niz: <code>{{ .TgUserID }}</code>"
"chooseClient" = "Gelen {{ .Inbound }} için bir Müşteri Seçin"
"chooseInbound" = "Bir Gelen Seçin"
