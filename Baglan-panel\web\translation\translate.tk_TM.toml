"username" = "Ulanyjy ady"
"password" = "Açar sözi"
"login" = "Gir"
"confirm" = "Tassykla"
"cancel" = "Ýatyr"
"close" = "Ýap"
"create" = "Döret"
"update" = "Täzele"
"copy" = "Göçür"
"copied" = "Göçürildi"
"download" = "Ýükle"
"remark" = "Bellik"
"enable" = "Açyk"
"protocol" = "Protokol"
"search" = "Gözle"
"filter" = "Süzgüç"
"loading" = "Ýüklenýär..."
"second" = "Sekunt"
"minute" = "Minut"
"hour" = "Sagat"
"day" = "Gün"
"check" = "Barla"
"indefinite" = "Kesgitlenmedik"
"unlimited" = "Çäksiz"
"none" = "Hiç zat"
"qrCode" = "QR Kod"
"info" = "Maglumat"
"edit" = "Üýtget"
"delete" = "Poz"
"reset" = "Täzeden düz"
"noData" = "Maglumat ýok."
"copySuccess" = "Üstünlikli göçürildi"
"sure" = "Ynanýarsyňyzmy"
"encryption" = "Şifrlemek"
"useIPv4ForHost" = "Öý üçin IPv4 ulan"
"transmission" = "Geçiriş"
"host" = "Serwer"
"path" = "Ýol"
"camouflage" = "Gizlemek"
"status" = "Ýagdaý"
"enabled" = "Açyk"
"disabled" = "Ýapyk"
"depleted" = "Gutardy"
"depletingSoon" = "Gutarmak üzre"
"offline" = "Awtonom"
"online" = "Onlaýn"
"domainName" = "Domen ady"
"monitor" = "Diňleýiş IP"
"certificate" = "Sanly şahadatnama"
"fail" = "Şowsuz"
"comment" = "Teswir"
"success" = "Üstünlik"
"getVersion" = "Wersiýany al"
"install" = "Gur"
"clients" = "Müşderiler"
"usage" = "Ulanyş"
"twoFactorCode" = "Kod"
"remained" = "Galan"
"security" = "Howpsuzlyk"
"secAlertTitle" = "Howpsuzlyk duýduryşy"
"secAlertSsl" = "Bu baglanyşyk howpsuz däl. Maglumatlaryň goralmagy üçin TLS açylýança duýgur maglumatlary girmekden gaça duruň."
"secAlertConf" = "Käbir sazlamalar hüjümlere açyk. Bolup biljek bozulmalary öňüni almak üçin howpsuzlyk protokollaryny güýçlendirmegiňizi maslahat berýäris."
"secAlertSSL" = "Panelde howpsuz baglanyşyk ýok. Maglumatlaryň goralmagy üçin TLS şahadatnamasyny ýükläň."
"secAlertPanelPort" = "Panel deslapky porty goragsyz. Tötänleýin ýa-da belli bir port sazlaň."
"secAlertPanelURI" = "Panel deslapky URI ýoly howpsuz däl. Çylşyrymly URI ýolyny sazlaň."
"secAlertSubURI" = "Abuna deslapky URI ýoly howpsuz däl. Çylşyrymly URI ýolyny sazlaň."
"secAlertSubJsonURI" = "Abuna JSON deslapky URI ýoly howpsuz däl. Çylşyrymly URI ýolyny sazlaň."
"emptyDnsDesc" = "Goşulan DNS serweri ýok."
"emptyFakeDnsDesc" = "Goşulan galp DNS serweri ýok."
"emptyBalancersDesc" = "Goşulan deňagramlaýjy ýok."
"emptyReverseDesc" = "Goşulan ters proksi ýok."
"somethingWentWrong" = "Bir zat nädogry boldy"

[menu]
"theme" = "Tema"
"dark" = "Garaňky"
"ultraDark" = "Aşa garaňky"
"dashboard" = "Umumy syn"
"inbounds" = "Müşderiler"
"settings" = "Panel sazlamalary"
"xray" = "Xray sazlamalary"
"logout" = "Çyk"
"link" = "Dolandyr"

[pages.login]
"hello" = "Salam"
"title" = "Hoş geldiňiz"
"loginAgain" = "Sessiýanyň wagty gutardy, täzeden giriň"

[pages.login.toasts]
"invalidFormData" = "Giriş maglumatynyň formaty nädogry."
"emptyUsername" = "Ulanyjy ady gerek"
"emptyPassword" = "Açar sözi gerek"
"wrongUsernameOrPassword" = "Nädogry ulanyjy ady, açar sözi ýa-da iki ädimli tassyklama kody."
"successLogin" = "Hasabyňyza üstünlikli girdiňiz."

[pages.index]
"title" = "Umumy syn"
"cpu" = "Prosessor"
"logicalProcessors" = "Logiki prosessorlar"
"frequency" = "Ýygylyk"
"swap" = "Çalyşmak"
"storage" = "Saklamak"
"memory" = "RAM"
"threads" = "Sapaklar"
"xrayStatus" = "Xray"
"stopXray" = "Dur"
"restartXray" = "Täzeden başla"
"xraySwitch" = "Wersiýa"
"xraySwitchClick" = "Geçmek isleýän wersiýaňyzy saýlaň."
"xraySwitchClickDesk" = "Üns beriň, köne wersiýalar häzirki sazlamalar bilen gabat gelmezlik bolup biler."
"xrayStatusUnknown" = "Näbelli"
"xrayStatusRunning" = "Işleýär"
"xrayStatusStop" = "Durupdyr"
"xrayStatusError" = "Ýalňyşlyk"
"xrayErrorPopoverTitle" = "Xray işledilende ýalňyşlyk ýüze çykdy"
"operationHours" = "Işleýiş wagty"
"systemLoad" = "Ulgam ýüki"
"systemLoadDesc" = "Geçen 1, 5 we 15 minut üçin ulgam ýük ortaça"
"connectionTcpCountDesc" = "Ulgam boýunça jemi TCP baglanyşyklary"
"connectionUdpCountDesc" = "Ulgam boýunça jemi UDP baglanyşyklary"
"connectionCount" = "Baglanyşyk statistikasy"
"ipAddresses" = "IP salgylar"
"toggleIpVisibility" = "IP görünişini üýtget"
"overallSpeed" = "Umumy tizlik"
"upload" = "Ýüklemek"
"download" = "Ýüklemek"
"totalData" = "Jemi maglumat"
"sent" = "Iberilen"
"received" = "Alnan"
"documentation" = "Resminamalar"
"xraySwitchVersionDialog" = "Xray wersiýasyny hakykatdanam üýtgetmek isleýärsiňizmi?"
"xraySwitchVersionDialogDesc" = "Bu amal Xray wersiýasyny #version# edip üýtgeder."
"xraySwitchVersionPopover" = "Xray üstünlikli täzelendi"
"geofileUpdateDialog" = "Geofile-y hakykatdanam täzelemek isleýärsiňizmi?"
"geofileUpdateDialogDesc" = "Bu amal #filename# faýlyny täzeler."
"geofileUpdatePopover" = "Geofile üstünlikli täzelendi"
"dontRefresh" = "Gurnamak dowam edýär, bu sahypany täzelemeň"
"logs" = "Gündelikler"
"config" = "Sazlama"
"backup" = "Ätiýaçlyk"
"backupTitle" = "Maglumat bazasy ätiýaçlygy we dikeltmek"
"exportDatabase" = "Ätiýaçlyk"
"exportDatabaseDesc" = "Häzirki maglumat bazaňyzyň ätiýaçlygyny öz içine alýan .db faýlyny enjamyňyza ýüklemek üçin basyň."
"importDatabase" = "Dikelt"
"importDatabaseDesc" = "Enjamyňyzdan .db faýlyny saýlap ýükläp, maglumat bazaňyzy ätiýaçlykdan dikeltmek üçin basyň."
"importDatabaseSuccess" = "Maglumat bazasy üstünlikli import edildi"
"importDatabaseError" = "Maglumat bazasy import edilende ýalňyşlyk ýüze çykdy"
"readDatabaseError" = "Maglumat bazasy okalýanda ýalňyşlyk ýüze çykdy"
"getDatabaseError" = "Maglumat bazasy alynanda ýalňyşlyk ýüze çykdy"
"getConfigError" = "Sazlama faýly alynanda ýalňyşlyk ýüze çykdy"

[pages.inbounds]
"title" = "Müşderiler"
"totalDownUp" = "Jemi Iberilen/Alnan"
"totalUsage" = "Jemi ulanyş"
"inboundCount" = "Jemi Müşderi"
"operate" = "Menýu"
"enable" = "Açyk"
"remark" = "Bellik"
"protocol" = "Protokol"
"port" = "Port"
"traffic" = "Trafik"
"details" = "Jikme-jiklikler"
"transportConfig" = "Daşamak"
"expireDate" = "Möhlet"
"resetTraffic" = "Trafigi täzeden düz"
"addInbound" = "Täze Müşderiler goş"
"generalActions" = "Umumy hereketler"
"autoRefresh" = "Awtomatik täzelemek"
"autoRefreshInterval" = "Aralyk"
"modifyInbound" = "Müşderileri üýtget"
"deleteInbound" = "Müşderileri poz"
"deleteInboundContent" = "Müşderileri pozmak isleýärsiňizmi?"
"deleteClient" = "Müşderini poz"
"deleteClientContent" = "Müşderini pozmak isleýärsiňizmi?"
"resetTrafficContent" = "Trafigi täzeden düzmek isleýärsiňizmi?"
"inboundUpdateSuccess" = "Müşderiler baglanyşygy üstünlikli täzelendi."
"inboundCreateSuccess" = "Müşderiler baglanyşygy üstünlikli döredildi."
"copyLink" = "URL-ni göçür"
"address" = "Salgy"
"network" = "Tor"
"destinationPort" = "Maksat porty"
"targetAddress" = "Maksat salgysy"
"monitorDesc" = "Ähli IP-leri diňlemek üçin boş goýuň"
"meansNoLimit" = "= Çäksiz. (birlik: GB)"
"totalFlow" = "Jemi akym"
"leaveBlankToNeverExpire" = "Hiç wagt gutarmamagy üçin boş goýuň"
"noRecommendKeepDefault" = "Deslapky ýagdaýy saklamagy maslahat berýäris"
"certificatePath" = "Faýl ýoly"
"certificateContent" = "Faýl mazmuny"
"publicKey" = "Açyk açar"
"privatekey" = "Gizlin açar"
"clickOnQRcode" = "Göçürmek üçin QR kody basyň"
"client" = "Müşderi"
"export" = "Ähli URL-leri eksport et"
"clone" = "Klonla"
"cloneInbound" = "Klonla"
"cloneInboundContent" = "Bu müşderileriň ähli sazlamalary, Port, Diňleýiş IP we Müşderiler bolmasa, klona ulanylýar."
"cloneInboundOk" = "Klonla"