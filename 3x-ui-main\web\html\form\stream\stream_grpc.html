{{define "form/streamGRPC"}}
<a-form :colon="false" :label-col="{ md: {span:8} }" :wrapper-col="{ md: {span:14} }">
    <a-form-item label="Service Name">
        <a-input v-model.trim="inbound.stream.grpc.serviceName"></a-input>
    </a-form-item>
    <a-form-item label="Authority">
        <a-input v-model.trim="inbound.stream.grpc.authority"></a-input>
    </a-form-item>
    <a-form-item label="Multi Mode">
        <a-switch v-model="inbound.stream.grpc.multiMode"></a-switch>
    </a-form-item>
</a-form>
{{end}}
