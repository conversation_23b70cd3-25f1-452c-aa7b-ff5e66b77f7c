"username" = "Имя пользователя"
"password" = "Пароль"
"login" = "Войти"
"confirm" = "Подтвердить"
"cancel" = "Отмена"
"close" = "Закрыть"
"create" = "Создать"
"update" = "Обновить"
"copy" = "Копировать"
"copied" = "Скопировано"
"download" = "Скачать"
"remark" = "Примечание"
"enable" = "Включить"
"protocol" = "Протокол"
"search" = "Поиск"
"filter" = "Фильтр"
"loading" = "Загрузка..."
"second" = "Секунда"
"minute" = "Минута"
"hour" = "Час"
"day" = "День"
"check" = "Проверить"
"indefinite" = "Бесконечно"
"unlimited" = "Безлимит"
"none" = "Пусто"
"qrCode" = "QR-код"
"info" = "Информация"
"edit" = "Изменить"
"delete" = "Удалить"
"reset" = "Сбросить"
"noData" = "Нет данных."
"copySuccess" = "Скопировано"
"sure" = "Да"
"encryption" = "Шифрование"
"useIPv4ForHost" = "Использовать IPv4 для хоста"
"transmission" = "Транспорт"
"host" = "Хост"
"path" = "Путь"
"camouflage" = "Маскировка"
"status" = "Статус"
"enabled" = "Включено"
"disabled" = "Отключено"
"depleted" = "Исчерпано"
"depletingSoon" = "Почти исчерпано"
"offline" = "Офлайн"
"online" = "Онлайн"
"domainName" = "Домен"
"monitor" = "Мониторинг IP"
"certificate" = "SSL сертификат"
"fail" = "Ошибка"
"comment" = "Комментарий"
"success" = "Успешно"
"getVersion" = "Узнать версию"
"install" = "Установка"
"clients" = "Клиенты"
"usage" = "Использование"
"twoFactorCode" = "Код"
"remained" = "Остаток"
"security" = "Безопасность"
"secAlertTitle" = "Предупреждение системы безопасности"
"secAlertSsl" = "Это соединение не защищено. Пожалуйста, не вводите конфиденциальную информацию, пока не установите SSL сертификат для защиты соединения"
"secAlertConf" = "Некоторые настройки уязвимы для атак. Чтобы в будущем не было проблем, нужно усилить защиту."
"secAlertSSL" = "Ваше подключение к панели не защищено. Установите SSL сертификат для защиты данных."
"secAlertPanelPort" = "Порт панели по умолчанию небезопасен. Установите случайный или просто другой порт."
"secAlertPanelURI" = "Адрес панели по умолчанию небезопасен. Сделайте адрес сложным."
"secAlertSubURI" = "URI-адрес подписки по умолчанию небезопасен. Пожалуйста, настройте сложный URI-адрес."
"secAlertSubJsonURI" = "URI-адрес по умолчанию для JSON подписки небезопасен. Пожалуйста, настройте сложный URI-адрес."
"emptyDnsDesc" = "Нет добавленных DNS-серверов."
"emptyFakeDnsDesc" = "Нет добавленных Fake DNS-серверов."
"emptyBalancersDesc" = "Нет добавленных балансировщиков."
"emptyReverseDesc" = "Нет добавленных реверс-прокси."
"somethingWentWrong" = "Что-то пошло не так"

[menu]
"theme" = "Тема"
"dark" = "Темная"
"ultraDark" = "Очень темная"
"dashboard" = "Дашборд"
"inbounds" = "Инбаунды"
"settings" = "Настройки"
"xray" = "Настройки Xray"
"logout" = "Выход"
"link" = "Управление"

[pages.login]
"hello" = "Привет!"
"title" = "Добро пожаловать!"
"loginAgain" = "Сессия истекла. Войдите в систему снова"

[pages.login.toasts]
"invalidFormData" = "Недопустимый формат данных"
"emptyUsername" = "Введите имя пользователя"
"emptyPassword" = "Введите пароль"
"wrongUsernameOrPassword" = "Неверные данные учетной записи."
"successLogin" = "Вы успешно вошли в аккаунт"

[pages.index]
"title" = "Дашборд"
"cpu" = "ЦП"
"logicalProcessors" = "Логические процессоры"
"frequency" = "Частота"
"swap" = "Файл подкачки"
"storage" = "Диск"
"memory" = "ОЗУ"
"threads" = "Потоки"
"xrayStatus" = "Xray"
"stopXray" = "Остановить"
"restartXray" = "Перезапустить"
"xraySwitch" = "Выбор версии"
"xraySwitchClick" = "Выберите желаемую версию"
"xraySwitchClickDesk" = "Важно: старые версии могут не поддерживать текущие настройки"
"xrayStatusUnknown" = "Неизвестно"
"xrayStatusRunning" = "Запущен"
"xrayStatusStop" = "Остановлен"
"xrayStatusError" = "Ошибка"
"xrayErrorPopoverTitle" = "Ошибка при запуске Xray"
"operationHours" = "Время работы системы"
"systemLoad" = "Нагрузка на систему"
"systemLoadDesc" = "Средняя загрузка системы за последние 1, 5 и 15 минут"
"connectionTcpCountDesc" = "Общее количество подключений TCP по всем сетевым картам."
"connectionUdpCountDesc" = "Общее количество подключений UDP по всем сетевым картам."
"connectionCount" = "Количество соединений"
"ipAddresses" = "IP-адреса сервера"
"toggleIpVisibility" = "Переключить видимость IP-адресов сервера"
"overallSpeed" = "Общая скорость передачи трафика"
"upload" = "Отправка"
"download" = "Загрузка"
"totalData" = "Общий объем трафика"
"sent" = "Отправлено"
"received" = "Получено"
"documentation" = "Документация"
"xraySwitchVersionDialog" = "Переключить версию Xray"
"xraySwitchVersionDialogDesc" = "Вы точно хотите сменить версию Xray?"
"xraySwitchVersionPopover" = "Xray успешно обновлён"
"geofileUpdateDialog" = "Вы действительно хотите обновить геофайл?"
"geofileUpdateDialogDesc" = "Это обновит файл #filename#."
"geofileUpdatePopover" = "Геофайл успешно обновлён"
"dontRefresh" = "Установка в процессе. Не обновляйте страницу"
"logs" = "Журнал"
"config" = "Конфигурация"
"backup" = "Резервная копия"
"backupTitle" = "Резервная копия базы данных"
"exportDatabase" = "Экспорт базы данных"
"exportDatabaseDesc" = "Нажмите, чтобы скачать файл .db, содержащий резервную копию вашей текущей базы данных на ваше устройство."
"importDatabase" = "Импорт базы данных"
"importDatabaseDesc" = "Нажмите, чтобы выбрать и загрузить файл .db с вашего устройства для восстановления базы данных из резервной копии."
"importDatabaseSuccess" = "База данных успешно импортирована"
"importDatabaseError" = "Произошла ошибка при импорте базы данных"
"readDatabaseError" = "Произошла ошибка при чтении базы данных"
"getDatabaseError" = "Произошла ошибка при получении базы данных"
"getConfigError" = "Произошла ошибка при получении конфигурационного файла"

[pages.inbounds]
"title" = "Инбаунды"
"totalDownUp" = "Объем отправленного/полученного трафика"
"totalUsage" = "Всего трафика"
"inboundCount" = "Всего инбаундов"
"operate" = "Меню"
"enable" = "Включить"
"remark" = "Примечание"
"protocol" = "Протокол"
"port" = "Порт"
"traffic" = "Трафик"
"details" = "Подробнее"
"transportConfig" = "Транспорт"
"expireDate" = "Дата окончания"
"resetTraffic" = "Сброс трафика"
"addInbound" = "Создать инбаунд"
"generalActions" = "Общие действия"
"autoRefresh" = "Автообновление"
"autoRefreshInterval" = "Интервал"
"modifyInbound" = "Изменить инбаунд"
"deleteInbound" = "Удалить инбаунд"
"deleteInboundContent" = "Вы уверены, что хотите удалить инбаунд?"
"deleteClient" = "Удалить клиента"
"deleteClientContent" = "Вы уверены, что хотите удалить клиента?"
"resetTrafficContent" = "Вы уверены, что хотите сбросить трафик?"
"inboundUpdateSuccess" = "Инбаунд успешно обновлен."
"inboundCreateSuccess" = "Инбаунд успешно создан."
"copyLink" = "Копировать ссылку"
"address" = "Адрес"
"network" = "Сеть"
"destinationPort" = "Порт назначения"
"targetAddress" = "Целевой адрес"
"monitorDesc" = "Оставьте пустым для прослушивания всех IP-адресов"
"meansNoLimit" = "= Без ограничений (значение: ГБ)"
"totalFlow" = "Общий расход"
"leaveBlankToNeverExpire" = "Оставьте пустым, чтобы было бесконечным"
"noRecommendKeepDefault" = "Рекомендуется оставить настройки по умолчанию"
"certificatePath" = "Путь к сертификату"
"certificateContent" = "Содержимое сертификата"
"publicKey" = "Публичный ключ"
"privatekey" = "Приватный ключ"
"clickOnQRcode" = "Нажмите на QR-код, чтобы скопировать"
"client" = "Клиент"
"export" = "Экспорт ссылок"
"clone" = "Клонировать"
"cloneInbound" = "Клонировать"
"cloneInboundContent" = "Будут клонированы все настройки инбаундов, кроме списка клиентов, порта и IP-адреса прослушивания"
"cloneInboundOk" = "Клонировано"
"resetAllTraffic" = "Сброс трафика всех инбаундов"
"resetAllTrafficTitle" = "Сброс трафика всех инбаундов"
"resetAllTrafficContent" = "Вы уверены, что хотите сбросить трафик всех инбаундов?"
"resetInboundClientTraffics" = "Сброс трафика клиента"
"resetInboundClientTrafficTitle" = "Сброс трафика клиентов"
"resetInboundClientTrafficContent" = "Вы уверены, что хотите сбросить трафик для этих клиентов?"
"resetAllClientTraffics" = "Сброс трафика всех клиентов"
"resetAllClientTrafficTitle" = "Сброс трафика всех клиентов"
"resetAllClientTrafficContent" = "Вы уверены, что хотите сбросить трафик всех клиентов?"
"delDepletedClients" = "Удалить отключенных клиентов"
"delDepletedClientsTitle" = "Удаление отключенных клиентов"
"delDepletedClientsContent" = "Вы уверены, что хотите удалить всех отключенных клиентов?"
"email" = "Email"
"emailDesc" = "Пожалуйста, укажите уникальный Email"
"IPLimit" = "Лимит по количеству IP"
"IPLimitDesc" = "Ограничение количества одновременных подключений с разных IP(0 – отключить)"
"IPLimitlog" = "Лог IP-адресов"
"IPLimitlogDesc" = "Лог IP-адресов (перед включением лога IP-адресов, вы должны очистить лог)"
"IPLimitlogclear" = "Очистить лог"
"setDefaultCert" = "Установить сертификат панели"
"telegramDesc" = "Пожалуйста, укажите Chat ID Telegram. (используйте команду '/id' в боте) или (@userinfobot)"
"subscriptionDesc" = "Вы можете найти свою ссылку подписки в разделе 'Подробнее'"
"info" = "Информация"
"same" = "Тот же"
"inboundData" = "Данные инбаундов"
"exportInbound" = "Экспорт инбаундов"
"import" = "Импортировать"
"importInbound" = "Импорт инбаундов"

[pages.client]
"add" = "Создать клиента"
"edit" = "Редактировать клиента"
"submitAdd" = "Добавить"
"submitEdit" = "Сохранить"
"clientCount" = "Количество клиентов"
"bulk" = "Добавить несколько"
"method" = "Метод"
"first" = "Первый"
"last" = "Последний"
"prefix" = "Префикс"
"postfix" = "Постфикс"
"delayedStart" = "Начало использования"
"expireDays" = "Длительность"
"days" = "дней"
"renew" = "Автопродление"
"renewDesc" = "Автопродление после истечения срока действия. (0 = отключить)(единица: день)"

[pages.inbounds.toasts]
"obtain" = "Получить"
"updateSuccess" = "Обновление прошло успешно"
"logCleanSuccess" = "Лог был очищен"
"inboundsUpdateSuccess" = "Инбаунды успешно обновлены"
"inboundUpdateSuccess" = "Инбаунд успешно обновлено"
"inboundCreateSuccess" = "Инбаунд успешно создано"
"inboundDeleteSuccess" = "Инбаунд успешно удалено"
"inboundClientAddSuccess" = "Клиент(ы) инбаунда добавлен(ы)"
"inboundClientDeleteSuccess" = "Клиент инбаунда удалён"
"inboundClientUpdateSuccess" = "Клиент инбаунда обновлён"
"delDepletedClientsSuccess" = "Все исчерпанные клиенты удалены"
"resetAllClientTrafficSuccess" = "Весь трафик клиента сброшен"
"resetAllTrafficSuccess" = "Весь трафик сброшен"
"resetInboundClientTrafficSuccess" = "Трафик сброшен"
"trafficGetError" = "Ошибка получения данных о трафике"
"getNewX25519CertError" = "Ошибка при получении сертификата X25519."

[pages.inbounds.stream.general]
"request" = "Запрос"
"response" = "Ответ"
"name" = "Имя"
"value" = "Значение"

[pages.inbounds.stream.tcp]
"version" = "Версия"
"method" = "Метод"
"path" = "Путь"
"status" = "Статус"
"statusDescription" = "Описание статуса"
"requestHeader" = "Заголовок запроса"
"responseHeader" = "Заголовок ответа"

[pages.settings]
"title" = "Настройки"
"save" = "Сохранить"
"infoDesc" = "Каждое внесённое изменение должно быть сохранено. Пожалуйста, перезапустите панель, чтобы изменения вступили в силу."
"restartPanel" = "Перезапуск панели"
"restartPanelDesc" = "Вы уверены, что хотите перезапустить панель? Подтвердите, и перезапуск произойдёт через 3 секунды. Если панель будет недоступна, проверьте лог сервера"
"restartPanelSuccess" = "Панель успешно перезапущена"
"actions" = "Действия"
"resetDefaultConfig" = "Восстановить настройки по умолчанию"
"panelSettings" = "Панель"
"securitySettings" = "Учетная запись"
"TGBotSettings" = "Telegram"
"panelListeningIP" = "IP-адрес для управления панелью"
"panelListeningIPDesc" = "Оставьте пустым для подключения с любого IP"
"panelListeningDomain" = "Домен панели"
"panelListeningDomainDesc" = "По умолчанию оставьте пустым, чтобы подключаться с любых доменов и IP-адресов"
"panelPort" = "Порт панели"
"panelPortDesc" = "Порт, на котором работает панель"
"publicKeyPath" = "Путь к файлу публичного ключа сертификата панели"
"publicKeyPathDesc" = "Введите полный путь, начинающийся с '/'"
"privateKeyPath" = "Путь к файлу приватного ключа сертификата панели"
"privateKeyPathDesc" = "Введите полный путь, начинающийся с '/'"
"panelUrlPath" = "Корневой путь URL адреса панели"
"panelUrlPathDesc" = "Должен начинаться с '/' и заканчиваться '/'"
"pageSize" = "Размер нумерации страниц"
"pageSizeDesc" = "Определить размер страницы для таблицы инбаундов. Установите 0, чтобы отключить"
"remarkModel" = "Модель примечания и символ разделения"
"datepicker" = "Выбор даты"
"datepickerPlaceholder" = "Выберите дату"
"datepickerDescription" = "Запланированные задачи будут выполняться в выбранное время"
"sampleRemark" = "Пример примечания"
"oldUsername" = "Текущий логин"
"currentPassword" = "Текущий пароль"
"newUsername" = "Новый логин"
"newPassword" = "Новый пароль"
"telegramBotEnable" = "Включить Telegram бота"
"telegramBotEnableDesc" = "Доступ к функциям панели через Telegram-бота"
"telegramToken" = "Токен Telegram бота"
"telegramTokenDesc" = "Необходимо получить токен у менеджера ботов Telegram @botfather"
"telegramProxy" = "Прокси Socks5"
"telegramProxyDesc" = "Если для подключения к Telegram вам нужен прокси Socks5, настройте его параметры согласно руководству."
"telegramAPIServer" = "API-сервер Telegram"
"telegramAPIServerDesc" = "Используемый API-сервер Telegram. Оставьте пустым, чтобы использовать сервер по умолчанию."
"telegramChatId" = "User ID администратора бота"
"telegramChatIdDesc" = "Один или несколько User ID администратора(-ов) Telegram-бота. Для получения User ID используйте @userinfobot или команду '/id' в боте."
"telegramNotifyTime" = "Частота уведомлений для администраторов от бота"
"telegramNotifyTimeDesc" = "Укажите интервал уведомлений в формате Crontab"
"tgNotifyBackup" = "Резервное копирование базы данных"
"tgNotifyBackupDesc" = "Отправлять уведомление с файлом резервной копии базы данных"
"tgNotifyLogin" = "Уведомление о входе"
"tgNotifyLoginDesc" = "Отображает имя пользователя, IP-адрес и время, когда кто-то пытается войти в вашу панель."
"sessionMaxAge" = "Продолжительность сессии"
"sessionMaxAgeDesc" = "Продолжительность сессии в системе (значение: минута)"
"expireTimeDiff" = "Задержка уведомления об истечении сессии"
"expireTimeDiffDesc" = "Получение уведомления об истечении срока действия сессии до достижения порогового значения (значение: день)"
"trafficDiff" = "Порог трафика для уведомления"
"trafficDiffDesc" = "Получение уведомления об исчерпании трафика до достижения порога (значение: ГБ)"
"tgNotifyCpu" = "Порог нагрузки на ЦП для уведомления"
"tgNotifyCpuDesc" = "Уведомление администраторов в Telegram, если нагрузка на ЦП превышает этот порог (значение: %)"
"timeZone" = "Часовой пояс"
"timeZoneDesc" = "Запланированные задачи выполняются в соответствии со временем в этом часовом поясе"
"subSettings" = "Подписка"
"subEnable" = "Включить подписку"
"subEnableDesc" = "Функция подписки с отдельной конфигурацией"
"subTitle" = "Заголовок подписки"
"subTitleDesc" = "Название подписки, которое видит клиент в VPN клиенте"
"subListen" = "Прослушивание IP"
"subListenDesc" = "Оставьте пустым по умолчанию, чтобы отслеживать все IP-адреса"
"subPort" = "Порт подписки"
"subPortDesc" = "Номер порта для обслуживания службы подписки не должен использоваться на сервере"
"subCertPath" = "Путь к файлу публичного ключа сертификата подписки"
"subCertPathDesc" = "Введите полный путь, начинающийся с '/'"
"subKeyPath" = "Путь к файлу приватного ключа сертификата подписки"
"subKeyPathDesc" = "Введите полный путь, начинающийся с '/'"
"subPath" = "Корневой путь URL-адреса подписки"
"subPathDesc" = "Должен начинаться с '/' и заканчиваться на '/'"
"subDomain" = "Домен прослушивания"
"subDomainDesc" = "Оставьте пустым по умолчанию, чтобы слушать все домены и IP-адреса"
"subUpdates" = "Интервалы обновления подписки"
"subUpdatesDesc" = "Интервал между обновлениями в клиентском приложении (в часах)"
"subEncrypt" = "Шифровать конфиги"
"subEncryptDesc" = "Шифровать возвращенные конфиги в подписке"
"subShowInfo" = "Показать информацию об использовании"
"subShowInfoDesc" = "Отображать остаток трафика и дату окончания после имени конфигурации"
"subURI" = "URI обратного прокси"
"subURIDesc" = "Изменить базовый URI URL-адреса подписки для использования за прокси-серверами"
"externalTrafficInformEnable" = "Информация о внешнем трафике"
"externalTrafficInformEnableDesc" = "Информировать внешний API о каждом обновлении трафика"
"externalTrafficInformURI" = "URI информации о внешнем трафике"
"externalTrafficInformURIDesc" = "Обновления трафика отправляются на этот URI"
"fragment" = "Фрагментация"
"fragmentDesc" = "Включить фрагментацию TLS-хэндшейка"
"fragmentSett" = "Настройки фрагментации"
"noisesDesc" = "Включить Noises."
"noisesSett" = "Настройки Noises"
"mux" = "Mux"
"muxDesc" = "Передача нескольких независимых потоков данных в одном соединении."
"muxSett" = "Настройки Mux"
"direct" = "Прямое подключение"
"directDesc" = "Устанавливает прямые соединения с доменами или IP-адресами определённой страны."
"notifications" = "Уведомления"
"certs" = "Сертификаты"
"externalTraffic" = "Внешний трафик"
"dateAndTime" = "Дата и время"
"proxyAndServer" = "Прокси и сервер"
"intervals" = "Интервалы"
"information" = "Информация"
"language" = "Язык интерфейса"
"telegramBotLanguage" = "Язык Telegram-бота"

[pages.xray]
"title" = "Настройки Xray"
"save" = "Сохранить"
"restart" = "Перезапуск Xray"
"restartSuccess" = "Xray успешно перезапущен"
"stopSuccess" = "Xray успешно остановлен"
"restartError" = "Произошла ошибка при перезапуске Xray."
"stopError" = "Произошла ошибка при остановке Xray."
"basicTemplate" = "Основное"
"advancedTemplate" = "Расширенный шаблон"
"generalConfigs" = "Основные настройки"
"generalConfigsDesc" = "Эти параметры описывают общие настройки"
"logConfigs" = "Логи"
"logConfigsDesc" = "Логи могут замедлять работу сервера. Включайте только нужные вам виды логов при необходимости!"
"blockConfigsDesc" = "Настройте, чтобы клиенты не имели доступа к определенным протоколам"
"basicRouting" = "Базовые соединения"
"blockConnectionsConfigsDesc" = "Эти параметры будут блокировать трафик в зависимости от страны назначения."
"directConnectionsConfigsDesc" = "Прямое соединение означает, что определенный трафик не будет перенаправлен через другой сервер."
"blockips" = "Заблокированные IP-адреса"
"blockdomains" = "Заблокированные домены"
"directips" = "Прямые IP-адреса"
"directdomains" = "Прямые домены"
"ipv4Routing" = "Правила IPv4"
"ipv4RoutingDesc" = "Эти параметры позволят клиентам маршрутизироваться к целевым доменам только через IPv4"
"warpRouting" = "Правила WARP"
"warpRoutingDesc" = " Эти опции будут направлять трафик в зависимости от конкретного пункта назначения через WARP."
"Template" = "Шаблон конфигурации Xray"
"TemplateDesc" = "На основе шаблона создаётся конфигурационный файл Xray."
"FreedomStrategy" = "Настройка стратегии протокола Freedom"
"FreedomStrategyDesc" = "Установка стратегии вывода сети в протоколе Freedom"
"RoutingStrategy" = "Настройка маршрутизации доменов"
"RoutingStrategyDesc" = "Установка общей стратегии маршрутизации разрешения DNS"
"Torrent" = "Заблокировать BitTorrent"
"Inbounds" = "Инбаунды"
"InboundsDesc" = "Изменение шаблона конфигурации для подключения определенных клиентов"
"Outbounds" = "Аутбаунды"
"Balancers" = "Балансировщик"
"OutboundsDesc" = "Изменение шаблона конфигурации, чтобы определить аутбаунды для этого сервера"
"Routings" = "Маршрутизация"
"RoutingsDesc" = "Важен приоритет каждого правила!"
"completeTemplate" = "Все"
"logLevel" = "Уровень логов"
"logLevelDesc" = "Уровень журнала для журналов ошибок, указывающий информацию, которую необходимо записать."
"accessLog" = "Логи доступа"
"accessLogDesc" = "Путь к файлу журнала доступа. Специальное значение «none» отключает логи доступа."
"errorLog" = "Логи ошибок"
"errorLogDesc" = "Путь к файлу логов ошибок. Специальное значение «none» отключает логи ошибок."
"dnsLog" = "Логи DNS"
"dnsLogDesc" = "Включить логи запросов DNS"
"maskAddress" = "Маскировка адреса"
"maskAddressDesc" = "При активации реальный IP-адрес заменяется на маскировочный в логах."
"statistics" = "Статистика"
"statsInboundUplink" = "Статистика входящего аплинка"
"statsInboundUplinkDesc" = "Включает сбор статистики для исходящего трафика всех входящих прокси."
"statsInboundDownlink" = "Статистика входящего даунлинка"
"statsInboundDownlinkDesc" = "Включает сбор статистики для входящего трафика всех входящих прокси."
"statsOutboundUplink" = "Статистика исходящего аплинка"
"statsOutboundUplinkDesc" = "Включает сбор статистики для исходящего трафика всех исходящих прокси."
"statsOutboundDownlink" = "Статистика исходящего даунлинка"
"statsOutboundDownlinkDesc" = "Включает сбор статистики для входящего трафика всех исходящих прокси."

[pages.xray.rules]
"first" = "Первый"
"last" = "Последний"
"up" = "Поднять вверх"
"down" = "Опустить вниз"
"source" = "Источник"
"dest" = "Пункт назначения"
"inbound" = "Инбаунд"
"outbound" = "Аутбаунд"
"balancer" = "Балансировщик"
"info" = "Информация"
"add" = "Создать правило"
"edit" = "Редактировать правило"
"useComma" = "Элементы, разделённые запятыми"

[pages.xray.outbound]
"addOutbound" = "Создать аутбаунд"
"addReverse" = "Создать реверс-прокси"
"editOutbound" = "Изменить аутбаунд"
"editReverse" = "Редактировать реверс-прокси"
"tag" = "Тег"
"tagDesc" = "Уникальный тег"
"address" = "Адрес"
"reverse" = "Реверс-прокси"
"domain" = "Домен"
"type" = "Тип"
"bridge" = "Мост"
"portal" = "Портал"
"link" = "Ссылка"
"intercon" = "Соединение"
"settings" = "Настройки"
"accountInfo" = "Информация об учетной записи"
"outboundStatus" = "Статус аутбаунда"
"sendThrough" = "Отправить через"

[pages.xray.balancer]
"addBalancer" = "Создать балансировщик"
"editBalancer" = "Редактировать балансировщик"
"balancerStrategy" = "Стратегия"
"balancerSelectors" = "Селекторы"
"tag" = "Тег"
"tagDesc" = "Уникальный тег"
"balancerDesc" = "Невозможно одновременно использовать balancerTag и outboundTag. При одновременном использовании будет работать только outboundTag."

[pages.xray.wireguard]
"secretKey" = "Секретный ключ"
"publicKey" = "Публичный ключ"
"allowedIPs" = "Разрешенные IP-адреса"
"endpoint" = "Конечная точка"
"psk" = "Общий ключ"
"domainStrategy" = "Стратегия домена"

[pages.xray.dns]
"enable" = "Включить DNS"
"enableDesc" = "Включить встроенный DNS-сервер"
"tag" = "Название тега DNS"
"tagDesc" = "Этот тег будет доступен как входящий тег в правилах маршрутизации."
"clientIp" = "IP клиента"
"clientIpDesc" = "Используется для уведомления сервера о указанном местоположении IP во время DNS-запросов"
"disableCache" = "Отключить кэш"
"disableCacheDesc" = "Отключает кэширование DNS"
"disableFallback" = "Отключить резервный DNS"
"disableFallbackDesc" = "Отключает резервные DNS-запросы"
"disableFallbackIfMatch" = "Отключить резервный DNS при совпадении"
"disableFallbackIfMatchDesc" = "Отключает резервные DNS-запросы при совпадении списка доменов DNS-сервера"
"strategy" = "Стратегия запроса"
"strategyDesc" = "Общая стратегия разрешения доменных имен"
"add" = "Создать DNS"
"edit" = "Редактировать DNS"
"domains" = "Домены"
"expectIPs" = "Ожидаемые IP"
"unexpectIPs" = "Неожидаемые IP"
"useSystemHosts" = "Использовать системные Hosts"
"useSystemHostsDesc" = "Использовать файл hosts из установленной системы"
"usePreset" = "Использовать шаблон"
"dnsPresetTitle" = "Шаблоны DNS"
"dnsPresetFamily" = "Семейный"

[pages.xray.fakedns]
"add" = "Создать Fake DNS"
"edit" = "Редактировать Fake DNS"
"ipPool" = "Подсеть пула IP"
"poolSize" = "Размер пула"

[pages.settings.security]
"admin" = "Учетные данные администратора"
"twoFactor" = "Двухфакторная аутентификация"
"twoFactorEnable" = "Включить 2FA"
"twoFactorEnableDesc" = "Добавляет дополнительный уровень аутентификации для повышения безопасности."
"twoFactorModalSetTitle" = "Включить двухфакторную аутентификацию"
"twoFactorModalDeleteTitle" = "Отключить двухфакторную аутентификацию"
"twoFactorModalSteps" = "Для настройки двухфакторной аутентификации выполните несколько шагов:"
"twoFactorModalFirstStep" = "1. Отсканируйте этот QR-код в приложении для аутентификации или скопируйте токен рядом с QR-кодом и вставьте его в приложение"
"twoFactorModalSecondStep" = "2. Введите код из приложения"
"twoFactorModalRemoveStep" = "Введите код из приложения, чтобы отключить двухфакторную аутентификацию."
"twoFactorModalChangeCredentialsTitle" = "Изменить учетные данные"
"twoFactorModalChangeCredentialsStep" = "Введите код из приложения, чтобы изменить учетные данные администратора."
"twoFactorModalSetSuccess" = "Двухфакторная аутентификация была успешно установлена"
"twoFactorModalDeleteSuccess" = "Двухфакторная аутентификация была успешно удалена"
"twoFactorModalError" = "Неверный код"

[pages.settings.toasts]
"modifySettings" = "Настройки изменены"
"getSettings" = "Произошла ошибка при получении параметров."
"modifyUserError" = "Произошла ошибка при изменении учетных данных администратора."
"modifyUser" = "Вы успешно изменили учетные данные администратора."
"originalUserPassIncorrect" = "Неверное имя пользователя или пароль"
"userPassMustBeNotEmpty" = "Новое имя пользователя и новый пароль должны быть заполнены"
"getOutboundTrafficError" = "Ошибка получения трафика аутбаунда"
"resetOutboundTrafficError" = "Ошибка сброса трафика аутбаунда"

[tgbot]
"keyboardClosed" = "❌ Клавиатура закрыта."
"noResult" = "❗ Нет результатов."
"noQuery" = "❌ Запрос не найден. Пожалуйста, повторите команду."
"wentWrong" = "❌ Что-то пошло не так..."
"noIpRecord" = "❗ Нет записей об IP-адресе."
"noInbounds" = "❗ У вас не настроено ни одного инбаунда."
"unlimited" = "♾ Безлимит"
"add" = "Добавить"
"month" = "Месяц"
"months" = "Месяцев"
"day" = "День"
"days" = "Дней"
"hours" = "Часов"
"unknown" = "Неизвестно"
"inbounds" = "Инбаунды"
"clients" = "Клиенты"
"offline" = "🔴 Офлайн"
"online" = "🟢 Онлайн"

[tgbot.commands]
"unknown" = "❗ Неизвестная команда"
"pleaseChoose" = "👇 Пожалуйста, выберите:\r\n"
"help" = "🤖 Добро пожаловать! Этот бот предназначен для предоставления вам данных с сервера и позволяет вносить изменения на него.\r\n\r\n"
"start" = "👋 Привет, <i>{{ .Firstname }}</i>.\r\n"
"welcome" = "🤖 Добро пожаловать в бота управления <b>{{ .Hostname }}</b>!\r\n"
"status" = "✅ Бот функционирует нормально."
"usage" = "❗ Пожалуйста, укажите email для поиска."
"getID" = "🆔 Ваш User ID: <code>{{ .ID }}</code>"
"helpAdminCommands" = "🔃 Для перезапуска Xray Core:\r\n<code>/restart</code>\r\n\r\n🔎 Для поиска клиента по email:\r\n<code>/usage [Email]</code>\r\n\r\n📊 Для поиска инбаундов (со статистикой клиентов):\r\n<code>/inbound [имя подключения]</code>\r\n\r\n🆔 Ваш Telegram User ID:\r\n<code>/id</code>"
"helpClientCommands" = "💲 Для просмотра информации о вашей подписке используйте команду:\r\n<code>/usage [Email]</code>\r\n\r\n🆔 Ваш Telegram User ID:\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ Ядро Xray успешно перезапущено."
"restartFailed" = "❗ Ошибка при перезапуске Xray-core.\r\n\r\n<code>Ошибка: {{ .Error }}</code>."
"xrayNotRunning" = "❗ Xray Core не запущен."
"startDesc" = "Показать главное меню"
"helpDesc" = "Справка по боту"
"statusDesc" = "Проверить статус бота"
"idDesc" = "Показать ваш Telegram ID"

[tgbot.messages]
"cpuThreshold" = "🔴 Загрузка процессора составляет {{ .Percent }}%, что превышает пороговое значение {{ .Threshold }}%"
"selectUserFailed" = "❌ Ошибка при выборе пользователя."
"userSaved" = "✅ Пользователь Telegram сохранен."
"loginSuccess" = "✅ Успешный вход в панель.\r\n"
"loginFailed" = "❗️ Ошибка входа в панель.\r\n"
"report" = "🕰 Запланированные отчеты: {{ .RunTime }}\r\n"
"datetime" = "⏰ Дата и время: {{ .DateTime }}\r\n"
"hostname" = "💻 Имя хоста: {{ .Hostname }}\r\n"
"version" = "🚀 Версия X-UI: {{ .Version }}\r\n"
"xrayVersion" = "📡 Версия Xray: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6: {{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4: {{ .IPv4 }}\r\n"
"ip" = "🌐 IP: {{ .IP }}\r\n"
"ips" = "🔢 IP-адреса:\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ Время работы сервера: {{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 Нагрузка сервера: {{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 Диск сервера: {{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 Количество TCP-соединений: {{ .Count }}\r\n"
"udpCount" = "🔸 Количество UDP-соединений: {{ .Count }}\r\n"
"traffic" = "🚦 Трафик: {{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ Состояние Xray: {{ .State }}\r\n"
"username" = "👤 Имя пользователя: {{ .Username }}\r\n"
"password" = "👤 Пароль: {{ .Password }}\r\n"
"time" = "⏰ Время: {{ .Time }}\r\n"
"inbound" = "📍 Входящий поток: {{ .Remark }}\r\n"
"port" = "🔌 Порт: {{ .Port }}\r\n"
"expire" = "📅 Дата окончания: {{ .Time }}\r\n"
"expireIn" = "📅 Окончание через: {{ .Time }}\r\n"
"active" = "💡 Активен: {{ .Enable }}\r\n"
"enabled" = "🚨 Активен: {{ .Enable }}\r\n"
"online" = "🌐 Статус соединения: {{ .Status }}\r\n"
"email" = "📧 Email: {{ .Email }}\r\n"
"upload" = "🔼 Исходящий трафик: ↑{{ .Upload }}\r\n"
"download" = "🔽 Входящий трафик: ↓{{ .Download }}\r\n"
"total" = "📊 Всего: ↑↓{{ .UpDown }} из {{ .Total }}\r\n"
"TGUser" = "👤 Telegram User ID: {{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 Исчерпаны {{ .Type }}:\r\n"
"exhaustedCount" = "🚨 Количество исчерпанных {{ .Type }}:\r\n"
"onlinesCount" = "🌐 Клиентов онлайн: {{ .Count }}\r\n"
"disabled" = "🛑 Отключено: {{ .Disabled }}\r\n"
"depleteSoon" = "🔜 Клиенты, у которых скоро исчерпание: {{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 Время резервного копирования: {{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 Обновлено: {{ .Time }}\r\n\r\n"
"yes" = "✅ Да"
"no" = "❌ Нет"

"received_id" = "🔑📥 ID обновлён."
"received_password" = "🔑📥 Пароль обновлён."
"received_email" = "📧📥 Email обновлен."
"received_comment" = "💬📥 Комментарий обновлён."
"id_prompt" = "🔑 Стандартный ID: {{ .ClientId }}\n\nВведите ваш ID."
"pass_prompt" = "🔑 Стандартный пароль: {{ .ClientPassword }}\n\nВведите ваш пароль."
"email_prompt" = "📧 Стандартный email: {{ .ClientEmail }}\n\nВведите ваш email."
"comment_prompt" = "💬 Стандартный комментарий: {{ .ClientComment }}\n\nВведите ваш комментарий."
"inbound_client_data_id" = "🔄 Инбаунды: {{ .InboundRemark }}\n\n🔑 ID: {{ .ClientId }}\n📧 Email: {{ .ClientEmail }}\n📊 Трафик: {{ .ClientTraffic }}\n📅 Дата исчерпания: {{ .ClientExp }}\n💬 Комментарий: {{ .ClientComment }}\n\nТеперь вы можете добавить клиента в инбаунд!"
"inbound_client_data_pass" = "🔄 Инбаунды: {{ .InboundRemark }}\n\n🔑 Пароль: {{ .ClientPass }}\n📧 Email: {{ .ClientEmail }}\n📊 Трафик: {{ .ClientTraffic }}\n📅 Дата исчерпания: {{ .ClientExp }}\n💬 Комментарий: {{ .ClientComment }}\n\nТеперь вы можете добавить клиента в инбаунд!"
"cancel" = "❌ Процесс отменён! \n\nВы можете снова начать с /start в любое время. 🔄"
"error_add_client"  = "⚠️ Ошибка:\n\n {{ .error }}"
"using_default_value"  = "Используется значение по умолчанию👌"
"incorrect_input" ="Ваш ввод недействителен.\nФразы должны быть непрерывными без пробелов.\nПравильный пример: aaaaaa\nНеправильный пример: aaa aaa 🚫"
"AreYouSure" = "Вы уверены? 🤔"
"SuccessResetTraffic" = "📧 Почта: {{ .ClientEmail }}\n🏁 Результат: ✅ Успешно"
"FailedResetTraffic" = "📧 Почта: {{ .ClientEmail }}\n🏁 Результат: ❌ Неудача \n\n🛠️ Ошибка: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 Сброс трафика завершён для всех клиентов."


[tgbot.buttons]
"closeKeyboard" = "❌ Закрыть клавиатуру"
"cancel" = "❌ Отмена"
"cancelReset" = "❌ Отменить сброс"
"cancelIpLimit" = "❌ Отменить лимит IP"
"confirmResetTraffic" = "✅ Подтвердить сброс трафика?"
"confirmClearIps" = "✅ Подтвердить очистку IP?"
"confirmRemoveTGUser" = "✅ Подтвердить удаление пользователя Telegram?"
"confirmToggle" = "✅ Подтвердить вкл/выкл пользователя?"
"dbBackup" = "📂 Бэкап БД"
"serverUsage" = "💻 Состояние сервера"
"getInbounds" = "🔌 Инбаунды"
"depleteSoon" = "⚠️ Скоро конец"
"clientUsage" = "Статистика клиента"
"onlines" = "🟢 Онлайн"
"commands" = "🖱️ Команды"
"refresh" = "🔄 Обновить"
"clearIPs" = "❌ Очистить IP"
"removeTGUser" = "❌ Удалить пользователя Telegram"
"selectTGUser" = "👤 Выбрать пользователя Telegram"
"selectOneTGUser" = "👤 Выберите пользователя Telegram:"
"resetTraffic" = "📈 Сбросить трафик"
"resetExpire" = "📅 Изменить дату окончания"
"ipLog" = "🔢 Лог IP"
"ipLimit" = "🔢 Лимит IP"
"setTGUser" = "👤 Установить пользователя Telegram"
"toggle" = "🔘 Вкл./Выкл."
"custom" = "🔢 Свой"
"confirmNumber" = "✅ Подтвердить: {{ .Num }}"
"confirmNumberAdd" = "✅ Подтвердить добавление: {{ .Num }}"
"limitTraffic" = "🚧 Лимит трафика"
"getBanLogs" = "📄 Лог банов"
"allClients" = "👥 Все клиенты"

"addClient" = "➕ Новый клиент"
"submitDisable" = "Добавить отключенным ☑️"
"submitEnable" = "Добавить включенныи ✅"
"use_default" = "🏷️ Использовать по умолчанию"
"change_id" = "⚙️🔑 ID"
"change_password" = "⚙️🔑 Пароль"
"change_email" = "⚙️📧 Email"
"change_comment" = "⚙️💬 Комментарий"
"ResetAllTraffics" = "Сбросить весь трафик"
"SortedTrafficUsageReport" = "Отсортированный отчет об использовании трафика"


[tgbot.answers]
"successfulOperation" = "✅ Успешно!"
"errorOperation" = "❗ Ошибка в операции."
"getInboundsFailed" = "❌ Не удалось получить инбаунды."
"getClientsFailed" = "❌ Не удалось получить клиентов."
"canceled" = "❌ {{ .Email }}: Операция отменена."
"clientRefreshSuccess" = "✅ {{ .Email }}: Клиент успешно обновлен."
"IpRefreshSuccess" = "✅ {{ .Email }}: IP-адреса успешно обновлены."
"TGIdRefreshSuccess" = "✅ {{ .Email }}: Пользователь Telegram клиента успешно обновлен."
"resetTrafficSuccess" = "✅ {{ .Email }}: Трафик успешно сброшен."
"setTrafficLimitSuccess" = "✅ {{ .Email }}: Лимит трафика успешно установлен."
"expireResetSuccess" = "✅ {{ .Email }}: Срок действия успешно сброшен."
"resetIpSuccess" = "✅ {{ .Email }}: Лимит IP ({{ .Count }}) успешно сохранен."
"clearIpSuccess" = "✅ {{ .Email }}: IP-адреса успешно очищены."
"getIpLog" = "✅ {{ .Email }}: Получен лог IP."
"getUserInfo" = "✅ {{ .Email }}: Получена информация о пользователе Telegram."
"removedTGUserSuccess" = "✅ {{ .Email }}: Пользователь Telegram успешно удален."
"enableSuccess" = "✅ {{ .Email }}: Включено успешно."
"disableSuccess" = "✅ {{ .Email }}: Отключено успешно."
"askToAddUserId" = "❌ Ваша конфигурация не найдена!\r\n💭 Пожалуйста, попросите администратора использовать ваш Telegram User ID в конфигурации.\r\n\r\n🆔 Ваш User ID: <code>{{ .TgUserID }}</code>"
"chooseClient" = "Выберите клиента для инбаунда {{ .Inbound }}"
"chooseInbound" = "Выберите инбаунд"
