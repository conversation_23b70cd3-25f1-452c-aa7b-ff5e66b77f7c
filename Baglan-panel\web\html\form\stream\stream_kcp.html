{{define "form/streamKCP"}}
<a-form :colon="false" :label-col="{ md: {span:8} }" :wrapper-col="{ md: {span:14} }">
    <a-form-item label='{{ i18n "camouflage" }}'>
        <a-select v-model="inbound.stream.kcp.type" :style="{ width: '50%' }" :dropdown-class-name="themeSwitcher.currentTheme">
            <a-select-option value="none">None</a-select-option>
            <a-select-option value="srtp">SRTP</a-select-option>
            <a-select-option value="utp">uTP</a-select-option>
            <a-select-option value="wechat-video">WeChat</a-select-option>
            <a-select-option value="dtls">DTLS 1.2</a-select-option>
            <a-select-option value="wireguard">WireGuard</a-select-option>
            <a-select-option value="dns">DNS</a-select-option>
        </a-select>
    </a-form-item>
    <a-form-item>
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    <span>{{ i18n "reset" }}</span>
                </template>
                {{ i18n "password" }}
                <a-icon @click="inbound.stream.kcp.seed = RandomUtil.randomSeq(10)"type="sync"> </a-icon>
            </a-tooltip>
        </template>
        <a-input v-model.trim="inbound.stream.kcp.seed"></a-input>
    </a-form-item>
    <a-form-item label='MTU'>
        <a-input-number v-model.number="inbound.stream.kcp.mtu" :min="576" :max="1460"></a-input-number>
    </a-form-item>
    <a-form-item label='TTI (ms)'>
        <a-input-number v-model.number="inbound.stream.kcp.tti" :min="10" :max="100"></a-input-number>
    </a-form-item>
    <a-form-item label='Uplink (MB/s)'>
        <a-input-number v-model.number="inbound.stream.kcp.upCap" :min="0"></a-input-number>
    </a-form-item> 
    <a-form-item label='Downlink (MB/s)'>
        <a-input-number v-model.number="inbound.stream.kcp.downCap" :min="0"></a-input-number>
    </a-form-item>
    <a-form-item label='Congestion'>
        <a-switch v-model="inbound.stream.kcp.congestion"></a-switch>
    </a-form-item>
    <a-form-item label='Read Buffer (MB)'>
        <a-input-number v-model.number="inbound.stream.kcp.readBuffer" :min="0"></a-input-number>
    </a-form-item>
    <a-form-item label='Write Buffer (MB)'>
        <a-input-number v-model.number="inbound.stream.kcp.writeBuffer" :min="0"></a-input-number>
    </a-form-item>
</a-form>
{{end}}
