"username" = "Tên người dùng"
"password" = "Mật khẩu"
"login" = "Đăng nhập"
"confirm" = "Xác nhận"
"cancel" = "Hủy bỏ"
"close" = "Đóng"
"create" = "Tạo"
"update" = "Cập nhật"
"copy" = "Sao chép"
"copied" = "Đã sao chép"
"download" = "Tải xuống"
"remark" = "Ghi chú"
"enable" = "Kích hoạt"
"protocol" = "Giao thức"
"search" = "Tìm kiếm"
"filter" = "Bộ lọc"
"loading" = "Đang tải"
"second" = "Giây"
"minute" = "Phút"
"hour" = "Giờ"
"day" = "Ngày"
"check" = "Kiểm tra"
"indefinite" = "Không xác định"
"unlimited" = "Không giới hạn"
"none" = "None"
"qrCode" = "Mã QR"
"info" = "Thông tin thêm"
"edit" = "Chỉnh sửa"
"delete" = "Xóa"
"reset" = "Đặt lại"
"noData" = "Không có dữ liệu."
"copySuccess" = "Đã sao chép thành công"
"sure" = "Chắc chắn"
"encryption" = "Mã hóa"
"useIPv4ForHost" = "Sử dụng IPv4 cho máy chủ"
"transmission" = "Truyền tải"
"host" = "Máy chủ"
"path" = "Đường dẫn"
"camouflage" = "Ngụy trang"
"status" = "Trạng thái"
"enabled" = "Đã kích hoạt"
"disabled" = "Đã tắt"
"depleted" = "Depleted"
"depletingSoon" = "Depleting..."
"offline" = "Ngoại tuyến"
"online" = "Trực tuyến"
"domainName" = "Tên miền"
"monitor" = "Listening IP"
"certificate" = "Chứng chỉ số"
"fail" = "Thất bại"
"comment" = "Bình luận"
"success" = "Thành công"
"getVersion" = "Lấy phiên bản"
"install" = "Cài đặt"
"clients" = "Các khách hàng"
"usage" = "Sử dụng"
"twoFactorCode" = "Mã"
"remained" = "Còn lại"
"security" = "Bảo vệ"
"secAlertTitle" = "Cảnh báo an ninh-Tiếng Việt by Ohoang7"
"secAlertSsl" = "Kết nối này không an toàn; Vui lòng không nhập thông tin nhạy cảm cho đến khi TLS được kích hoạt để bảo vệ dữ liệu của Bạn"
"secAlertConf" = "Một số cài đặt có thể dễ bị tấn công. Đề xuất tăng cường các giao thức bảo mật để ngăn chặn các vi phạm tiềm ẩn."
"secAlertSSL" = "Bảng điều khiển thiếu kết nối an toàn. Vui lòng cài đặt chứng chỉ TLS để bảo vệ dữ liệu."
"secAlertPanelPort" = "Cổng mặc định của bảng điều khiển có thể dễ bị tấn công. Vui lòng cấu hình một cổng ngẫu nhiên hoặc cụ thể."
"secAlertPanelURI" = "Đường dẫn URI mặc định của bảng điều khiển không an toàn. Vui lòng cấu hình một đường dẫn URI phức tạp."
"secAlertSubURI" = "Đường dẫn URI mặc định của đăng ký không an toàn. Vui lòng cấu hình một đường dẫn URI phức tạp."
"secAlertSubJsonURI" = "Đường dẫn URI JSON mặc định của đăng ký không an toàn. Vui lòng cấu hình một đường dẫn URI phức tạp."
"emptyDnsDesc" = "Không có máy chủ DNS nào được thêm."
"emptyFakeDnsDesc" = "Không có máy chủ Fake DNS nào được thêm."
"emptyBalancersDesc" = "Không có bộ cân bằng tải nào được thêm."
"emptyReverseDesc" = "Không có proxy ngược nào được thêm."
"somethingWentWrong" = "Đã xảy ra lỗi"

[menu]
"theme" = "Chủ đề"
"dark" = "Tối"
"ultraDark" = "Siêu tối"
"dashboard" = "Trạng thái hệ thống"
"inbounds" = "Đầu vào khách hàng"
"settings" = "Cài đặt bảng điều khiển"
"logout" = "Đăng xuất"
"xray" = "Cài đặt Xray"
"link" = "Quản lý"

[pages.login]
"hello" = "Xin chào"
"title" = "Chào mừng"
"loginAgain" = "Thời hạn đăng nhập đã hết. Vui lòng đăng nhập lại."

[pages.login.toasts]
"invalidFormData" = "Dạng dữ liệu nhập không hợp lệ."
"emptyUsername" = "Vui lòng nhập tên người dùng."
"emptyPassword" = "Vui lòng nhập mật khẩu."
"wrongUsernameOrPassword" = "Tên người dùng, mật khẩu hoặc mã xác thực hai yếu tố không hợp lệ."  
"successLogin" = "Bạn đã đăng nhập vào tài khoản thành công."

[pages.index]
"title" = "Trạng thái hệ thống"
"cpu" = "CPU"
"logicalProcessors" = "Bộ xử lý logic"
"frequency" = "Tần số"
"swap" = "Swap"
"storage" = "Lưu trữ"
"memory" = "RAM"
"threads" = "Luồng"
"xrayStatus" = "Xray"
"stopXray" = "Dừng lại"
"restartXray" = "Khởi động lại"
"xraySwitch" = "Phiên bản"
"xraySwitchClick" = "Chọn phiên bản mà bạn muốn chuyển đổi sang."
"xraySwitchClickDesk" = "Hãy lựa chọn thận trọng, vì các phiên bản cũ có thể không tương thích với các cấu hình hiện tại."
"xrayStatusUnknown" = "Không xác định"
"xrayStatusRunning" = "Đang chạy"
"xrayStatusStop" = "Dừng"
"xrayStatusError" = "Lỗi"
"xrayErrorPopoverTitle" = "Đã xảy ra lỗi khi chạy Xray"
"operationHours" = "Thời gian hoạt động"
"systemLoad" = "Tải hệ thống"
"systemLoadDesc" = "trung bình tải hệ thống trong 1, 5 và 15 phút qua"
"connectionTcpCountDesc" = "Tổng số kết nối TCP trên tất cả các thẻ mạng."
"connectionUdpCountDesc" = "Tổng số kết nối UDP trên tất cả các thẻ mạng."
"connectionCount" = "Số lượng kết nối"
"ipAddresses" = "Địa chỉ IP"
"toggleIpVisibility" = "Chuyển đổi hiển thị IP"
"overallSpeed" = "Tốc độ tổng thể"
"upload" = "Tải lên"
"download" = "Tải xuống"
"totalData" = "Tổng dữ liệu"
"sent" = "Đã gửi"
"received" = "Đã nhận"
"documentation" = "Tài liệu"
"xraySwitchVersionDialog" = "Bạn có chắc chắn muốn thay đổi phiên bản Xray không?"
"xraySwitchVersionDialogDesc" = "Hành động này sẽ thay đổi phiên bản Xray thành #version#."
"xraySwitchVersionPopover" = "Xray đã được cập nhật thành công"
"geofileUpdateDialog" = "Bạn có chắc chắn muốn cập nhật geofile không?"
"geofileUpdateDialogDesc" = "Hành động này sẽ cập nhật tệp #filename#."
"geofileUpdatePopover" = "Geofile đã được cập nhật thành công"
"dontRefresh" = "Đang tiến hành cài đặt, vui lòng không làm mới trang này."
"logs" = "Nhật ký"
"config" = "Cấu hình"
"backup" = "Sao lưu"
"backupTitle" = "Sao lưu & Khôi phục Cơ sở dữ liệu"
"exportDatabase" = "Sao lưu"
"exportDatabaseDesc" = "Nhấp để tải xuống tệp .db chứa bản sao lưu cơ sở dữ liệu hiện tại của bạn vào thiết bị."
"importDatabase" = "Khôi phục"
"importDatabaseDesc" = "Nhấp để chọn và tải lên tệp .db từ thiết bị của bạn để khôi phục cơ sở dữ liệu từ bản sao lưu."
"importDatabaseSuccess" = "Đã nhập cơ sở dữ liệu thành công"
"importDatabaseError" = "Lỗi xảy ra khi nhập cơ sở dữ liệu"
"readDatabaseError" = "Lỗi xảy ra khi đọc cơ sở dữ liệu"
"getDatabaseError" = "Lỗi xảy ra khi truy xuất cơ sở dữ liệu"
"getConfigError" = "Lỗi xảy ra khi truy xuất tệp cấu hình"

[pages.inbounds]
"title" = "Điểm vào (Inbounds)"
"totalDownUp" = "Tổng tải lên/tải xuống"
"totalUsage" = "Tổng sử dụng"
"inboundCount" = "Số lượng điểm vào"
"operate" = "Thao tác"
"enable" = "Kích hoạt"
"remark" = "Chú thích"
"protocol" = "Giao thức"
"port" = "Cổng"
"traffic" = "Lưu lượng"
"details" = "Chi tiết"
"transportConfig" = "Giao vận"
"expireDate" = "Ngày hết hạn"
"resetTraffic" = "Đặt lại lưu lượng"
"addInbound" = "Thêm điểm vào"
"generalActions" = "Hành động chung"
"autoRefresh" = "Tự động làm mới"
"autoRefreshInterval" = "Khoảng thời gian"
"modifyInbound" = "Chỉnh sửa điểm vào (Inbound)"
"deleteInbound" = "Xóa điểm vào (Inbound)"
"deleteInboundContent" = "Xác nhận xóa điểm vào? (Inbound)"
"deleteClient" = "Xóa người dùng"
"deleteClientContent" = "Bạn có chắc chắn muốn xóa người dùng không?"
"resetTrafficContent" = "Xác nhận đặt lại lưu lượng?"
"inboundUpdateSuccess" = "Đã cập nhật kết nối inbound thành công."
"inboundCreateSuccess" = "Đã tạo kết nối inbound thành công."
"copyLink" = "Sao chép liên kết"
"address" = "Địa chỉ"
"network" = "Mạng"
"destinationPort" = "Cổng đích"
"targetAddress" = "Địa chỉ mục tiêu"
"monitorDesc" = "Mặc định để trống"
"meansNoLimit" = "= Không giới hạn (đơn vị: GB)"
"totalFlow" = "Tổng lưu lượng"
"leaveBlankToNeverExpire" = "Để trống để không bao giờ hết hạn"
"noRecommendKeepDefault" = "Không yêu cầu đặc biệt để giữ nguyên cài đặt mặc định"
"certificatePath" = "Đường dẫn tập"
"certificateContent" = "Nội dung tập"
"publicKey" = "Khóa công khai"
"privatekey" = "Khóa cá nhân"
"clickOnQRcode" = "Nhấn vào Mã QR để sao chép"
"client" = "Người dùng"
"export" = "Xuất liên kết"
"clone" = "Sao chép"
"cloneInbound" = "Sao chép điểm vào (Inbound)"
"cloneInboundContent" = "Tất cả cài đặt của điểm vào này, trừ Cổng, IP nghe và máy khách, sẽ được áp dụng cho bản sao."
"cloneInboundOk" = "Sao chép"
"resetAllTraffic" = "Đặt lại lưu lượng cho tất cả điểm vào"
"resetAllTrafficTitle" = "Đặt lại lưu lượng cho tất cả điểm vào"
"resetAllTrafficContent" = "Bạn có chắc chắn muốn đặt lại lưu lượng cho tất cả điểm vào không?"
"resetInboundClientTraffics" = "Đặt lại lưu lượng toàn bộ người dùng của điểm vào"
"resetInboundClientTrafficTitle" = "Đặt lại lưu lượng cho toàn bộ người dùng của điểm vào"
"resetInboundClientTrafficContent" = "Bạn có chắc chắn muốn đặt lại tất cả lưu lượng cho các người dùng của điểm vào này không?"
"resetAllClientTraffics" = "Đặt lại lưu lượng cho toàn bộ người dùng"
"resetAllClientTrafficTitle" = "Đặt lại lưu lượng cho toàn bộ người dùng"
"resetAllClientTrafficContent" = "Bạn có chắc chắn muốn đặt lại tất cả lưu lượng cho toàn bộ người dùng không?"
"delDepletedClients" = "Xóa các người dùng đã cạn kiệt"
"delDepletedClientsTitle" = "Xóa các người dùng đã cạn kiệt"
"delDepletedClientsContent" = "Bạn có chắc chắn muốn xóa toàn bộ người dùng đã cạn kiệt không?"
"email" = "Email"
"emailDesc" = "Vui lòng cung cấp một địa chỉ email duy nhất."
"IPLimit" = "Giới hạn IP"
"IPLimitDesc" = "Vô hiệu hóa điểm vào nếu số lượng vượt quá giá trị đã nhập (nhập 0 để vô hiệu hóa giới hạn IP)."
"IPLimitlog" = "Lịch sử IP"
"IPLimitlogDesc" = "Lịch sử đăng nhập IP (trước khi kích hoạt điểm vào sau khi bị vô hiệu hóa bởi giới hạn IP, bạn nên xóa lịch sử)."
"IPLimitlogclear" = "Xóa Lịch sử"
"setDefaultCert" = "Đặt chứng chỉ từ bảng điều khiển"
"telegramDesc" = "Vui lòng cung cấp ID Trò chuyện Telegram. (sử dụng lệnh '/id' trong bot) hoặc (@userinfobot)"
"subscriptionDesc" = "Bạn có thể tìm liên kết gói đăng ký của mình trong Chi tiết, cũng như bạn có thể sử dụng cùng tên cho nhiều cấu hình khác nhau"
"info" = "Thông tin"
"same" = "Giống nhau"
"inboundData" = "Dữ liệu gửi đến"
"exportInbound" = "Xuất nhập khẩu"
"import" = "Nhập"
"importInbound" = "Nhập inbound"

[pages.client]
"add" = "Thêm người dùng"
"edit" = "Chỉnh sửa người dùng"
"submitAdd" = "Thêm"
"submitEdit" = "Lưu thay đổi"
"clientCount" = "Số lượng người dùng"
"bulk" = "Thêm hàng loạt"
"method" = "Phương pháp"
"first" = "Đầu tiên"
"last" = "Cuối cùng"
"prefix" = "Tiền tố"
"postfix" = "Hậu tố"
"delayedStart" = "Bắt đầu ở Lần Đầu"
"expireDays" = "Khoảng thời gian"
"days" = "ngày"
"renew" = "Tự động gia hạn"
"renewDesc" = "Tự động gia hạn sau khi hết hạn. (0 = tắt)(đơn vị: ngày)"

[pages.inbounds.toasts]
"obtain" = "Nhận"
"updateSuccess" = "Cập nhật thành công"
"logCleanSuccess" = "Đã xóa nhật ký"
"inboundsUpdateSuccess" = "Đã cập nhật thành công các kết nối inbound"
"inboundUpdateSuccess" = "Đã cập nhật thành công kết nối inbound"
"inboundCreateSuccess" = "Đã tạo thành công kết nối inbound"
"inboundDeleteSuccess" = "Đã xóa thành công kết nối inbound"
"inboundClientAddSuccess" = "Đã thêm client inbound"
"inboundClientDeleteSuccess" = "Đã xóa client inbound"
"inboundClientUpdateSuccess" = "Đã cập nhật client inbound"
"delDepletedClientsSuccess" = "Đã xóa tất cả client hết hạn"
"resetAllClientTrafficSuccess" = "Đã đặt lại toàn bộ lưu lượng client"
"resetAllTrafficSuccess" = "Đã đặt lại toàn bộ lưu lượng"
"resetInboundClientTrafficSuccess" = "Đã đặt lại lưu lượng"
"trafficGetError" = "Lỗi khi lấy thông tin lưu lượng"
"getNewX25519CertError" = "Lỗi khi lấy chứng chỉ X25519."

[pages.inbounds.stream.general]
"request" = "Lời yêu cầu"
"response" = "Phản ứng"
"name" = "Tên"
"value" = "Giá trị"

[pages.inbounds.stream.tcp]
"version" = "Phiên bản"
"method" = "Phương pháp"
"path" = "Đường dẫn"
"status" = "Trạng thái"
"statusDescription" = "Tình trạng Mô tả"
"requestHeader" = "Header yêu cầu"
"responseHeader" = "Header phản hồi"

[pages.settings]
"title" = "Cài đặt"
"save" = "Lưu"
"infoDesc" = "Mọi thay đổi được thực hiện ở đây cần phải được lưu. Vui lòng khởi động lại bảng điều khiển để áp dụng các thay đổi."
"restartPanel" = "Khởi động lại bảng điều khiển"
"restartPanelDesc" = "Bạn có chắc chắn muốn khởi động lại bảng điều khiển? Nhấn OK để khởi động lại sau 3 giây. Nếu bạn không thể truy cập bảng điều khiển sau khi khởi động lại, vui lòng xem thông tin nhật ký của bảng điều khiển trên máy chủ."
"restartPanelSuccess" = "Đã khởi động lại bảng điều khiển thành công"
"actions" = "Hành động"
"resetDefaultConfig" = "Đặt lại cấu hình mặc định"
"panelSettings" = "Bảng điều khiển"
"securitySettings" = "Bảo mật"
"TGBotSettings" = "Bot Telegram"
"panelListeningIP" = "IP Nghe của bảng điều khiển"
"panelListeningIPDesc" = "Mặc định để trống để nghe tất cả các IP."
"panelListeningDomain" = "Tên miền của nghe bảng điều khiển"
"panelListeningDomainDesc" = "Mặc định để trống để nghe tất cả các tên miền và IP"
"panelPort" = "Cổng bảng điều khiển"
"panelPortDesc" = "Cổng được sử dụng để kết nối với bảng điều khiển này"
"publicKeyPath" = "Đường dẫn file chứng chỉ bảng điều khiển"
"publicKeyPathDesc" = "Điền vào đường dẫn đầy đủ (bắt đầu từ '/')"
"privateKeyPath" = "Đường dẫn file khóa của chứng chỉ bảng điều khiển"
"privateKeyPathDesc" = "Điền vào đường dẫn đầy đủ (bắt đầu từ '/')"
"panelUrlPath" = "Đường dẫn gốc URL bảng điều khiển"
"panelUrlPathDesc" = "Phải bắt đầu và kết thúc bằng '/'"
"pageSize" = "Kích thước phân trang"
"pageSizeDesc" = "Xác định kích thước trang cho bảng gửi đến. Đặt 0 để tắt"
"remarkModel" = "Ghi chú mô hình và ký tự phân tách"
"datepicker" = "Kiểu lịch"
"datepickerPlaceholder" = "Chọn ngày"
"datepickerDescription" = "Tác vụ chạy theo lịch trình sẽ chạy theo kiểu lịch này."
"sampleRemark" = "Nhận xét mẫu"
"oldUsername" = "Tên người dùng hiện tại"
"currentPassword" = "Mật khẩu hiện tại"
"newUsername" = "Tên người dùng mới"
"newPassword" = "Mật khẩu mới"
"telegramBotEnable" = "Bật Bot Telegram"
"telegramBotEnableDesc" = "Kết nối với các tính năng của bảng điều khiển này thông qua bot Telegram"
"telegramToken" = "Token Telegram"
"telegramTokenDesc" = "Bạn phải nhận token từ quản lý bot Telegram @botfather"
"telegramProxy" = "Socks5 Proxy"
"telegramProxyDesc" = "Nếu bạn cần socks5 proxy để kết nối với Telegram. Điều chỉnh cài đặt của nó theo hướng dẫn."
"telegramAPIServer" = "Telegram API Server"
"telegramAPIServerDesc" = "Máy chủ API Telegram để sử dụng. Để trống để sử dụng máy chủ mặc định."
"telegramChatId" = "Chat ID Telegram của quản trị viên"
"telegramChatIdDesc" = "Nhiều Chat ID phân tách bằng dấu phẩy. Sử dụng @userinfobot hoặc sử dụng lệnh '/id' trong bot để lấy Chat ID của bạn."
"telegramNotifyTime" = "Thời gian thông báo của bot Telegram"
"telegramNotifyTimeDesc" = "Sử dụng định dạng thời gian Crontab."
"tgNotifyBackup" = "Sao lưu Cơ sở dữ liệu"
"tgNotifyBackupDesc" = "Bao gồm tệp sao lưu cơ sở dữ liệu với thông báo báo cáo."
"tgNotifyLogin" = "Thông báo Đăng nhập"
"tgNotifyLoginDesc" = "Hiển thị tên người dùng, địa chỉ IP và thời gian khi ai đó cố gắng đăng nhập vào bảng điều khiển của bạn."
"sessionMaxAge" = "Thời gian tối đa của phiên"
"sessionMaxAgeDesc" = "Thời gian của phiên đăng nhập (đơn vị: phút)"
"expireTimeDiff" = "Ngưỡng hết hạn cho thông báo"
"expireTimeDiffDesc" = "Nhận thông báo về việc hết hạn tài khoản trước ngưỡng này (đơn vị: ngày)"
"trafficDiff" = "Ngưỡng lưu lượng cho thông báo"
"trafficDiffDesc" = "Nhận thông báo về việc cạn kiệt lưu lượng trước khi đạt đến ngưỡng này (đơn vị: GB)"
"tgNotifyCpu" = "Ngưỡng cảnh báo tỷ lệ CPU"
"tgNotifyCpuDesc" = "Nhận thông báo nếu tỷ lệ sử dụng CPU vượt quá ngưỡng này (đơn vị: %)"
"timeZone" = "Múi giờ"
"timeZoneDesc" = "Các tác vụ được lên lịch chạy theo thời gian trong múi giờ này."
"subSettings" = "Gói đăng ký"
"subEnable" = "Bật dịch vụ"
"subEnableDesc" = "Tính năng gói đăng ký với cấu hình riêng"
"subTitle" = "Tiêu đề Đăng ký"
"subTitleDesc" = "Tiêu đề hiển thị trong ứng dụng VPN"
"subListen" = "Listening IP"
"subListenDesc" = "Mặc định để trống để nghe tất cả các IP"
"subPort" = "Cổng gói đăng ký"
"subPortDesc" = "Số cổng dịch vụ đăng ký phải chưa được sử dụng trên máy chủ"
"subCertPath" = "Đường dẫn file chứng chỉ gói đăng ký"
"subCertPathDesc" = "Điền vào đường dẫn đầy đủ (bắt đầu với '/')"
"subKeyPath" = "Đường dẫn file khóa của chứng chỉ gói đăng ký"
"subKeyPathDesc" = "Điền vào đường dẫn đầy đủ (bắt đầu với '/')"
"subPath" = "Đường dẫn gốc URL gói đăng ký"
"subPathDesc" = "Phải bắt đầu và kết thúc bằng '/'"
"subDomain" = "Tên miền con"
"subDomainDesc" = "Mặc định để trống để nghe tất cả các tên miền và IP"
"subUpdates" = "Khoảng thời gian cập nhật gói đăng ký"
"subUpdatesDesc" = "Số giờ giữa các cập nhật trong ứng dụng khách"
"subEncrypt" = "Mã hóa cấu hình"
"subEncryptDesc" = "Mã hóa các cấu hình được trả về trong gói đăng ký"
"subShowInfo" = "Hiển thị thông tin sử dụng"
"subShowInfoDesc" = "Hiển thị lưu lượng truy cập còn lại và ngày sau tên cấu hình"
"subURI" = "URI proxy trung gian"
"subURIDesc" = "Thay đổi URI cơ sở của URL gói đăng ký để sử dụng cho proxy trung gian"
"externalTrafficInformEnable" = "Thông báo giao thông bên ngoài"
"externalTrafficInformEnableDesc" = "Thông báo cho API bên ngoài về mọi cập nhật lưu lượng truy cập."
"externalTrafficInformURI" = "URI thông báo lưu lượng truy cập bên ngoài"
"externalTrafficInformURIDesc" = "Cập nhật lưu lượng truy cập được gửi tới URI này."
"fragment" = "Sự phân mảnh"
"fragmentDesc" = "Kích hoạt phân mảnh cho gói TLS hello"
"fragmentSett" = "Cài đặt phân mảnh"
"noisesDesc" = "Bật Noises."
"noisesSett" = "Cài đặt Noises"
"mux" = "Mux"
"muxDesc" = "Truyền nhiều luồng dữ liệu độc lập trong luồng dữ liệu đã thiết lập."
"muxSett" = "Mux Cài đặt"
"direct" = "Kết nối trực tiếp"
"directDesc" = "Trực tiếp thiết lập kết nối với tên miền hoặc dải IP của một quốc gia cụ thể."
"notifications" = "Thông báo"
"certs" = "Chứng chỉ"
"externalTraffic" = "Lưu lượng bên ngoài"
"dateAndTime" = "Ngày và giờ"
"proxyAndServer" = "Proxy và máy chủ"
"intervals" = "Khoảng thời gian"
"information" = "Thông tin"
"language" = "Ngôn ngữ"
"telegramBotLanguage" = "Ngôn ngữ của Bot Telegram"

[pages.xray]
"title" = "Cài đặt Xray"
"save" = "Lưu cài đặt"
"restart" = "Khởi động lại Xray"
"restartSuccess" = "Đã khởi động lại Xray thành công"
"stopSuccess" = "Xray đã được dừng thành công"
"restartError" = "Đã xảy ra lỗi khi khởi động lại Xray."
"stopError" = "Đã xảy ra lỗi khi dừng Xray."
"basicTemplate" = "Mẫu Cơ bản"
"advancedTemplate" = "Mẫu Nâng cao"
"generalConfigs" = "Cấu hình Chung"
"generalConfigsDesc" = "Những tùy chọn này sẽ cung cấp điều chỉnh tổng quát."
"logConfigs" = "Nhật ký"
"logConfigsDesc" = "Nhật ký có thể ảnh hưởng đến hiệu suất máy chủ của bạn. Bạn chỉ nên kích hoạt nó một cách khôn ngoan trong trường hợp bạn cần"
"blockConfigsDesc" = "Những tùy chọn này sẽ ngăn người dùng kết nối đến các giao thức và trang web cụ thể."
"basicRouting" = "Định tuyến Cơ bản"
"blockConnectionsConfigsDesc" = "Các tùy chọn này sẽ chặn lưu lượng truy cập dựa trên quốc gia được yêu cầu cụ thể."
"directConnectionsConfigsDesc" = "Kết nối trực tiếp đảm bảo rằng lưu lượng truy cập cụ thể không được định tuyến qua máy chủ khác."
"blockips" = "Chặn IP"
"blockdomains" = "Chặn Tên Miền"
"directips" = "IP Trực Tiếp"
"directdomains" = "Tên Miền Trực Tiếp"
"ipv4Routing" = "Định tuyến IPv4"
"ipv4RoutingDesc" = "Những tùy chọn này sẽ chỉ định kết nối đến các tên miền mục tiêu qua IPv4."
"warpRouting" = "Định tuyến WARP"
"warpRoutingDesc" = "Cảnh báo: Trước khi sử dụng những tùy chọn này, hãy cài đặt WARP ở chế độ proxy socks5 trên máy chủ của bạn bằng cách làm theo các bước trên GitHub của bảng điều khiển. WARP sẽ định tuyến lưu lượng đến các trang web qua máy chủ Cloudflare."
"Template" = "Mẫu Cấu hình Xray"
"TemplateDesc" = "Tạo tệp cấu hình Xray cuối cùng dựa trên mẫu này."
"FreedomStrategy" = "Cấu hình Chiến lược cho Giao thức Freedom"
"FreedomStrategyDesc" = "Đặt chiến lược đầu ra của mạng trong Giao thức Freedom."
"RoutingStrategy" = "Cấu hình Chiến lược Định tuyến Tên miền"
"RoutingStrategyDesc" = "Đặt chiến lược định tuyến tổng thể cho việc giải quyết DNS."
"Torrent" = "Cấu hình sử dụng BitTorrent"
"Inbounds" = "Đầu vào"
"InboundsDesc" = "Thay đổi mẫu cấu hình để chấp nhận các máy khách cụ thể."
"Outbounds" = "Đầu ra"
"Balancers" = "Cân bằng"
"OutboundsDesc" = "Thay đổi mẫu cấu hình để xác định các cách ra đi cho máy chủ này."
"Routings" = "Quy tắc định tuyến"
"RoutingsDesc" = "Mức độ ưu tiên của mỗi quy tắc đều quan trọng!"
"completeTemplate" = "All"
"logLevel" = "Mức đăng nhập"
"logLevelDesc" = "Cấp độ nhật ký cho nhật ký lỗi, cho biết thông tin cần được ghi lại."
"accessLog" = "Nhật ký truy cập"
"accessLogDesc" = "Đường dẫn tệp cho nhật ký truy cập. Nhật ký truy cập bị vô hiệu hóa có giá trị đặc biệt 'không'"
"errorLog" = "Nhật ký lỗi"
"errorLogDesc" = "Đường dẫn tệp cho nhật ký lỗi. Nhật ký lỗi bị vô hiệu hóa có giá trị đặc biệt 'không'"
"dnsLog" = "Nhật ký DNS"
"dnsLogDesc" = "Có bật nhật ký truy vấn DNS không"
"maskAddress" = "Ẩn Địa Chỉ"
"maskAddressDesc" = "Mặt nạ địa chỉ IP, khi được bật, sẽ tự động thay thế địa chỉ IP xuất hiện trong nhật ký."
"statistics" = "Thống kê"
"statsInboundUplink" = "Thống kê tải lên đầu vào"
"statsInboundUplinkDesc" = "Kích hoạt thu thập thống kê cho lưu lượng tải lên của tất cả các proxy đầu vào."
"statsInboundDownlink" = "Thống kê tải xuống đầu vào"
"statsInboundDownlinkDesc" = "Kích hoạt thu thập thống kê cho lưu lượng tải xuống của tất cả các proxy đầu vào."
"statsOutboundUplink" = "Thống kê tải lên đầu ra"
"statsOutboundUplinkDesc" = "Kích hoạt thu thập thống kê cho lưu lượng tải lên của tất cả các proxy đầu ra."
"statsOutboundDownlink" = "Thống kê tải xuống đầu ra"
"statsOutboundDownlinkDesc" = "Kích hoạt thu thập thống kê cho lưu lượng tải xuống của tất cả các proxy đầu ra."

[pages.xray.rules]
"first" = "Đầu tiên"
"last" = "Cuối cùng"
"up" = "Lên"
"down" = "Xuống"
"source" = "Nguồn"
"dest" = "Đích"
"inbound" = "Vào"
"outbound" = "Ra"
"balancer" = "Cân bằng"
"info" = "Thông tin"
"add" = "Thêm quy tắc"
"edit" = "Chỉnh sửa quy tắc"
"useComma" = "Các mục được phân tách bằng dấu phẩy"

[pages.xray.outbound]
"addOutbound" = "Thêm thư đi"
"addReverse" = "Thêm đảo ngược"
"editOutbound" = "Chỉnh sửa gửi đi"
"editReverse" = "Chỉnh sửa ngược lại"
"tag" = "Thẻ"
"tagDesc" = "thẻ duy nhất"
"address" = "Địa chỉ"
"reverse" = "Đảo ngược"
"domain" = "Miền"
"type" = "Loại"
"bridge" = "Cầu"
"portal" = "Cổng thông tin"
"link" = "Liên kết"
"intercon" = "Kết nối"
"settings" = "cài đặt"
"accountInfo" = "Thông tin tài khoản"
"outboundStatus" = "Trạng thái đầu ra"
"sendThrough" = "Gửi qua"

[pages.xray.balancer]
"addBalancer" = "Thêm cân bằng"
"editBalancer" = "Chỉnh sửa cân bằng"
"balancerStrategy" = "Chiến lược"
"balancerSelectors" = "Bộ chọn"
"tag" = "Thẻ"
"tagDesc" = "thẻ duy nhất"
"balancerDesc" = "Không thể sử dụng balancerTag và outboundTag cùng một lúc. Nếu sử dụng cùng lúc thì chỉ outboundTag mới hoạt động."

[pages.xray.wireguard]
"secretKey" = "Khoá bí mật"
"publicKey" = "Khóa công khai"
"allowedIPs" = "IP được phép"
"endpoint" = "Điểm cuối"
"psk" = "Khóa chia sẻ"
"domainStrategy" = "Chiến lược tên miền"

[pages.xray.dns]
"enable" = "Kích hoạt DNS"
"enableDesc" = "Kích hoạt máy chủ DNS tích hợp"
"tag" = "Thẻ gửi đến DNS"
"tagDesc" = "Thẻ này sẽ có sẵn dưới dạng thẻ Gửi đến trong quy tắc định tuyến."
"clientIp" = "IP khách hàng"
"clientIpDesc" = "Được sử dụng để thông báo cho máy chủ về vị trí IP được chỉ định trong các truy vấn DNS"
"disableCache" = "Tắt bộ nhớ đệm"
"disableCacheDesc" = "Tắt bộ nhớ đệm DNS"
"disableFallback" = "Tắt Fallback"
"disableFallbackDesc" = "Tắt các truy vấn DNS Fallback"
"disableFallbackIfMatch" = "Tắt Fallback Nếu Khớp"
"disableFallbackIfMatchDesc" = "Tắt các truy vấn DNS Fallback khi danh sách tên miền khớp của máy chủ DNS được kích hoạt"
"strategy" = "Chiến lược truy vấn"
"strategyDesc" = "Chiến lược tổng thể để phân giải tên miền"
"add" = "Thêm máy chủ"
"edit" = "Chỉnh sửa máy chủ"
"domains" = "Tên miền"
"expectIPs" = "Các IP Dự Kiến"
"unexpectIPs" = "IP không mong muốn"
"useSystemHosts" = "Sử dụng Hosts hệ thống"
"useSystemHostsDesc" = "Sử dụng file hosts từ hệ thống đã cài đặt"
"usePreset" = "Dùng mẫu"
"dnsPresetTitle" = "Mẫu DNS"
"dnsPresetFamily" = "Gia đình"

[pages.xray.fakedns]
"add" = "Thêm DNS giả"
"edit" = "Chỉnh sửa DNS giả"
"ipPool" = "Mạng con nhóm IP"
"poolSize" = "Kích thước bể bơi"

[pages.settings.security]
"admin" = "Thông tin đăng nhập quản trị viên"
"twoFactor" = "Xác thực hai yếu tố"  
"twoFactorEnable" = "Bật 2FA"  
"twoFactorEnableDesc" = "Thêm một lớp bảo mật bổ sung để tăng cường an toàn."  
"twoFactorModalSetTitle" = "Bật xác thực hai yếu tố"
"twoFactorModalDeleteTitle" = "Tắt xác thực hai yếu tố"
"twoFactorModalSteps" = "Để thiết lập xác thực hai yếu tố, hãy thực hiện các bước sau:"
"twoFactorModalFirstStep" = "1. Quét mã QR này trong ứng dụng xác thực hoặc sao chép mã token gần mã QR và dán vào ứng dụng"
"twoFactorModalSecondStep" = "2. Nhập mã từ ứng dụng"
"twoFactorModalRemoveStep" = "Nhập mã từ ứng dụng để xóa xác thực hai yếu tố."
"twoFactorModalChangeCredentialsTitle" = "Thay đổi thông tin xác thực"
"twoFactorModalChangeCredentialsStep" = "Nhập mã từ ứng dụng để thay đổi thông tin xác thực quản trị viên."
"twoFactorModalSetSuccess" = "Xác thực hai yếu tố đã được thiết lập thành công"
"twoFactorModalDeleteSuccess" = "Xác thực hai yếu tố đã được xóa thành công"
"twoFactorModalError" = "Mã sai"

[pages.settings.toasts]
"modifySettings" = "Các tham số đã được thay đổi."
"getSettings" = "Lỗi xảy ra khi truy xuất tham số."
"modifyUserError" = "Đã xảy ra lỗi khi thay đổi thông tin đăng nhập quản trị viên."
"modifyUser" = "Bạn đã thay đổi thông tin đăng nhập quản trị viên thành công."
"originalUserPassIncorrect" = "Tên người dùng hoặc mật khẩu gốc không đúng"
"userPassMustBeNotEmpty" = "Tên người dùng mới và mật khẩu mới không thể để trống"
"getOutboundTrafficError" = "Lỗi khi lấy lưu lượng truy cập đi"
"resetOutboundTrafficError" = "Lỗi khi đặt lại lưu lượng truy cập đi"

[tgbot]
"keyboardClosed" = "❌ Bàn phím tùy chỉnh đã đóng!"
"noResult" = "❗ Không có kết quả!"
"noQuery" = "❌ Không tìm thấy truy vấn! Vui lòng sử dụng lệnh lại!"
"wentWrong" = "❌ Đã xảy ra lỗi!"
"noIpRecord" = "❗ Không có bản ghi IP!"
"noInbounds" = "❗ Không tìm thấy inbound!"
"unlimited" = "♾ Không giới hạn"
"add" = "Thêm"
"month" = "Tháng"
"months" = "Tháng"
"day" = "Ngày"
"days" = "Ngày"
"hours" = "Giờ"
"unknown" = "Không rõ"
"inbounds" = "Vào"
"clients" = "Các người dùng"
"offline" = "🔴 Ngoại tuyến"
"online" = "🟢 Trực tuyến"

[tgbot.commands]
"unknown" = "❗ Lệnh không rõ"
"pleaseChoose" = "👇 Vui lòng chọn:\r\n"
"help" = "🤖 Chào mừng bạn đến với bot này! Bot được thiết kế để cung cấp cho bạn dữ liệu cụ thể từ máy chủ và cho phép bạn thực hiện các thay đổi cần thiết.\r\n\r\n"
"start" = "👋 Xin chào <i>{{ .Firstname }}</i>.\r\n"
"welcome" = "🤖 Chào mừng đến với bot quản lý của <b>{{ .Hostname }}</b>.\r\n"
"status" = "✅ Bot hoạt động bình thường!"
"usage" = "❗ Vui lòng cung cấp văn bản để tìm kiếm!"
"getID" = "🆔 ID của bạn: <code>{{ .ID }}</code>"
"helpAdminCommands" = "Để khởi động lại Xray Core:\r\n<code>/restart</code>\r\n\r\nĐể tìm kiếm email của khách hàng:\r\n<code>/usage [Email]</code>\r\n\r\nĐể tìm kiếm các nhập (với số liệu thống kê của khách hàng):\r\n<code>/inbound [Ghi chú]</code>\r\n\r\nID Trò chuyện Telegram:\r\n<code>/id</code>"
"helpClientCommands" = "Để tìm kiếm thống kê, sử dụng lệnh sau:\r\n<code>/usage [Email]</code>\r\n\r\nID Trò chuyện Telegram:\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ Hoạt động thành công!"
"restartFailed" = "❗ Lỗi trong quá trình hoạt động.\r\n\r\n<code>Lỗi: {{ .Error }}</code>."
"xrayNotRunning" = "❗ Xray Core không chạy."
"startDesc" = "Hiển thị menu chính"
"helpDesc" = "Trợ giúp bot"
"statusDesc" = "Kiểm tra trạng thái bot"
"idDesc" = "Hiển thị ID Telegram của bạn"

[tgbot.messages]
"cpuThreshold" = "🔴 Sử dụng CPU {{ .Percent }}% vượt quá ngưỡng {{ .Threshold }}%"
"selectUserFailed" = "❌ Lỗi khi chọn người dùng!"
"userSaved" = "✅ Người dùng Telegram đã được lưu."
"loginSuccess" = "✅ Đăng nhập thành công vào bảng điều khiển.\r\n"
"loginFailed" = "❗️ Đăng nhập vào bảng điều khiển thất bại.\r\n"
"report" = "🕰 Báo cáo định kỳ: {{ .RunTime }}\r\n"
"datetime" = "⏰ Ngày-Giờ: {{ .DateTime }}\r\n"
"hostname" = "💻 Tên máy chủ: {{ .Hostname }}\r\n"
"version" = "🚀 Phiên bản X-UI: {{ .Version }}\r\n"
"xrayVersion" = "📡 Phiên bản Xray: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6: {{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4: {{ .IPv4 }}\r\n"
"ip" = "🌐 IP: {{ .IP }}\r\n"
"ips" = "🔢 Các IP:\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ Thời gian hoạt động của máy chủ: {{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 Tải máy chủ: {{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 Bộ nhớ máy chủ: {{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 Số lượng kết nối TCP: {{ .Count }}\r\n"
"udpCount" = "🔸 Số lượng kết nối UDP: {{ .Count }}\r\n"
"traffic" = "🚦 Lưu lượng: {{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ Trạng thái Xray: {{ .State }}\r\n"
"username" = "👤 Tên người dùng: {{ .Username }}\r\n"
"password" = "👤 Mật khẩu: {{ .Password }}\r\n"
"time" = "⏰ Thời gian: {{ .Time }}\r\n"
"inbound" = "📍 Inbound: {{ .Remark }}\r\n"
"port" = "🔌 Cổng: {{ .Port }}\r\n"
"expire" = "📅 Ngày hết hạn: {{ .Time }}\r\n"
"expireIn" = "📅 Hết hạn sau: {{ .Time }}\r\n"
"active" = "💡 Đang hoạt động: {{ .Enable }}\r\n"
"enabled" = "🚨 Đã bật: {{ .Enable }}\r\n"
"online" = "🌐 Trạng thái kết nối: {{ .Status }}\r\n"
"email" = "📧 Email: {{ .Email }}\r\n"
"upload" = "🔼 Tải lên: ↑{{ .Upload }}\r\n"
"download" = "🔽 Tải xuống: ↓{{ .Download }}\r\n"
"total" = "📊 Tổng cộng: ↑↓{{ .UpDown }} / {{ .Total }}\r\n"
"TGUser" = "👤 Người dùng Telegram: {{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 Sự cạn kiệt {{ .Type }}:\r\n"
"exhaustedCount" = "🚨 Số lần cạn kiệt {{ .Type }}:\r\n"
"onlinesCount" = "🌐 Khách hàng trực tuyến: {{ .Count }}\r\n"
"disabled" = "🛑 Vô hiệu hóa: {{ .Disabled }}\r\n"
"depleteSoon" = "🔜 Sắp cạn kiệt: {{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 Thời gian sao lưu: {{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 Đã cập nhật lần cuối vào: {{ .Time }}\r\n\r\n"
"yes" = "✅ Có"
"no" = "❌ Không"

"received_id" = "🔑📥 ID đã được cập nhật."
"received_password" = "🔑📥 Mật khẩu đã được cập nhật."
"received_email" = "📧📥 Email đã được cập nhật."
"received_comment" = "💬📥 Bình luận đã được cập nhật."
"id_prompt" = "🔑 ID mặc định: {{ .ClientId }}\n\nVui lòng nhập ID của bạn."
"pass_prompt" = "🔑 Mật khẩu mặc định: {{ .ClientPassword }}\n\nVui lòng nhập mật khẩu của bạn."
"email_prompt" = "📧 Email mặc định: {{ .ClientEmail }}\n\nVui lòng nhập email của bạn."
"comment_prompt" = "💬 Bình luận mặc định: {{ .ClientComment }}\n\nVui lòng nhập bình luận của bạn."
"inbound_client_data_id" = "🔄 Kết nối vào: {{ .InboundRemark }}\n\n🔑 ID: {{ .ClientId }}\n📧 Email: {{ .ClientEmail }}\n📊 Dung lượng: {{ .ClientTraffic }}\n📅 Ngày hết hạn: {{ .ClientExp }}\n🌐 Giới hạn IP: {{ .IpLimit }}\n💬 Ghi chú: {{ .ClientComment }}\n\nBây giờ bạn có thể thêm khách hàng vào inbound!"
"inbound_client_data_pass" = "🔄 Kết nối vào: {{ .InboundRemark }}\n\n🔑 Mật khẩu: {{ .ClientPass }}\n📧 Email: {{ .ClientEmail }}\n📊 Dung lượng: {{ .ClientTraffic }}\n📅 Ngày hết hạn: {{ .ClientExp }}\n🌐 Giới hạn IP: {{ .IpLimit }}\n💬 Ghi chú: {{ .ClientComment }}\n\nBây giờ bạn có thể thêm khách hàng vào inbound!"
"cancel" = "❌ Quá trình đã bị hủy! \n\nBạn có thể bắt đầu lại bất cứ lúc nào bằng cách nhập /start. 🔄"
"error_add_client"  = "⚠️ Lỗi:\n\n {{ .error }}"
"using_default_value"  = "Được rồi, tôi sẽ sử dụng giá trị mặc định. 😊"
"incorrect_input" ="Dữ liệu bạn nhập không hợp lệ.\nCác chuỗi phải liền mạch và không có dấu cách.\nVí dụ đúng: aaaaaa\nVí dụ sai: aaa aaa 🚫"
"AreYouSure" = "Bạn có chắc không? 🤔"
"SuccessResetTraffic" = "📧 Email: {{ .ClientEmail }}\n🏁 Kết quả: ✅ Thành công"
"FailedResetTraffic" = "📧 Email: {{ .ClientEmail }}\n🏁 Kết quả: ❌ Thất bại \n\n🛠️ Lỗi: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 Quá trình đặt lại lưu lượng đã hoàn tất cho tất cả khách hàng."


[tgbot.buttons]
"closeKeyboard" = "❌ Đóng Bàn Phím"
"cancel" = "❌ Hủy"
"cancelReset" = "❌ Hủy Đặt Lại"
"cancelIpLimit" = "❌ Hủy Giới Hạn IP"
"confirmResetTraffic" = "✅ Xác Nhận Đặt Lại Lưu Lượng?"
"confirmClearIps" = "✅ Xác Nhận Xóa Các IP?"
"confirmRemoveTGUser" = "✅ Xác Nhận Xóa Người Dùng Telegram?"
"confirmToggle" = "✅ Xác nhận Bật/Tắt người dùng?"
"dbBackup" = "Tải bản sao lưu cơ sở dữ liệu"
"serverUsage" = "Sử Dụng Máy Chủ"
"getInbounds" = "Lấy cổng vào"
"depleteSoon" = "Depleted Soon"
"clientUsage" = "Lấy Sử Dụng"
"onlines" = "Khách hàng trực tuyến"
"commands" = "Lệnh"
"refresh" = "🔄 Cập Nhật"
"clearIPs" = "❌ Xóa IP"
"removeTGUser" = "❌ Xóa Người Dùng Telegram"
"selectTGUser" = "👤 Chọn Người Dùng Telegram"
"selectOneTGUser" = "👤 Chọn một người dùng telegram:"
"resetTraffic" = "📈 Đặt Lại Lưu Lượng"
"resetExpire" = "📅 Thay đổi ngày hết hạn"
"ipLog" = "🔢 Nhật ký địa chỉ IP"
"ipLimit" = "🔢 Giới Hạn địa chỉ IP"
"setTGUser" = "👤 Đặt Người Dùng Telegram"
"toggle" = "🔘 Bật / Tắt"
"custom" = "🔢 Tùy chỉnh"
"confirmNumber" = "✅ Xác nhận: {{ .Num }}"
"confirmNumberAdd" = "✅ Xác nhận thêm: {{ .Num }}"
"limitTraffic" = "🚧 Giới hạn lưu lượng"
"getBanLogs" = "Cấm nhật ký"
"allClients" = "Tất cả Khách hàng"

"addClient" = "Thêm Khách Hàng"
"submitDisable" = "Gửi Dưới Dạng Vô Hiệu ☑️"
"submitEnable" = "Gửi Dưới Dạng Kích Hoạt ✅"
"use_default" = "🏷️ Sử Dụng Mặc Định"
"change_id" = "⚙️🔑 ID"
"change_password" = "⚙️🔑 Mật Khẩu"
"change_email" = "⚙️📧 Email"
"change_comment" = "⚙️💬 Bình Luận"
"ResetAllTraffics" = "Đặt lại tất cả lưu lượng"
"SortedTrafficUsageReport" = "Báo cáo sử dụng lưu lượng đã sắp xếp"


[tgbot.answers]
"successfulOperation" = "✅ Thành công!"
"errorOperation" = "❗ Lỗi Trong Quá Trình Thực Hiện."
"getInboundsFailed" = "❌ Không Thể Lấy Được Inbounds"
"getClientsFailed" = "❌ Không thể lấy khách hàng."
"canceled" = "❌ {{ .Email }} : Thao Tác Đã Bị Hủy."
"clientRefreshSuccess" = "✅ {{ .Email }} : Cập Nhật Thành Công Cho Khách Hàng."
"IpRefreshSuccess" = "✅ {{ .Email }} : Cập Nhật Thành Công Cho IPs."
"TGIdRefreshSuccess" = "✅ {{ .Email }} : Cập Nhật Thành Công Cho Người Dùng Telegram."
"resetTrafficSuccess" = "✅ {{ .Email }} : Đặt Lại Lưu Lượng Thành Công."
"setTrafficLimitSuccess" = "✅ {{ .Email }} : Đã lưu thành công giới hạn lưu lượng."
"expireResetSuccess" = "✅ {{ .Email }} : Đặt Lại Ngày Hết Hạn Thành Công."
"resetIpSuccess" = "✅ {{ .Email }} : Giới Hạn IP {{ .Count }} Đã Được Lưu Thành Công."
"clearIpSuccess" = "✅ {{ .Email }} : IP Đã Được Xóa Thành Công."
"getIpLog" = "✅ {{ .Email }} : Lấy nhật ký IP Thành Công."
"getUserInfo" = "✅ {{ .Email }} : Lấy Thông Tin Người Dùng Telegram Thành Công."
"removedTGUserSuccess" = "✅ {{ .Email }} : Người Dùng Telegram Đã Được Xóa Thành Công."
"enableSuccess" = "✅ {{ .Email }} : Đã Bật Thành Công."
"disableSuccess" = "✅ {{ .Email }} : Đã Tắt Thành Công."
"askToAddUserId" = "Cấu hình của bạn không được tìm thấy!\r\nVui lòng yêu cầu Quản trị viên sử dụng ID người dùng telegram của bạn trong cấu hình của bạn.\r\n\r\nID người dùng của bạn: <code>{{ .TgUserID }}</code>"
"chooseClient" = "Chọn một Khách hàng cho Inbound {{ .Inbound }}"
"chooseInbound" = "Chọn một Inbound"
