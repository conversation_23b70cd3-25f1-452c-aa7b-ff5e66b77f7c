{"remarks": "", "dns": {"tag": "dns_out", "queryStrategy": "UseIP", "servers": [{"address": "*******", "skipFallback": false}]}, "inbounds": [{"port": 10808, "protocol": "socks", "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "userLevel": 8}, "sniffing": {"destOverride": ["http", "tls", "quic", "fakedns"], "enabled": true}, "tag": "socks"}, {"port": 10809, "protocol": "http", "settings": {"userLevel": 8}, "tag": "http"}], "log": {"loglevel": "warning"}, "outbounds": [{"tag": "direct", "protocol": "freedom", "settings": {"domainStrategy": "AsIs", "redirect": "", "noises": []}}, {"tag": "block", "protocol": "blackhole", "settings": {"response": {"type": "http"}}}], "policy": {"levels": {"8": {"connIdle": 300, "downlinkOnly": 1, "handshake": 4, "uplinkOnly": 1}}, "system": {"statsOutboundUplink": true, "statsOutboundDownlink": true}}, "routing": {"domainStrategy": "AsIs", "rules": [{"type": "field", "network": "tcp,udp", "outboundTag": "proxy"}]}, "stats": {}}