{{define "settings/panel/security"}}
<a-collapse default-active-key="1">
    <a-collapse-panel key="1" header='{{ i18n "pages.settings.security.admin"}}'>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.oldUsername"}}</template>
            <template #control>
                <a-input autocomplete="username" v-model="user.oldUsername"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.currentPassword"}}</template>
            <template #control>
                <a-input-password autocomplete="current-password" v-model="user.oldPassword"></a-input-password>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.newUsername"}}</template>
            <template #control>
                <a-input v-model="user.newUsername"></a-input>
            </template>
        </a-setting-list-item>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.newPassword"}}</template>
            <template #control>
                <a-input-password autocomplete="new-password" v-model="user.newPassword"></a-input-password>
            </template>
        </a-setting-list-item>
        <a-list-item>
            <a-space direction="horizontal" :style="{ padding: '0 20px' }">
                <a-button type="primary" @click="updateUser">{{ i18n "confirm" }}</a-button>
            </a-space>
        </a-list-item>
    </a-collapse-panel>
    <a-collapse-panel key="2" header='{{ i18n "pages.settings.security.twoFactor" }}'>
        <a-setting-list-item paddings="small">
            <template #title>{{ i18n "pages.settings.security.twoFactorEnable" }}</template>
            <template #description>{{ i18n "pages.settings.security.twoFactorEnableDesc" }}</template>
            <template #control>
                <a-switch @click="toggleTwoFactor" :checked="allSetting.twoFactorEnable"></a-switch>
            </template>
        </a-setting-list-item>
    </a-collapse-panel>
</a-collapse>
{{end}}