{{define "form/streamXHTTP"}}
<a-form :colon="false" :label-col="{ md: {span:8} }" :wrapper-col="{ md: {span:14} }">
    <a-form-item label='{{ i18n "host" }}'>
        <a-input v-model.trim="inbound.stream.xhttp.host"></a-input>
    </a-form-item>
    <a-form-item label='{{ i18n "path" }}'>
        <a-input v-model.trim="inbound.stream.xhttp.path"></a-input>
    </a-form-item>
    <a-form-item label='{{ i18n "pages.inbounds.stream.tcp.requestHeader" }}'>
        <a-button icon="plus" size="small" @click="inbound.stream.xhttp.addHeader('', '')"></a-button>
    </a-form-item>
    <a-form-item :wrapper-col="{span:24}">
        <a-input-group compact v-for="(header, index) in inbound.stream.xhttp.headers">
            <a-input :style="{ width: '50%' }" v-model.trim="header.name"
                placeholder='{{ i18n "pages.inbounds.stream.general.name"}}'>
                <template slot="addonBefore" :style="{ margin: '0' }">[[ index+1 ]]</template>
            </a-input>
            <a-input :style="{ width: '50%' }" v-model.trim="header.value"
                placeholder='{{ i18n "pages.inbounds.stream.general.value" }}'>
                <a-button icon="minus" slot="addonAfter" size="small" @click="inbound.stream.xhttp.removeHeader(index)"></a-button>
            </a-input>
        </a-input-group>
    </a-form-item>
    <a-form-item label='Mode'>
        <a-select v-model="inbound.stream.xhttp.mode" :style="{ width: '50%' }"
            :dropdown-class-name="themeSwitcher.currentTheme">
            <a-select-option v-for="key in MODE_OPTION" :value="key">[[ key ]]</a-select-option>
        </a-select>
    </a-form-item>
    <a-form-item label="Max Buffered Upload" v-if="inbound.stream.xhttp.mode === 'packet-up'">
        <a-input-number v-model.number="inbound.stream.xhttp.scMaxBufferedPosts"></a-input-number>
    </a-form-item>
    <a-form-item label="Max Upload Size (Byte)" v-if="inbound.stream.xhttp.mode === 'packet-up'">
        <a-input v-model.trim="inbound.stream.xhttp.scMaxEachPostBytes"></a-input>
    </a-form-item>
    <a-form-item label="Stream-Up Server" v-if="inbound.stream.xhttp.mode === 'stream-up'">
        <a-input v-model.trim="inbound.stream.xhttp.scStreamUpServerSecs"></a-input>
    </a-form-item>
    <a-form-item label="Padding Bytes">
        <a-input v-model.trim="inbound.stream.xhttp.xPaddingBytes"></a-input>
    </a-form-item>
    <a-form-item label="No SSE Header">
        <a-switch v-model="inbound.stream.xhttp.noSSEHeader"></a-switch>
    </a-form-item>
</a-form>
{{end}}