"username" = "Username"
"password" = "Password"
"login" = "Log In"
"confirm" = "Confirm"
"cancel" = "Cancel"
"close" = "Close"
"create" = "Create"
"update" = "Update"
"copy" = "Copy"
"copied" = "Copied"
"download" = "Download"
"remark" = "Remark"
"enable" = "Enabled"
"protocol" = "Protocol"
"search" = "Search"
"filter" = "Filter"
"loading" = "Loading..."
"second" = "Second"
"minute" = "Minute"
"hour" = "Hour"
"day" = "Day"
"check" = "Check"
"indefinite" = "Indefinite"
"unlimited" = "Unlimited"
"none" = "None"
"qrCode" = "QR Code"
"info" = "More Information"
"edit" = "Edit"
"delete" = "Delete"
"reset" = "Reset"
"noData" = "No data."
"copySuccess" = "Copied Successful"
"sure" = "Sure"
"encryption" = "Encryption"
"useIPv4ForHost" = "Use IPv4 for host"
"transmission" = "Transmission"
"host" = "Host"
"path" = "Path"
"camouflage" = "Obfuscation"
"status" = "Status"
"enabled" = "Enabled"
"disabled" = "Disabled"
"depleted" = "Ended"
"depletingSoon" = "Depleting"
"offline" = "Offline"
"online" = "Online"
"domainName" = "Domain Name"
"monitor" = "Listen IP"
"certificate" = "Digital Certificate"
"fail" = "Failed"
"comment" = "Comment"
"success" = "Successfully"
"getVersion" = "Get Version"
"install" = "Install"
"clients" = "Clients"
"usage" = "Usage"
"twoFactorCode" = "Code"
"remained" = "Remained"
"security" = "Security"
"secAlertTitle" = "Security Alert"
"secAlertSsl" = "This connection is not secure. Please avoid entering sensitive information until TLS is activated for data protection."
"secAlertConf" = "Certain settings are vulnerable to attacks. It is recommended to reinforce security protocols to prevent potential breaches."
"secAlertSSL" = "Panel lacks secure connection. Please install TLS certificate for data protection."
"secAlertPanelPort" = "Panel default port is vulnerable. Please configure a random or specific port."
"secAlertPanelURI" = "Panel default URI path is insecure. Please configure a complex URI path."
"secAlertSubURI" = "Subscription default URI path is insecure. Please configure a complex URI path."
"secAlertSubJsonURI" = "Subscription JSON default URI path is insecure. Please configure a complex URI path."
"emptyDnsDesc" = "No added DNS servers."
"emptyFakeDnsDesc" = "No added Fake DNS servers."
"emptyBalancersDesc" = "No added balancers."
"emptyReverseDesc" = "No added reverse proxies."
"somethingWentWrong" = "Something went wrong"

[menu]
"theme" = "Theme"
"dark" = "Dark"
"ultraDark" = "Ultra Dark"
"dashboard" = "Overview"
"inbounds" = "Inbounds"
"settings" = "Panel Settings"
"xray" = "Xray Configs"
"logout" = "Log Out"
"link" = "Manage"

[pages.login]
"hello" = "Hello"
"title" = "Welcome"
"loginAgain" = "Your session has expired, please log in again"

[pages.login.toasts]
"invalidFormData" = "The Input data format is invalid."
"emptyUsername" = "Username is required"
"emptyPassword" = "Password is required"
"wrongUsernameOrPassword" = "Invalid username or password or two-factor code."
"successLogin" = " You have successfully logged into your account."

[pages.index]
"title" = "Overview"
"cpu" = "CPU"
"logicalProcessors" = "Logical Processors"
"frequency" = "Frequency"
"swap" = "Swap"
"storage" = "Storage"
"memory" = "RAM"
"threads" = "Threads"
"xrayStatus" = "Xray"
"stopXray" = "Stop"
"restartXray" = "Restart"
"xraySwitch" = "Version"
"xraySwitchClick" = "Choose the version you want to switch to."
"xraySwitchClickDesk" = "Choose carefully, as older versions may not be compatible with current configurations."
"xrayStatusUnknown" = "Unknown"
"xrayStatusRunning" = "Running"
"xrayStatusStop" = "Stop"
"xrayStatusError" = "Error"
"xrayErrorPopoverTitle" = "An error occurred while running Xray"
"operationHours" = "Uptime"
"systemLoad" = "System Load"
"systemLoadDesc" = "System load average for the past 1, 5, and 15 minutes"
"connectionCount" = "Connection Stats"
"ipAddresses" = "IP Addresses"
"toggleIpVisibility" = "Toggle visibility of the IP"
"overallSpeed" = "Overall Speed"
"upload" = "Upload"
"download" = "Download"
"totalData" = "Total Data"
"sent" = "Sent"
"received" = "Received"
"documentation" = "Documentation"
"xraySwitchVersionDialog" = "Do you really want to change the Xray version?"
"xraySwitchVersionDialogDesc" = "This will change the Xray version to #version#."
"xraySwitchVersionPopover" = "Xray updated successfully"
"geofileUpdateDialog" = "Do you really want to update the geofile?"
"geofileUpdateDialogDesc" = "This will update the #filename# file."
"geofileUpdatePopover" = "Geofile updated successfully"
"dontRefresh" = "Installation is in progress, please do not refresh this page"
"logs" = "Logs"
"config" = "Config"
"backup" = "Backup"
"backupTitle" = "Database Backup & Restore"
"exportDatabase" = "Back Up"
"exportDatabaseDesc" = "Click to download a .db file containing a backup of your current database to your device."
"importDatabase" = "Restore"
"importDatabaseDesc" = "Click to select and upload a .db file from your device to restore your database from a backup."
"importDatabaseSuccess" = "The database has been successfully imported."
"importDatabaseError" = "An error occurred while importing the database."
"readDatabaseError" = "An error occurred while reading the database."
"getDatabaseError" = "An error occurred while retrieving the database."
"getConfigError" = "An error occurred while retrieving the config file."

[pages.inbounds]
"title" = "Inbounds"
"totalDownUp" = "Total Sent/Received"
"totalUsage" = "Total Usage"
"inboundCount" = "Total Inbounds"
"operate" = "Menu"
"enable" = "Enabled"
"remark" = "Remark"
"protocol" = "Protocol"
"port" = "Port"
"traffic" = "Traffic"
"details" = "Details"
"transportConfig" = "Transport"
"expireDate" = "Duration"
"resetTraffic" = "Reset Traffic"
"addInbound" = "Add Inbound"
"generalActions" = "General Actions"
"autoRefresh" = "Auto-refresh"
"autoRefreshInterval" = "Interval"
"modifyInbound" = "Modify Inbound"
"deleteInbound" = "Delete Inbound"
"deleteInboundContent" = "Are you sure you want to delete inbound?"
"deleteClient" = "Delete Client"
"deleteClientContent" = "Are you sure you want to delete client?"
"resetTrafficContent" = "Are you sure you want to reset traffic?"
"copyLink" = "Copy URL"
"address" = "Address"
"network" = "Network"
"destinationPort" = "Destination Port"
"targetAddress" = "Target Address"
"monitorDesc" = "Leave blank to listen on all IPs"
"meansNoLimit" = "= Unlimited. (unit: GB)"
"totalFlow" = "Total Flow"
"leaveBlankToNeverExpire" = "Leave blank to never expire"
"noRecommendKeepDefault" = "It is recommended to keep the default"
"certificatePath" = "File Path"
"certificateContent" = "File Content"
"publicKey" = "Public Key"
"privatekey" = "Private Key"
"clickOnQRcode" = "Click on QR Code to Copy"
"client" = "Client"
"export" = "Export All URLs"
"clone" = "Clone"
"cloneInbound" = "Clone"
"cloneInboundContent" = "All settings of this inbound, except Port, Listening IP, and Clients, will be applied to the clone."
"cloneInboundOk" = "Clone"
"resetAllTraffic" = "Reset All Inbounds Traffic"
"resetAllTrafficTitle" = "Reset All Inbounds Traffic"
"resetAllTrafficContent" = "Are you sure you want to reset the traffic of all inbounds?"
"resetInboundClientTraffics" = "Reset Clients Traffic"
"resetInboundClientTrafficTitle" = "Reset Clients Traffic"
"resetInboundClientTrafficContent" = "Are you sure you want to reset the traffic of this inbound's clients?"
"resetAllClientTraffics" = "Reset All Clients Traffic"
"resetAllClientTrafficTitle" = "Reset All Clients Traffic"
"resetAllClientTrafficContent" = "Are you sure you want to reset the traffic of all clients?"
"delDepletedClients" = "Delete Depleted Clients"
"delDepletedClientsTitle" = "Delete Depleted Clients"
"delDepletedClientsContent" = "Are you sure you want to delete all the depleted clients?"
"email" = "Email"
"emailDesc" = "Please provide a unique email address."
"IPLimit" = "IP Limit"
"IPLimitDesc" = "Disables inbound if the count exceeds the set value. (0 = disable)"
"IPLimitlog" = "IP Log"
"IPLimitlogDesc" = "The IPs history log. (to enable inbound after disabling, clear the log)"
"IPLimitlogclear" = "Clear The Log"
"setDefaultCert" = "Set Cert from Panel"
"telegramDesc" = "Please provide Telegram Chat ID. (use '/id' command in the bot) or (@userinfobot)"
"subscriptionDesc" = "To find your subscription URL, navigate to the 'Details'. Additionally, you can use the same name for several clients."
"info" = "Info"
"same" = "Same"
"inboundData" = "Inbound's Data"
"exportInbound" = "Export Inbound"
"import" = "Import"
"importInbound" = "Import an Inbound"

[pages.client]
"add" = "Add Client"
"edit" = "Edit Client"
"submitAdd" = "Add Client"
"submitEdit" = "Save Changes"
"clientCount" = "Number of Clients"
"bulk" = "Add Bulk"
"method" = "Method"
"first" = "First"
"last" = "Last"
"prefix" = "Prefix"
"postfix" = "Postfix"
"delayedStart" = "Start After First Use"
"expireDays" = "Duration"
"days" = "Day(s)"
"renew" = "Auto Renew"
"renewDesc" = "Auto-renewal after expiration. (0 = disable)(unit: day)"

[pages.inbounds.toasts]
"obtain" = "Obtain"
"updateSuccess" = "The update was successful."
"logCleanSuccess" = "The log has been cleared."
"inboundsUpdateSuccess" = "Inbounds have been successfully updated."
"inboundUpdateSuccess" = "Inbound has been successfully updated."
"inboundCreateSuccess" = "Inbound has been successfully created."
"inboundDeleteSuccess" = "Inbound has been successfully deleted."
"inboundClientAddSuccess" = "Inbound client(s) have been added."
"inboundClientDeleteSuccess" = "Inbound client has been deleted."
"inboundClientUpdateSuccess" = "Inbound client has been updated."
"delDepletedClientsSuccess" = "All depleted clients are deleted."
"resetAllClientTrafficSuccess" = "All traffic from the client has been reset."
"resetAllTrafficSuccess" = "All traffic has been reset."
"resetInboundClientTrafficSuccess" = "Traffic has been reset."
"trafficGetError" = "Error getting traffics."
"getNewX25519CertError" = "Error while obtaining the X25519 certificate."


[pages.inbounds.stream.general]
"request" = "Request"
"response" = "Response"
"name" = "Name"
"value" = "Value"

[pages.inbounds.stream.tcp]
"version" = "Version"
"method" = "Method"
"path" = "Path"
"status" = "Status"
"statusDescription" = "Status Desc"
"requestHeader" = "Request Header"
"responseHeader" = "Response Header"

[pages.settings]
"title" = "Panel Settings"
"save" = "Save"
"infoDesc" = "Every change made here needs to be saved. Please restart the panel to apply changes."
"restartPanel" = "Restart Panel"
"restartPanelDesc" = "Are you sure you want to restart the panel? If you cannot access the panel after restarting, please view the panel log info on the server."
"restartPanelSuccess" = "The panel was successfully restarted."
"actions" = "Actions"
"resetDefaultConfig" = "Reset to Default"
"panelSettings" = "General"
"securitySettings" = "Authentication"
"TGBotSettings" = "Telegram Bot"
"panelListeningIP" = "Listen IP"
"panelListeningIPDesc" = "The IP address for the web panel. (leave blank to listen on all IPs)"
"panelListeningDomain" = "Listen Domain"
"panelListeningDomainDesc" = "The domain name for the web panel. (leave blank to listen on all domains and IPs)"
"panelPort" = "Listen Port"
"panelPortDesc" = "The port number for the web panel. (must be an unused port)"
"publicKeyPath" = "Public Key Path"
"publicKeyPathDesc" = "The public key file path for the web panel. (begins with ‘/‘)"
"privateKeyPath" = "Private Key Path"
"privateKeyPathDesc" = "The private key file path for the web panel. (begins with ‘/‘)"
"panelUrlPath" = "URI Path"
"panelUrlPathDesc" = "The URI path for the web panel. (begins with ‘/‘ and concludes with ‘/‘)"
"pageSize" = "Pagination Size"
"pageSizeDesc" = "Define page size for inbounds table. (0 = disable)"
"remarkModel" = "Remark Model & Separation Character"
"datepicker" = "Calendar Type"
"datepickerPlaceholder" = "Select date"
"datepickerDescription" = "Scheduled tasks will run based on this calendar."
"sampleRemark" = "Sample Remark"
"oldUsername" = "Current Username"
"currentPassword" = "Current Password"
"newUsername" = "New Username"
"newPassword" = "New Password"
"telegramBotEnable" = "Enable Telegram Bot"
"telegramBotEnableDesc" = "Enables the Telegram bot."
"telegramToken" = "Telegram Token"
"telegramTokenDesc" = "The Telegram bot token obtained from '@BotFather'."
"telegramProxy" = "SOCKS Proxy"
"telegramProxyDesc" = "Enables SOCKS5 proxy for connecting to Telegram. (adjust settings as per guide)"
"telegramAPIServer" = "Telegram API Server"
"telegramAPIServerDesc" = "The Telegram API server to use. Leave blank to use the default server."
"telegramChatId" = "Admin Chat ID"
"telegramChatIdDesc" = "The Telegram Admin Chat ID(s). (comma-separated)(get it here @userinfobot) or (use '/id' command in the bot)"
"telegramNotifyTime" = "Notification Time"
"telegramNotifyTimeDesc" = "The Telegram bot notification time set for periodic reports. (use the crontab time format)"
"tgNotifyBackup" = "Database Backup"
"tgNotifyBackupDesc" = "Send a database backup file with a report."
"tgNotifyLogin" = "Login Notification"
"tgNotifyLoginDesc" = "Get notified about the username, IP address, and time whenever someone attempts to log into your web panel."
"sessionMaxAge" = "Session Duration"
"sessionMaxAgeDesc" = "The duration for which you can stay logged in. (unit: minute)"
"expireTimeDiff" = "Expiration Date Notification"
"expireTimeDiffDesc" = "Get notified about expiration date when reaching this threshold. (unit: day)"
"trafficDiff" = "Traffic Cap Notification"
"trafficDiffDesc" = "Get notified about traffic cap when reaching this threshold. (unit: GB)"
"tgNotifyCpu" = "CPU Load Notification"
"tgNotifyCpuDesc" = "Get notified if CPU load exceeds this threshold. (unit: %)"
"timeZone" = "Time Zone"
"timeZoneDesc" = "Scheduled tasks will run based on this time zone."
"subSettings" = "Subscription"
"subEnable" = "Enable Subscription Service"
"subEnableDesc" = "Enables the subscription service."
"subTitle" = "Subscription Title"
"subTitleDesc" = "Title shown in VPN client"
"subListen" = "Listen IP"
"subListenDesc" = "The IP address for the subscription service. (leave blank to listen on all IPs)"
"subPort" = "Listen Port"
"subPortDesc" = "The port number for the subscription service. (must be an unused port)"
"subCertPath" = "Public Key Path"
"subCertPathDesc" = "The public key file path for the subscription service. (begins with ‘/‘)"
"subKeyPath" = "Private Key Path"
"subKeyPathDesc" = "The private key file path for the subscription service. (begins with ‘/‘)"
"subPath" = "URI Path"
"subPathDesc" = "The URI path for the subscription service. (begins with ‘/‘ and concludes with ‘/‘)"
"subDomain" = "Listen Domain"
"subDomainDesc" = "The domain name for the subscription service. (leave blank to listen on all domains and IPs)"
"subUpdates" = "Update Intervals"
"subUpdatesDesc" = "The update intervals of the subscription URL in the client apps. (unit: hour)"
"subEncrypt" = "Encode"
"subEncryptDesc" = "The returned content of subscription service will be Base64 encoded."
"subShowInfo" = "Show Usage Info"
"subShowInfoDesc" = "The remaining traffic and date will be displayed in the client apps."
"subURI" = "Reverse Proxy URI"
"subURIDesc" = "The URI path of the subscription URL for use behind proxies."
"externalTrafficInformEnable" = "External Traffic Inform"
"externalTrafficInformEnableDesc" = "Inform external API on every traffic update."
"externalTrafficInformURI" = "External Traffic Inform URI"
"externalTrafficInformURIDesc" = "Traffic updates are sent to this URI."
"fragment" = "Fragmentation"
"fragmentDesc" = "Enable fragmentation for TLS hello packet."
"fragmentSett" = "Fragmentation Settings"
"noisesDesc" = "Enable Noises."
"noisesSett" = "Noises Settings"
"mux" = "Mux"
"muxDesc" = "Transmit multiple independent data streams within an established data stream."
"muxSett" = "Mux Settings"
"direct" = "Direct Connection"
"directDesc" = "Directly establishes connections with domains or IP ranges of a specific country."
"notifications" = "Notifications"
"certs" = "Certificaties"
"externalTraffic" = "External Traffic"
"dateAndTime" = "Date and Time"
"proxyAndServer" = "Proxy and Server"
"intervals" = "Intervals"
"information" = "Information"
"language" = "Language"
"telegramBotLanguage" = "Telegram Bot Language"

[pages.xray]
"title" = "Xray Configs"
"save" = "Save"
"restart" = "Restart Xray"
"restartSuccess" = "Xray has been successfully relaunched."
"stopSuccess" = "Xray has been successfully stopped."
"restartError" = "There was an error when rebooting the Xray."
"stopError" = "There was an error when stopping the Xray."
"basicTemplate" = "Basics"
"advancedTemplate" = "Advanced"
"generalConfigs" = "General"
"generalConfigsDesc" = "These options will determine general adjustments."
"logConfigs" = "Log"
"logConfigsDesc" = "Logs may affect your server's efficiency. It is recommended to enable it wisely only in case of your needs"
"blockConfigsDesc" = "These options will block traffic based on specific requested protocols and websites."
"basicRouting" = "Basic Routing"
"blockConnectionsConfigsDesc" = "These options will block traffic based on the specific requested country."
"directConnectionsConfigsDesc" = "A direct connection ensures that specific traffic is not routed through another server."
"blockips" = "Block IPs"
"blockdomains" = "Block Domains"
"directips" = "Direct IPs"
"directdomains" = "Direct Domains"
"ipv4Routing" = "IPv4 Routing"
"ipv4RoutingDesc" = "These options will route traffic based on a specific destination via IPv4."
"warpRouting" = "WARP Routing"
"warpRoutingDesc" = "These options will route traffic based on a specific destination via WARP."
"Template" = "Advanced Xray Configuration Template"
"TemplateDesc" = "The final Xray config file will be generated based on this template."
"FreedomStrategy" = "Freedom Protocol Strategy"
"FreedomStrategyDesc" = "Set the output strategy for the network in the Freedom Protocol."
"RoutingStrategy" = "Overall Routing Strategy"
"RoutingStrategyDesc" = "Set the overall traffic routing strategy for resolving all requests."
"Torrent" = "Block BitTorrent Protocol"
"Inbounds" = "Inbounds"
"InboundsDesc" = "Accepting the specific clients."
"Outbounds" = "Outbounds"
"Balancers" = "Balancers"
"OutboundsDesc" = "Set the outgoing traffic pathway."
"Routings" = "Routing Rules"
"RoutingsDesc" = "The priority of each rule is important!"
"completeTemplate" = "All"
"logLevel" = "Log Level"
"logLevelDesc" = "The log level for error logs, indicating the information that needs to be recorded."
"accessLog" = "Access Log"
"accessLogDesc" = "The file path for the access log. The special value 'none' disabled access logs"
"errorLog" = "Error Log"
"errorLogDesc" = "The file path for the error log. The special value 'none' disabled error logs"
"dnsLog" = "DNS Log"
"dnsLogDesc" = "Whether to enable DNS query logs"
"maskAddress" = "Mask Address"
"maskAddressDesc" = "IP address mask, when enabled, will automatically replace the IP address that appears in the log."
"statistics" = "Statistics"
"statsInboundUplink" = "Inbound Upload Statistics"
"statsInboundUplinkDesc" = "Enables the statistics collection for upstream traffic of all inbound proxies."
"statsInboundDownlink" = "Inbound Download Statistics"
"statsInboundDownlinkDesc" = "Enables the statistics collection for downstream traffic of all inbound proxies."
"statsOutboundUplink" = "Outbound Upload Statistics"
"statsOutboundUplinkDesc" = "Enables the statistics collection for upstream traffic of all outbound proxies."
"statsOutboundDownlink" = "Outbound Download Statistics"
"statsOutboundDownlinkDesc" = "Enables the statistics collection for downstream traffic of all outbound proxies."

[pages.xray.rules]
"first" = "First"
"last" = "Last"
"up" = "Up"
"down" = "Down"
"source" = "Source"
"dest" = "Destination"
"inbound" = "Inbound"
"outbound" = "Outbound"
"balancer" = "Balancer"
"info" = "Info"
"add" = "Add Rule"
"edit" = "Edit Rule"
"useComma" = "Comma-separated items"

[pages.xray.outbound]
"addOutbound" = "Add Outbound"
"addReverse" = "Add Reverse"
"editOutbound" = "Edit Outbound"
"editReverse" = "Edit Reverse"
"tag" = "Tag"
"tagDesc" = "Unique Tag"
"address" = "Address"
"reverse" = "Reverse"
"domain" = "Domain"
"type" = "Type"
"bridge" = "Bridge"
"portal" = "Portal"
"link" = "Link"
"intercon" = "Interconnection"
"settings" = "Settings"
"accountInfo" = "Account Information"
"outboundStatus" = "Outbound Status"
"sendThrough" = "Send Through"

[pages.xray.balancer]
"addBalancer" = "Add Balancer"
"editBalancer" = "Edit Balancer"
"balancerStrategy" = "Strategy"
"balancerSelectors" = "Selectors"
"tag" = "Tag"
"tagDesc" = "Unique Tag"
"balancerDesc" = "It is not possible to use balancerTag and outboundTag at the same time. If used at the same time, only outboundTag will work."

[pages.xray.wireguard]
"secretKey" = "Secret Key"
"publicKey" = "Public Key"
"allowedIPs" = "Allowed IPs"
"endpoint" = "Endpoint"
"psk" = "PreShared Key"
"domainStrategy" = "Domain Strategy"

[pages.xray.dns]
"enable" = "Enable DNS"
"enableDesc" = "Enable built-in DNS server"
"tag" = "DNS Inbound Tag"
"tagDesc" = "This tag will be available as an Inbound tag in routing rules."
"clientIp" = "Client IP"
"clientIpDesc" = "Used to notify the server of the specified IP location during DNS queries"
"disableCache" = "Disable cache"
"disableCacheDesc" = "Disables DNS caching"
"disableFallback" = "Disable Fallback"
"disableFallbackDesc" = "Disables fallback DNS queries"
"disableFallbackIfMatch" = "Disable Fallback If Match"
"disableFallbackIfMatchDesc" = "Disables fallback DNS queries when the matching domain list of the DNS server is hit"
"strategy" = "Query Strategy"
"strategyDesc" = "Overall strategy to resolve domain names"
"add" = "Add Server"
"edit" = "Edit Server"
"domains" = "Domains"
"expectIPs" = "Expect IPs"
"unexpectIPs" = "Unexpect IPs"
"useSystemHosts" = "Use System Hosts"
"useSystemHostsDesc" = "Use the hosts file from an installed system"
"usePreset" = "Use Preset"
"dnsPresetTitle" = "DNS Presets"
"dnsPresetFamily" = "Family"

[pages.xray.fakedns]
"add" = "Add Fake DNS"
"edit" = "Edit Fake DNS"
"ipPool" = "IP Pool Subnet"
"poolSize" = "Pool Size"

[pages.settings.security]
"admin" = "Admin credentials"
"twoFactor" = "Two-factor authentication"
"twoFactorEnable" = "Enable 2FA"
"twoFactorEnableDesc" = "Adds an additional layer of authentication to provide more security."
"twoFactorModalSetTitle" = "Enable two-factor authentication"
"twoFactorModalDeleteTitle" = "Disable two-factor authentication"
"twoFactorModalSteps" = "To set up two-factor authentication, perform a few steps:"
"twoFactorModalFirstStep" = "1. Scan this QR code in the app for authentication or copy the token near the QR code and paste it into the app"
"twoFactorModalSecondStep" = "2. Enter the code from the app"
"twoFactorModalRemoveStep" = "Enter the code from the application to remove two-factor authentication."
"twoFactorModalChangeCredentialsTitle" = "Change credentials"
"twoFactorModalChangeCredentialsStep" = "Enter the code from the application to change administrator credentials."
"twoFactorModalSetSuccess" = "Two-factor authentication has been successfully established"
"twoFactorModalDeleteSuccess" = "Two-factor authentication has been successfully deleted"
"twoFactorModalError" = "Wrong code"

[pages.settings.toasts]
"modifySettings" = "The parameters have been changed."
"getSettings" = "An error occurred while retrieving parameters."
"modifyUserError" = "An error occurred while changing administrator credentials."
"modifyUser" = "You have successfully changed the credentials of the administrator."
"originalUserPassIncorrect" = "The сurrent username or password is invalid"
"userPassMustBeNotEmpty" = "The new username and password is empty"
"getOutboundTrafficError" = "Error getting traffics"
"resetOutboundTrafficError" = "Error in reset outbound traffics"

[tgbot]
"keyboardClosed" = "❌ Custom keyboard closed!"
"noResult" = "❗ No result!"
"noQuery" = "❌ Query not found! Please use the command again!"
"wentWrong" = "❌ Something went wrong!"
"noIpRecord" = "❗ No IP Record!"
"noInbounds" = "❗ No inbound found!"
"unlimited" = "♾ Unlimited(Reset)"
"add" = "Add"
"month" = "Month"
"months" = "Months"
"day" = "Day"
"days" = "Days"
"hours" = "Hours"
"unknown" = "Unknown"
"inbounds" = "Inbounds"
"clients" = "Clients"
"offline" = "🔴 Offline"
"online" = "🟢 Online"

[tgbot.commands]
"unknown" = "❗ Unknown command."
"pleaseChoose" = "👇 Please choose:\r\n"
"help" = "🤖 Welcome to this bot! It's designed to offer specific data from the web panel and allows you to make modifications as needed.\r\n\r\n"
"start" = "👋 Hello <i>{{ .Firstname }}</i>.\r\n"
"welcome" = "🤖 Welcome to <b>{{ .Hostname }}</b> management bot.\r\n"
"status" = "✅ Bot is OK!"
"usage" = "❗ Please provide a text to search!"
"getID" = "🆔 Your ID: <code>{{ .ID }}</code>"
"helpAdminCommands" = "To restart Xray Core:\r\n<code>/restart</code>\r\n\r\nTo search for a client email:\r\n<code>/usage [Email]</code>\r\n\r\nTo search for inbounds (with client stats):\r\n<code>/inbound [Remark]</code>\r\n\r\nTelegram Chat ID:\r\n<code>/id</code>"
"helpClientCommands" = "To search for statistics, use the following command:\r\n\r\n<code>/usage [Email]</code>\r\n\r\nTelegram Chat ID:\r\n<code>/id</code>"
"restartUsage" = "\r\n\r\n<code>/restart</code>"
"restartSuccess" = "✅ Operation successful!"
"restartFailed" = "❗ Error in operation.\r\n\r\n<code>Error: {{ .Error }}</code>."
"xrayNotRunning" = "❗ Xray Core is not running."
"startDesc" = "Show the main menu"
"helpDesc" = "Bot help"
"statusDesc" = "Check bot status"
"idDesc" = "Show your Telegram ID"

[tgbot.messages]
"cpuThreshold" = "🔴 CPU Load {{ .Percent }}% exceeds the threshold of {{ .Threshold }}%"
"selectUserFailed" = "❌ Error in user selection!"
"userSaved" = "✅ Telegram User saved."
"loginSuccess" = "✅ Logged in to the panel successfully.\r\n"
"loginFailed" = "❗️Login attempt to the panel failed.\r\n"
"report" = "🕰 Scheduled Reports: {{ .RunTime }}\r\n"
"datetime" = "⏰ Date&Time: {{ .DateTime }}\r\n"
"hostname" = "💻 Host: {{ .Hostname }}\r\n"
"version" = "🚀 3X-UI Version: {{ .Version }}\r\n"
"xrayVersion" = "📡 Xray Version: {{ .XrayVersion }}\r\n"
"ipv6" = "🌐 IPv6: {{ .IPv6 }}\r\n"
"ipv4" = "🌐 IPv4: {{ .IPv4 }}\r\n"
"ip" = "🌐 IP: {{ .IP }}\r\n"
"ips" = "🔢 IPs:\r\n{{ .IPs }}\r\n"
"serverUpTime" = "⏳ Uptime: {{ .UpTime }} {{ .Unit }}\r\n"
"serverLoad" = "📈 System Load: {{ .Load1 }}, {{ .Load2 }}, {{ .Load3 }}\r\n"
"serverMemory" = "📋 RAM: {{ .Current }}/{{ .Total }}\r\n"
"tcpCount" = "🔹 TCP: {{ .Count }}\r\n"
"udpCount" = "🔸 UDP: {{ .Count }}\r\n"
"traffic" = "🚦 Traffic: {{ .Total }} (↑{{ .Upload }},↓{{ .Download }})\r\n"
"xrayStatus" = "ℹ️ Status: {{ .State }}\r\n"
"username" = "👤 Username: {{ .Username }}\r\n"
"password" = "👤 Password: {{ .Password }}\r\n"
"time" = "⏰ Time: {{ .Time }}\r\n"
"inbound" = "📍 Inbound: {{ .Remark }}\r\n"
"port" = "🔌 Port: {{ .Port }}\r\n"
"expire" = "📅 Expire Date: {{ .Time }}\r\n"
"expireIn" = "📅 Expire In: {{ .Time }}\r\n"
"active" = "💡 Active: {{ .Enable }}\r\n"
"enabled" = "🚨 Enabled: {{ .Enable }}\r\n"
"online" = "🌐 Connection status: {{ .Status }}\r\n"
"email" = "📧 Email: {{ .Email }}\r\n"
"upload" = "🔼 Upload: ↑{{ .Upload }}\r\n"
"download" = "🔽 Download: ↓{{ .Download }}\r\n"
"total" = "📊 Total: ↑↓{{ .UpDown }} / {{ .Total }}\r\n"
"TGUser" = "👤 Telegram User: {{ .TelegramID }}\r\n"
"exhaustedMsg" = "🚨 Exhausted {{ .Type }}:\r\n"
"exhaustedCount" = "🚨 Exhausted {{ .Type }} count:\r\n"
"onlinesCount" = "🌐 Online Clients: {{ .Count }}\r\n"
"disabled" = "🛑 Disabled: {{ .Disabled }}\r\n"
"depleteSoon" = "🔜 Deplete Soon: {{ .Deplete }}\r\n\r\n"
"backupTime" = "🗄 Backup Time: {{ .Time }}\r\n"
"refreshedOn" = "\r\n📋🔄 Refreshed On: {{ .Time }}\r\n\r\n"
"yes" = "✅ Yes"
"no" = "❌ No"

"received_id" = "🔑📥 ID updated."
"received_password" = "🔑📥 Password updated."
"received_email" = "📧📥 Email updated."
"received_comment" = "💬📥 Comment updated."
"id_prompt" = "🔑 Default ID: {{ .ClientId }}\n\nEnter your id."
"pass_prompt" = "🔑 Default Password: {{ .ClientPassword }}\n\nEnter your password."
"email_prompt" = "📧 Default Email: {{ .ClientEmail }}\n\nEnter your email."
"comment_prompt" = "💬 Default Comment: {{ .ClientComment }}\n\nEnter your Comment."
"inbound_client_data_id" = "🔄 Inbound: {{ .InboundRemark }}\n\n🔑 ID: {{ .ClientId }}\n📧 Email: {{ .ClientEmail }}\n📊 Traffic: {{ .ClientTraffic }}\n📅 Expire Date: {{ .ClientExp }}\n🌐 IP Limit: {{ .IpLimit }}\n💬 Comment: {{ .ClientComment }}\n\nYou can add the client to inbound now!"
"inbound_client_data_pass" = "🔄 Inbound: {{ .InboundRemark }}\n\n🔑 Password: {{ .ClientPass }}\n📧 Email: {{ .ClientEmail }}\n📊 Traffic: {{ .ClientTraffic }}\n📅 Expire Date: {{ .ClientExp }}\n🌐 IP Limit: {{ .IpLimit }}\n💬 Comment: {{ .ClientComment }}\n\nYou can add the client to inbound now!"
"cancel" = "❌ Process Canceled! \n\nYou can /start again anytime. 🔄"
"error_add_client"  = "⚠️ Error:\n\n {{ .error }}"
"using_default_value"  = "Okay, I'll stick with the default value. 😊"
"incorrect_input" ="Your input is not valid.\nThe phrases should be continuous without spaces.\nCorrect example: aaaaaa\nIncorrect example: aaa aaa 🚫"
"AreYouSure" = "Are you sure? 🤔"
"SuccessResetTraffic" = "📧 Email: {{ .ClientEmail }}\n🏁 Result: ✅ Success"
"FailedResetTraffic" = "📧 Email: {{ .ClientEmail }}\n🏁 Result: ❌ Failed \n\n🛠️ Error: [ {{ .ErrorMessage }} ]"
"FinishProcess" = "🔚 Traffic reset process finished for all clients."


[tgbot.buttons]
"closeKeyboard" = "❌ Close Keyboard"
"cancel" = "❌ Cancel"
"cancelReset" = "❌ Cancel Reset"
"cancelIpLimit" = "❌ Cancel IP Limit"
"confirmResetTraffic" = "✅ Confirm Reset Traffic?"
"confirmClearIps" = "✅ Confirm Clear IPs?"
"confirmRemoveTGUser" = "✅ Confirm Remove Telegram User?"
"confirmToggle" = "✅ Confirm Enable/Disable User?"
"dbBackup" = "Get DB Backup"
"serverUsage" = "Server Usage"
"getInbounds" = "Get Inbounds"
"depleteSoon" = "Deplete Soon"
"clientUsage" = "Get Usage"
"onlines" = "Online Clients"
"commands" = "Commands"
"refresh" = "🔄 Refresh"
"clearIPs" = "❌ Clear IPs"
"removeTGUser" = "❌ Remove Telegram User"
"selectTGUser" = "👤 Select Telegram User"
"selectOneTGUser" = "👤 Select a Telegram User:"
"resetTraffic" = "📈 Reset Traffic"
"resetExpire" = "📅 Change Expiry Date"
"ipLog" = "🔢 IP Log"
"ipLimit" = "🔢 IP Limit"
"setTGUser" = "👤 Set Telegram User"
"toggle" = "🔘 Enable / Disable"
"custom" = "🔢 Custom"
"confirmNumber" = "✅ Confirm: {{ .Num }}"
"confirmNumberAdd" = "✅ Confirm adding: {{ .Num }}"
"limitTraffic" = "🚧 Traffic Limit"
"getBanLogs" = "Get Ban Logs"
"allClients" = "All Clients"

"addClient" = "Add Client"
"submitDisable" = "Submit As Disable ☑️"
"submitEnable" = "Submit As Enable ✅"
"use_default" = "🏷️ Use default"
"change_id" = "⚙️🔑 ID"
"change_password" = "⚙️🔑 Password"
"change_email" = "⚙️📧 Email"
"change_comment" = "⚙️💬 Comment"
"ResetAllTraffics" = "Reset All Traffics"
"SortedTrafficUsageReport" = "Sorted Traffic Usage Report"

[tgbot.answers]
"successfulOperation" = "✅ Operation successful!"
"errorOperation" = "❗ Error in operation."
"getInboundsFailed" = "❌ Failed to get inbounds."
"getClientsFailed" = "❌ Failed to get clients."
"canceled" = "❌ {{ .Email }}: Operation canceled."
"clientRefreshSuccess" = "✅ {{ .Email }}: Client refreshed successfully."
"IpRefreshSuccess" = "✅ {{ .Email }}: IPs refreshed successfully."
"TGIdRefreshSuccess" = "✅ {{ .Email }}: Client's Telegram User refreshed successfully."
"resetTrafficSuccess" = "✅ {{ .Email }}: Traffic reset successfully."
"setTrafficLimitSuccess" = "✅ {{ .Email }}: Traffic limit saved successfully."
"expireResetSuccess" = "✅ {{ .Email }}: Expire days reset successfully."
"resetIpSuccess" = "✅ {{ .Email }}: IP limit {{ .Count }} saved successfully."
"clearIpSuccess" = "✅ {{ .Email }}: IPs cleared successfully."
"getIpLog" = "✅ {{ .Email }}: Get IP Log."
"getUserInfo" = "✅ {{ .Email }}: Get Telegram User Info."
"removedTGUserSuccess" = "✅ {{ .Email }}: Telegram User removed successfully."
"enableSuccess" = "✅ {{ .Email }}: Enabled successfully."
"disableSuccess" = "✅ {{ .Email }}: Disabled successfully."
"askToAddUserId" = "Your configuration is not found!\r\nPlease ask your admin to use your Telegram ChatID in your configuration(s).\r\n\r\nYour ChatID: <code>{{ .TgUserID }}</code>"
"chooseClient" = "Choose a Client for Inbound {{ .Inbound }}"
"chooseInbound" = "Choose an Inbound"
