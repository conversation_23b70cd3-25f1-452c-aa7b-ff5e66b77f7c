{{define "form/inbound"}}
<!-- base -->
<a-form :colon="false" :label-col="{ md: {span:8} }" :wrapper-col="{ md: {span:14} }">
    <a-form-item label='{{ i18n "enable" }}'>
        <a-switch v-model="dbInbound.enable"></a-switch>
    </a-form-item>
    <a-form-item label='{{ i18n "remark" }}'>
        <a-input v-model.trim="dbInbound.remark"></a-input>
    </a-form-item>

    <a-form-item label='{{ i18n "protocol" }}'>
        <a-select v-model="inbound.protocol" :disabled="isEdit" :dropdown-class-name="themeSwitcher.currentTheme">
            <a-select-option v-for="p in Protocols" :key="p" :value="p">[[ p ]]</a-select-option>
        </a-select>
    </a-form-item>

    <a-form-item>
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    <span>{{ i18n "pages.inbounds.monitorDesc" }}</span>
                </template>
                {{ i18n "monitor" }}
                <a-icon type="question-circle"></a-icon>
            </a-tooltip>
        </template>
        <a-input v-model.trim="inbound.listen"></a-input>
    </a-form-item>

    <a-form-item label='{{ i18n "pages.inbounds.port" }}'>
        <a-input-number v-model.number="inbound.port" :min="1" :max="65531"></a-input-number>
    </a-form-item>

    <a-form-item>
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    0 <span>{{ i18n "pages.inbounds.meansNoLimit" }}</span>
                </template>
                {{ i18n "pages.inbounds.totalFlow" }}
                <a-icon type="question-circle"></a-icon>
            </a-tooltip>
        </template>
        <a-input-number v-model.number="dbInbound.totalGB" :min="0"></a-input-number>
    </a-form-item>

    <a-form-item>
        <template slot="label">
            <a-tooltip>
                <template slot="title">
                    <span>{{ i18n "pages.inbounds.leaveBlankToNeverExpire" }}</span>
                </template>
                {{ i18n "pages.inbounds.expireDate" }}
                <a-icon type="question-circle"></a-icon>
            </a-tooltip>
        </template>
        <a-date-picker :style="{ width: '100%' }" v-if="datepicker == 'gregorian'" :show-time="{ format: 'HH:mm:ss' }"
            format="YYYY-MM-DD HH:mm:ss" :dropdown-class-name="themeSwitcher.currentTheme"
            v-model="dbInbound._expiryTime"></a-date-picker>
        <a-persian-datepicker v-else placeholder='{{ i18n "pages.settings.datepickerPlaceholder" }}'
            value="dbInbound._expiryTime" v-model="dbInbound._expiryTime">
        </a-persian-datepicker>
    </a-form-item>
</a-form>

<!-- vmess settings -->
<template v-if="inbound.protocol === Protocols.VMESS">
    {{template "form/vmess"}}
</template>

<!-- vless settings -->
<template v-if="inbound.protocol === Protocols.VLESS">
    {{template "form/vless"}}
</template>

<!-- trojan settings -->
<template v-if="inbound.protocol === Protocols.TROJAN">
    {{template "form/trojan"}}
</template>

<!-- shadowsocks -->
<template v-if="inbound.protocol === Protocols.SHADOWSOCKS">
    {{template "form/shadowsocks"}}
</template>

<!-- dokodemo-door -->
<template v-if="inbound.protocol === Protocols.DOKODEMO">
    {{template "form/dokodemo"}}
</template>

<!-- socks -->
<template v-if="inbound.protocol === Protocols.SOCKS">
    {{template "form/socks"}}
</template>

<!-- http -->
<template v-if="inbound.protocol === Protocols.HTTP">
    {{template "form/http"}}
</template>

<!-- wireguard -->
<template v-if="inbound.protocol === Protocols.WIREGUARD">
    {{template "form/wireguard"}}
</template>

<!-- stream settings -->
<template v-if="inbound.canEnableStream()">
    {{template "form/streamSettings"}}
    {{template "form/externalProxy" }}
</template>

<!-- tls settings -->
<template v-if="inbound.canEnableTls()">
    {{template "form/tlsSettings"}}
</template>

<!-- sniffing -->
<a-collapse>
    <a-collapse-panel header='Sniffing'>
        {{template "form/sniffing"}}
    </a-collapse-panel>
</a-collapse>

<!-- allocate -->
<!-- Temporarily disabled until we accepts range for port allocation
<a-collapse>
    <a-collapse-panel header='Allocate'>
        {{template "form/allocate"}}
    </a-collapse-panel>
</a-collapse>
-->

{{end}}
